package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.ProductIdDTO;
import com.sankuai.general.product.query.center.client.dto.*;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.dto.detail.DealGroupDetailDTO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import java.util.List;
/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:13
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class ProductBaseInfo extends FetcherReturnValueDTO {
    /**
     * 商品Id
     */
    private final ProductIdDTO productIdDTO;
    /**
     * 商品基础信息
     */
    private final DealGroupBasicDTO basic;
    /**
     * 商品图像信息
     */
    private final DealGroupImageDTO image;
    /**
     * 团购bg/bu信息
     */
    private final DealGroupBgBuDTO bgBu;
    /**
     * 团购频道信息
     */
    private final DealGroupChannelDTO channel;
    /**
     * 规则信息
     */
    private final DealGroupRuleDTO rule;
    /**
     * 展示门店信息
     */
    private final DealGroupDisplayShopDTO displayShopInfo;
    /**
     * 标签信息
     */
    private final List<DealGroupTagDTO> tags;
    /**
     * 区域信息
     */
    private final List<DealGroupRegionDTO> regions;
    /**
     * 价格信息
     */
    private final PriceDTO price;
    /**
     * 售卖渠道聚合信息
     */
    private final SaleChannelAggregationDTO saleChannelAggregation;
    /**
     * 团购属性信息
     */
    private List<AttrDTO> attrs;
    /**
     * 详情信息
     */
    private DealGroupDetailDTO detail;
    /**
     * deal信息
     */
    private final List<DealGroupDealDTO> deals;

    /**
     * 元数据信息
     */
    private MetaObjectInfoDTO metaObjectInfo;

    public ProductBaseInfo(final ProductTypeEnum productType,
                           final DealGroupDTO dealGroupDTO) {
        if (productType == ProductTypeEnum.DEAL) {
            this.productIdDTO =
                    new ProductIdDTO(
                            productType, dealGroupDTO.getDpDealGroupId(), dealGroupDTO.getMtDealGroupId()
                    );
        } else if (productType == ProductTypeEnum.RESERVE) {
            this.productIdDTO =
                    new ProductIdDTO(
                            productType, dealGroupDTO.getBizProductId(), dealGroupDTO.getBizProductId()
                    );
        } else {
            throw new IllegalArgumentException("暂不支持该商品类型:" + productType.getDesc());
        }
        this.basic = dealGroupDTO.getBasic();
        this.image = dealGroupDTO.getImage();
        this.bgBu = dealGroupDTO.getBgBu();
        this.channel = dealGroupDTO.getChannel();
        this.rule = dealGroupDTO.getRule();
        this.displayShopInfo = dealGroupDTO.getDisplayShopInfo();
        this.tags = dealGroupDTO.getTags();
        this.regions = dealGroupDTO.getRegions();
        this.price = dealGroupDTO.getPrice();
        this.saleChannelAggregation = dealGroupDTO.getSaleChannelAggregation();
        this.deals = dealGroupDTO.getDeals();
        this.attrs = dealGroupDTO.getAttrs();
        this.detail = dealGroupDTO.getDetail();
        this.metaObjectInfo = dealGroupDTO.getMetaObjectInfo();
    }
}