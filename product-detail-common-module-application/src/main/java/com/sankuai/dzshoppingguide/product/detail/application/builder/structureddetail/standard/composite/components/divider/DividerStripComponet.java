package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.components.divider;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructContentModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.base.AbstractDetailComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.base.DetailComponentParam;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum.DETAIL_TYPE_DIVIDER_STRIP;

/**
 * 分割线组件
 */
public class DividerStripComponet extends AbstractDetailComponent {

    public DividerStripComponet() {
        super("分割线组件");
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DetailComponentParam param) {
        List<DealDetailStructuredDetailVO> resultList = Lists.newArrayList();
        // 分割线组件
        DealDetailStructuredDetailVO detailVO = DealDetailStructuredDetailVO.builder().type(DETAIL_TYPE_DIVIDER_STRIP.getType()).build();
        resultList.add(detailVO);
        return resultList;
    }

}
