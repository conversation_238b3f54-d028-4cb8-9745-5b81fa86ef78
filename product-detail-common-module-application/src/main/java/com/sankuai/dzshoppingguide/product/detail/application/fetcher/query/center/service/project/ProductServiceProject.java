package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:13
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class ProductServiceProject extends FetcherReturnValueDTO {

    /**
     * 团购服务项目信息
     */
    private final DealGroupServiceProjectDTO serviceProject;

    public ProductServiceProject(DealGroupServiceProjectDTO serviceProject) {
        this.serviceProject = serviceProject;
    }

}
