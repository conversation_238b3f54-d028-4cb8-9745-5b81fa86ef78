package com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.domain.cpv.ShopInfoFieldConfigService;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.dto.TypeHierarchyView;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.sankuai.sinai.data.api.service.MtPoiService;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/12 19:03
 */
@Fetcher(
        previousLayerDependencies = {ShopIdMapperFetcher.class}
)
@Slf4j
public class ShopInfoFetcher extends NormalFetcherContext<ShopInfo> {
    @RpcClient(remoteAppkey = "com.sankuai.sinai.data.query")
    private DpPoiService sinaiDpPoiService;

    @RpcClient(remoteAppkey = "com.sankuai.sinai.data.query")
    private MtPoiService sinaiMtPoiService;

    @Autowired
    private ShopInfoFieldConfigService shopInfoFieldConfigService;
    @Override
    protected CompletableFuture<ShopInfo> doFetch() {
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        if (shopIdMapper == null) {
            return CompletableFuture.completedFuture(null);
        }
        long dpShopId = shopIdMapper.getDpBestShopId();
        long mtShopId = shopIdMapper.getMtBestShopId();
        try {
            CompletableFuture<DpPoiDTO> dpShopCf = loadDpPoiDTO(dpShopId, shopInfoFieldConfigService.getDpShopFieldConfig());
            CompletableFuture<MtPoiDTO> mtShopCf = loadMtPoiDTO(mtShopId, shopInfoFieldConfigService.getMtShopFieldConfig());
            return CompletableFuture.allOf(dpShopCf, mtShopCf).thenApply(aVoid -> {
                MtPoiDTO mtPoiDTO = mtShopCf.join();
                List<Integer> backCateIdList = Optional.ofNullable(mtPoiDTO).map(MtPoiDTO::getTypeHierarchy)
                        .orElse(Lists.newArrayList()).stream().map(TypeHierarchyView::getId)
                        .collect(Collectors.toList());
                Collections.reverse(backCateIdList);
                return new ShopInfo(dpShopCf.join(), mtShopCf.join(), backCateIdList);
            });
        } catch (Exception e) {
            log.error("ShopInfoFetcher error,dpShopId:{},mtShopId:{}", dpShopId,mtShopId,e);
        }
        return CompletableFuture.completedFuture(null);
    }


    public CompletableFuture<DpPoiDTO> loadDpPoiDTO(long dpShopId, List<String> fields) {
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(Lists.newArrayList(dpShopId));
        dpPoiRequest.setFields(shopInfoFieldConfigService.getDpShopFieldConfig());

        try {
            return AthenaInf.getRpcCompletableFuture(sinaiDpPoiService.findShopsByShopIds(dpPoiRequest)).thenApply(res -> {
                if (res == null) {
                    return null;
                }
                return res.get(0);
            });
        } catch (Exception e) {
            log.error("ShopInfoFetcher loadDpPoiDTO error,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return CompletableFuture.completedFuture(null);
    }

    public CompletableFuture<MtPoiDTO> loadMtPoiDTO(long mtShopId, List<String> fields) {
        if (mtShopId <= 0 || CollectionUtils.isEmpty(fields) ) {
            return CompletableFuture.completedFuture(null);
        }

        try {
            return AthenaInf.getRpcCompletableFuture(sinaiMtPoiService.findPoisById(Lists.newArrayList(mtShopId),fields)).thenApply(res -> {
                if ( MapUtils.isNotEmpty(res)) {
                    return res.get(mtShopId);
                }
                return null;
            });
        } catch (Exception e) {
            log.error("ShopInfoFetcher loadMtPoiDTO,mtShopId:{}", mtShopId,e);
        }
        return CompletableFuture.completedFuture(null);
    }
}
