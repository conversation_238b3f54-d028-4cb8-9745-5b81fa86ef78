package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;

import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:13
 */
@Slf4j
@EqualsAndHashCode(callSuper = true)
public class ProductAttr extends FetcherReturnValueDTO {

    public ProductAttr(Map<String, AttrDTO> skuAttr) {
        this.skuAttr = skuAttr;
    }

    /**
     * attrName,attrValue
     */
    private final Map<String, AttrDTO> skuAttr;

    public Map<String, AttrDTO> getSkuAttr() {
        return skuAttr;
    }

    public List<AttrDTO> getSkuAttrList() {
        if ( MapUtils.isEmpty(skuAttr) || CollectionUtils.isEmpty(skuAttr.values())) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(skuAttr.values());
    }

    public Optional<AttrDTO> getProductAttr(String attrName) {
        return Optional.ofNullable(skuAttr).map(map -> map.get(attrName));
    }

    public AttrDTO safeGetSkuAttr(String attrName) {
        Optional<AttrDTO> attrOptional = getProductAttr(attrName);
        // 处理 Optional 为空的情况，例如返回 null 或抛出自定义异常
        return attrOptional.orElse(null);
    }

    public List<String> getSkuAttrValue(String attrName) {
        Optional<AttrDTO> optionalAttrDTO = getProductAttr(attrName);
        if (!optionalAttrDTO.isPresent()) {
            // 处理 Optional 为空的情况，例如返回 null 或抛出自定义异常
            return null;
        }
        AttrDTO attrDTO = optionalAttrDTO.get();
        // 兼容B端属性值为数组类型时的格式问题
        try {
            boolean singleValueArrayAttr = LionConfigUtils.isSingleValueArrayAttr(attrName);
            if (singleValueArrayAttr && isSingleValueArray(attrDTO.getValue())) {
                String firstElement = attrDTO.getValue().get(0);
                return JSON.parseArray(firstElement, String.class);
            }
        } catch (Exception e) {
            log.error("getSkuAttrValue error, attrName:{}, attrValue:{}", attrName, attrDTO.getValue(), e);
        }
        return attrDTO.getValue();
    }

    private boolean isSingleValueArray(List<String> values) {
        if (CollectionUtils.isEmpty(values) || values.size() > 1) {
            return false;
        }
        return values.get(0).startsWith("[") && values.get(0).endsWith("]");
    }

    public List<String> getSkuAttrValues(String attrName) {
        return getSkuAttrValue(attrName);
    }

    public String getSkuAttrValueJson(String attrName) {
        AttrDTO attrDTO = safeGetSkuAttr(attrName);
        if (attrDTO == null) {
            return null;
        }
        List<String> attrValue =  attrDTO.getValue();
        if (CollectionUtils.isEmpty(attrValue)) {
            return null;
        }
        return JSON.toJSONString(attrValue);
    }

    public String getSkuAttrFirstValue(String attrName) {
        AttrDTO attrDTO = safeGetSkuAttr(attrName);
        if (attrDTO == null) {
            return null;
        }
        List<String> attrValue =  attrDTO.getValue();
        if (CollectionUtils.isEmpty(attrValue)) {
            return null;
        }
        return attrValue.get(0);
    }

    public String getSafeString(String key, String defaultValue) {
        String value = getSkuAttrFirstValue(key);
        if (value == null || value.isEmpty()) {
            return defaultValue;
        }
        return value;
    }
    public static String joinListByDelimiter(List<String> values, String delimiter){
        return SkuAttr.joinListByDelimiter(values, delimiter);
    }

    public String getProductAttrFirstValue(String attrName) {
        return getProductAttr(attrName)
                .map(AttrDTO::getValue)
                .filter(org.apache.commons.collections4.CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .orElse(null);
    }

}
