package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: created by hang.yu on 2023/9/27 16:17
 */
@Getter
@AllArgsConstructor
public enum BathServiceProjectEnum {

    /**
     * 浴资票(浴资票)
     */
    BATH_TICKET_1(20413636L, "浴资票"),

    /**
     * 服务费(浴资票)
     */
    SERVICE_FEE_1(20418466L, "服务费"),

    /**
     * 自助餐(浴资票)
     */
    BUFFET(20417997L, "自助餐"),

    /**
     * 门票附赠服务(浴资票)
     */
    ADDITIONAL_SERVICE(24493794L, "附赠服务"),


    /**
     * 餐饮(店内服务)
     */
    FOOD_1(20409567L, "餐饮"),

    /**
     * 搓澡(店内服务)
     */
    SCRUB_1(20411838L, "搓澡"),

    /**
     * 按摩/足疗(店内服务)
     */
    MASSAGE_1(20417999L, "按摩/足疗"),

    /**
     * 玩乐(店内服务)
     */
    PLAY_1(20430032L, "玩乐"),

    /**
     * 美容spa(店内服务)
     */
    SPA_1(20422649L, "美容spa"),

    /**
     * 住宿休憩(店内服务)
     */
    ACCOMMODATION_1(20413638L, "住宿休憩"),


    /**
     * 浴资票(浴资票和店内服务)
     */
    BATH_TICKET_2(20413640L, "浴资票"),

    /**
     * 餐饮(浴资票和店内服务)
     */
    FOOD_2(20408599L, "餐饮"),

    /**
     * 搓澡(浴资票和店内服务)
     */
    SCRUB_2(20409577L, "搓澡"),

    /**
     * 服务费(浴资票和店内服务)
     */
    SERVICE_FEE_2(20413642L, "服务费"),

    /**
     * 美容spa(浴资票和店内服务)
     */
    SPA_2(20414722L, "美容spa"),

    /**
     * 按摩/足疗(浴资票和店内服务)
     */
    MASSAGE_2(20425002L, "按摩/足疗"),

    /**
     * 住宿休憩(浴资票和店内服务)
     */
    ACCOMMODATION_2(20425003L, "住宿休憩"),

    /**
     * 玩乐(浴资票和店内服务)
     */
    PLAY_2(20425004L, "玩乐");

    private final long cpvObjectId;

    private final String serviceProjectName;

    public static BathServiceProjectEnum getEnumByServiceProjectId(long cpvObjectId) {
        for (BathServiceProjectEnum value : BathServiceProjectEnum.values()) {
            if (value.getCpvObjectId() == cpvObjectId) {
                return value;
            }
        }
        return null;
    }

}
