package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.customer;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCustomerBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:12
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class
)
public class ProductCustomerFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        ProductCustomer> {

    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder
                .customer(DealGroupCustomerBuilder.builder().all())//客户
        ;
    }

    @Override
    protected FetcherResponse<ProductCustomer> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        ProductCustomer productCustomer = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getCustomer)
                .map(customer -> new ProductCustomer(
                        Optional.ofNullable(customer.getOriginCustomerId()).orElse(0L),
                        Optional.ofNullable(customer.getPlatformCustomerId()).orElse(0L)
                ))
                .orElse(null);
        return FetcherResponse.succeed(productCustomer);
    }
}
