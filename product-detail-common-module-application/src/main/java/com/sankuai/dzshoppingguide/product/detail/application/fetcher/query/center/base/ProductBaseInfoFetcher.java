package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import java.util.Date;
import java.util.Optional;
/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:12
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class
)
public class ProductBaseInfoFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        ProductBaseInfo> {
    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder
                .category(DealGroupCategoryBuilder.builder().all())//品类
                .channel(DealGroupChannelBuilder.builder().all())//团购频道信息
                .image(DealGroupImageBuilder.builder().all())//商品图片视频信息
                .region(DealGroupRegionBuilder.builder().all())//商品城市
                .dealGroupTag(DealGroupTagBuilder.builder().all())//标签
                .dealGroupPrice(DealGroupPriceBuilder.builder().all())//商品价格
                .displayShop(DealGroupDisplayShopBuilder.builder().all())//适用门店
                .rule(DealGroupRuleBuilder.builder()
                        .refundRule()
                        .buyRule()
                        .useRule(DealGroupUtils.convertDate2String(new Date())))//交易规则
                .dealGroupSaleChannelAggregation(DealGroupSaleChannelAggregationBuilder.builder().all())//售卖渠道
                .detail(DealGroupDetailBuilder.builder().all()) // 商品详情
                .metaObjectInfo(DealGroupMetaObjectInfoBuilder.builder().all()) // 商品版本元数据信息
        ;
    }

    @Override
    protected FetcherResponse<ProductBaseInfo> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        Optional<DealGroupDTO> dealGroupDTOOptional = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO);
        if (!dealGroupDTOOptional.isPresent()) {
            return FetcherResponse.succeed(null);//聚合查询失败已经报错了，这里无需再报错
        }
        DealGroupDTO dealGroupDTO = dealGroupDTOOptional.get();
        ProductBaseInfo productBaseInfo = new ProductBaseInfo(this.request.getProductTypeEnum(), dealGroupDTO);
        return FetcherResponse.succeed(productBaseInfo);
    }
}