package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.utils.SkuIdUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.cpv.SkuAttrConfigService;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:12
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class,
        fillRequestDependencies = {
                ProductCategoryFetcher.class
        }
)
public class SkuAttrFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        SkuAttr> {

    @Resource
    private SkuAttrConfigService skuAttrConfigService;

    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        Set<String> productAttrConfig = skuAttrConfigService.getSkuAttrConfig(
                request.getProductTypeEnum(), productCategory.getProductSecondCategoryId()
        );
        if (CollectionUtils.isNotEmpty(productAttrConfig)) {
            requestBuilder.attrsByKey(AttrSubjectEnum.DEAL, productAttrConfig);
        }
    }

    @Override
    protected FetcherResponse<SkuAttr> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        Map<Long, Map<String, AttrDTO>> attrMap = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getDeals)
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        sku -> SkuIdUtils.getSkuId(this.request.getProductTypeEnum(), sku),
                        sku -> {
                            List<AttrDTO> attrDTOS = Optional.of(sku).map(DealGroupDealDTO::getAttrs).orElse(new ArrayList<>());
                            return attrDTOS.stream().collect(Collectors.toMap(
                                    AttrDTO::getName,
                                    v -> v,
                                    (v1, v2) -> v1
                            ));
                        },
                        (v1, v2) -> v1
                ));
        return FetcherResponse.succeed(new SkuAttr(attrMap));
    }

}
