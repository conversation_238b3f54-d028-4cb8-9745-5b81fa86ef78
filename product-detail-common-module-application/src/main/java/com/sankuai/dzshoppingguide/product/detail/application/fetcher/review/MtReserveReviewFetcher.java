package com.sankuai.dzshoppingguide.product.detail.application.fetcher.review;

import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.QueryContext;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.mtreview.ReviewDTO;
import com.sankuai.beautycontent.beautylaunchapi.model.request.MtReviewPmfRequest;
import com.sankuai.beautycontent.beautylaunchapi.model.service.review.MtShopReviewPmfService;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: litengfei04
 * @Date: 2025/2/9
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class},
        timeout = 500
)
@Slf4j
public class MtReserveReviewFetcher extends NormalFetcherContext<MtReserveReviewReturnValue> {

    @MdpPigeonClient(url = "beauty.launch.service.review.MtShopReviewPmfService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private MtShopReviewPmfService mtShopReviewPmfService;

    @Override
    protected CompletableFuture<MtReserveReviewReturnValue> doFetch() {
        // 点评侧直接返回
        if (this.request.getClientTypeEnum().isDpClientType()) {
            return CompletableFuture.completedFuture(null);
        }
        MtReviewPmfRequest reviewPmfRequest = new MtReviewPmfRequest();
        reviewPmfRequest.setPageNo(1);
        reviewPmfRequest.setPageSize(2);
        reviewPmfRequest.setSceneId(1000);
        reviewPmfRequest.setPlatform(2);
        reviewPmfRequest.setProductId(request.getProductId());
        reviewPmfRequest.setShopIdLong(request.getPoiId());
        QueryContext queryContext = new QueryContext();
        queryContext.setH5(false);
        queryContext.setInApp(true);
        queryContext.setUserId(request.getUserId());
        queryContext.setDeviceId(request.getShepherdGatewayParam().getUnionid());
        queryContext.setClientVersion(request.getShepherdGatewayParam().getAppVersion());
        reviewPmfRequest.setQueryContext(queryContext);
        Map<String, String> extConditionMap = new HashMap<>();
        extConditionMap.put("requestType", "reviewModule");
        reviewPmfRequest.setExtConditionMap(extConditionMap);
        return queryModule(reviewPmfRequest)
                .thenApply(r -> {
                    if (r == null || !r.isSuccess() || r.getData() == null) {
                        return null;
                    }
                    MtReserveReviewReturnValue mtReserveReviewReturnValue = new MtReserveReviewReturnValue();
                    mtReserveReviewReturnValue.setData((ReviewDTO) r.getData());
                    return mtReserveReviewReturnValue;
                })
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "MtReserveReviewFetcher")
                            .putTag("method", "queryProductReviewModule")
                            .message(String.format("queryProductReviewModule error, request : %s", JsonCodec.encode(reviewPmfRequest))));
                    return null;
                });
    }

    private CompletableFuture<RemoteResponse> queryModule(MtReviewPmfRequest request) {
        CompletableFuture<RemoteResponse> future = PigeonCallbackUtils.setPigeonCallback(RemoteResponse.class);
        mtShopReviewPmfService.queryProductReviewModule(request);
        future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "MtReserveReviewFetcher")
                    .putTag("method", "queryProductReviewModule")
                    .message(String.format("queryProductReviewModule error, request : %s, error message:%s", JsonCodec.encodeWithUTF8(request), e)));
            return null;
        });
        return future;
    }

}