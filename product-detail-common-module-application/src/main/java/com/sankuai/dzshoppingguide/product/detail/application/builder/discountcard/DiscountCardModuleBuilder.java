package com.sankuai.dzshoppingguide.product.detail.application.builder.discountcard;

import com.dianping.cat.util.StringUtils;
import com.dianping.gm.marketing.member.card.api.dto.DiscountCardShelfDTO;
import com.dianping.gm.marketing.member.card.api.dto.membercard.CardTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.deal.DealDiscountCardFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.ReserveDiscountCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.ReserveDiscountCardFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.hold.status.CardHoldStatusFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.hold.status.CardHoldStatusResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.reserveinfo.ReserveInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.reserveinfo.ReserveQueryResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.shop.info.dto.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule.ShopScheduleTimeFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule.ShopScheduleTimeResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule.TechStockWhiteShopFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.tech.schedule.TechStockWhiteShopResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DateAndTimeUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealDiscountCardBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReservationPromoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.discountcard.vo.DiscountCardModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DiscountCardContents;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dztheme.massagebook.theme.res.*;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils.getMiddleReserveConfig;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/6 14:57
 */
@Builder(
        moduleKey = ModuleKeyConstants.DISCOUNT_CARD,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ReserveDiscountCardFetcher.class,
                ReserveInfoFetcher.class,
                CardHoldStatusFetcher.class,
                TechStockWhiteShopFetcher.class,
                ShopScheduleTimeFetcher.class,
                DealDiscountCardFetcher.class}
)
public class DiscountCardModuleBuilder extends BaseBuilder<DiscountCardModuleVO> {

    public static final String DEFAULT_PRICE_DESC = "起";

    @Override
    public DiscountCardModuleVO doBuild() {
        // 团祥折扣卡数据构建
        if (request.getProductTypeEnum() != ProductTypeEnum.RESERVE ) {
            return DealDiscountCardBuildUtils.doBuild(getDependencyResult(DealDiscountCardFetcher.class));
        }

        // 持卡状态信息
        CardHoldStatusResult cardHoldStatusResult = getDependencyResult(CardHoldStatusFetcher.class);
        ShopScheduleTimeResult shopScheduleTimeResult = getDependencyResult(ShopScheduleTimeFetcher.class);
        // 获取门店今日排班开始时间
        Long todayScheduleStartTime = Optional.ofNullable(shopScheduleTimeResult).map(ShopScheduleTimeResult::getTodayScheduleStartTime).orElse(0L);
        // 获取门店今日排班结束时间
        Long todayScheduleEndTime = Optional.ofNullable(shopScheduleTimeResult).map(ShopScheduleTimeResult::getTodayScheduleEndTime).orElse(0L);

        List<Integer> userHasCardTypeList = Optional.ofNullable(cardHoldStatusResult).map(CardHoldStatusResult::getCardHoldStatusDTO).map(CardHoldStatusDTO::getUserHoldCardTypeList).orElse(null);
        boolean userHoldCardStatus = CollectionUtils.isNotEmpty(userHasCardTypeList) && userHasCardTypeList.contains(CardTypeEnum.DISCOUNT_CARD.getCode());

        List<Integer> shopHasCardTypeList = Optional.ofNullable(cardHoldStatusResult).map(CardHoldStatusResult::getCardHoldStatusDTO).map(CardHoldStatusDTO::getShopHasCardTypeList).orElse(null);
        boolean shopHoldCardStatus = CollectionUtils.isNotEmpty(shopHasCardTypeList) && shopHasCardTypeList.contains(CardTypeEnum.DISCOUNT_CARD.getCode());

        TechStockWhiteShopResult isTechStockWhiteShop = getDependencyResult(TechStockWhiteShopFetcher.class);
        Boolean isThirtyTechStockShop = Optional.ofNullable(isTechStockWhiteShop).map(TechStockWhiteShopResult::isWhiteList).orElse(false);

        // 折扣卡信息
        DiscountCardModuleVO discountCardModuleVO = new DiscountCardModuleVO();
        ReserveDiscountCard discountCard = getDependencyResult(ReserveDiscountCardFetcher.class);
        if (discountCard == null || discountCard.getDiscountCard() == null) {
            return discountCardModuleVO;
        }

        long productId = request.getProductId();

        // 获取预订相关的信息
        ReserveQueryResult reserveQueryResult = getDependencyResult(ReserveInfoFetcher.class);
        List<ReserveProductDTO> reserveProductDTOS = Optional.ofNullable(reserveQueryResult).map(ReserveQueryResult::getProducts).orElse(
                Lists.newArrayList());
        if ( CollectionUtils.isEmpty(reserveProductDTOS)) {
         return discountCardModuleVO;
        }

        ReserveProductDTO reserveProductDTO = reserveProductDTOS.stream().filter(target -> target.getProductId() == productId).findFirst().orElse(null);

        List<ReserveProductItemDTO> reserveProductItemDTOS = Optional.ofNullable(reserveProductDTO).map(ReserveProductDTO::getProductItems).orElse(Lists.newArrayList());

        ReserveProductItemDTO reserveProductItemDTO = reserveProductItemDTOS.stream().findFirst().orElse(null);

        if (reserveProductItemDTO == null) {
            return discountCardModuleVO;
        }

        ProductItemM productItemM = buildProductItem(reserveProductItemDTO);

        List<PeriodPriceM> periodPriceMS = buildPeriodPriceM(productItemM, todayScheduleStartTime, todayScheduleEndTime, isThirtyTechStockShop, userHoldCardStatus);
        productItemM.setPeriodPriceM(periodPriceMS);

        boolean isPeriodFuturePromoCheaper = ReservationPromoUtils.isPeriodFuturePromoCheaper(productItemM, userHoldCardStatus, shopHoldCardStatus);

        // 如果折扣卡信息为空 or 会员优惠<非会员优惠 or 有会员 则不展示折扣卡横幅
        if (discountCard.getDiscountCard() == null || userHoldCardStatus || !isPeriodFuturePromoCheaper) {
            return discountCardModuleVO;
        }
        return buildDiscountCardModuleVO(discountCard.getDiscountCard(), productItemM);
    }

    private DiscountCardModuleVO buildDiscountCardModuleVO(DiscountCardShelfDTO cardInfo, ProductItemM productItemM) {
        List<DiscountCardContents> contents = buildDiscountCardContents(cardInfo, productItemM);
        if (CollectionUtils.isEmpty(contents)) {
            return null;
        }
        DiscountCardModuleVO discountCardModuleVO = new DiscountCardModuleVO();
        discountCardModuleVO.setPrefixIcon("https://p0.meituan.net/dealproduct/18acc242c09a2e2c1ad1abee202ed3521601.png");
        discountCardModuleVO.setSuffixIcon("https://p0.meituan.net/dealproduct/9385c3035492d55b7354ce5bbdea7009380.png");
        discountCardModuleVO.setBackgroundColor("#FFEDDE");
        discountCardModuleVO.setContents(contents);
        discountCardModuleVO.setJumpUrl(cardInfo.getLinkUrl());
        return discountCardModuleVO;
    }

    private List<DiscountCardContents> buildDiscountCardContents(DiscountCardShelfDTO cardInfo, ProductItemM productItemM) {
        String saveAmount = getSaveAmount(productItemM);
        String discount = cardInfo.getDiscountValue() == null ? null : cardInfo.getDiscountValue().stripTrailingZeros().toPlainString();
        if ( com.dianping.cat.util.StringUtils.isBlank(saveAmount) || StringUtils.isBlank(discount)) {
            return null;
        }
        return buildContents(discount, saveAmount);
    }

    private String getSaveAmount(ProductItemM productItemM) {
        BigDecimal periodPromoLowestSalePrice = ReservationPromoUtils.getPeriodPromoLowestSalePrice(productItemM);
        BigDecimal periodFuturePromoLowestSalePrice = ReservationPromoUtils.getPeriodFuturePromoLowestSalePrice(productItemM);
        if (periodPromoLowestSalePrice == null || periodFuturePromoLowestSalePrice == null) {
            return "";
        }
        BigDecimal saveMoney = periodPromoLowestSalePrice.subtract(periodFuturePromoLowestSalePrice);
        if (saveMoney.compareTo(BigDecimal.ZERO) > 0) {
            return saveMoney.stripTrailingZeros().toPlainString();
        }
        return "";
    }

    private List<DiscountCardContents> buildContents(String discount, String saveMoney) {
        DiscountCardContents content1 = new DiscountCardContents();
        content1.setContent("开通会员，本单");
        content1.setFontColor("#8e3c12");
        content1.setFontSize(12);

        DiscountCardContents content2 = new DiscountCardContents();
        content2.setContent(discount);
        content2.setFontColor("#FF4B10");
        content2.setFontSize(12);


        DiscountCardContents content3 = new DiscountCardContents();
        content3.setContent("折，多省");
        content3.setFontColor("#8e3c12");
        content3.setFontSize(12);

        DiscountCardContents content4 = new DiscountCardContents();
        content4.setContent(saveMoney);
        content4.setFontColor("#FF4B10");
        content4.setFontSize(12);

        DiscountCardContents content5 = new DiscountCardContents();
        content5.setContent("元");
        content5.setFontColor("#8e3c12");
        content5.setFontSize(12);

        return Lists.newArrayList(content1, content2, content3, content4, content5);
    }

    /**
     * 1、三方技师预订库存粒度：15分钟
     * 2、平台和普通三方预订库存粒度
     *  20241202之前 30分钟
     *  后续终态是 15分钟。该接口根据Lion开关决定是否将30切换成15。开关开启后存在过渡期，有些门店是30分钟，有些门店是15分钟。通过实际库存返回结果里的stockGranularity参数来判断
     */
    public int getStockGranularity(boolean isTechStockShop, ProductItemM productItemM) {
        if (isTechStockShop) {
            return 15;
        }
        MidReserveConfig bean = getMiddleReserveConfig();
        boolean stockGranularitySwitch = bean != null && bean.isStockGranularitySwitch();
        if (!stockGranularitySwitch) {
            return 30;
        }
        if (productItemM == null || org.apache.commons.collections.CollectionUtils.isEmpty(productItemM.getBookPeriodStocks())) {
            return 30;
        }
        PeriodStockM periodStockM = productItemM.getBookPeriodStocks().get(0);
        int actual = Optional.ofNullable(periodStockM).map(PeriodStockM::getStockGranularity).orElse(30);
        if (30 == actual || actual == 0) {
            return 30;
        }
        if (inMidWhiteList()) {
            return actual;
        }
        // 如果供应链库存已经切换到15分钟粒度了，那由实验决定库存展示粒度。命中实验组15；否则30
        // 实验95%的结果是false https://nibab.sankuai.com/ab/experimentList/detail?expId=EXP2024112700017&fromPage=auditCenter
        // if (hitMidExp(ctx)) {
        //     return 15;
        // }
        return 30;
    }

    private static boolean inMidWhiteList() {
        MidReserveConfig middleReserveConfig = getMiddleReserveConfig();
        if (middleReserveConfig == null || org.apache.commons.collections.CollectionUtils.isEmpty(middleReserveConfig.getWhiteList())) {
            return false;
        }
        // 这个地方的unionId暂时获取不到
        // String unionId = ParamsUtil.getStringSafely(ctx, PmfConstants.Params.unionId);
        // if ( StringUtils.isBlank(unionId)) {
        //     return false;
        // }
        // return middleReserveConfig.getWhiteList().contains(unionId);
        return true;
    }

    private List<PeriodPriceM> buildPeriodPriceM(ProductItemM productItemM, long shopTodayScheduleStartTime,long shopTodayScheduleEndTime,boolean techStockWhiteShop,boolean userHoldCard) {

        // 获取门店今日排班开始时间 shopTodayScheduleStartTime
        // 获取门店今日排班结束时间 shopTodayScheduleEndTime

        int stockGranularity = getStockGranularity(techStockWhiteShop,productItemM);

        // 获取当天所有时段，不带跨天逻辑和基准价, 目前可能会出现时段基准价相同的情况
        List<PeriodPriceM> allPeriod = getAllPeriod(productItemM, shopTodayScheduleStartTime, shopTodayScheduleEndTime,
                stockGranularity);

        // 合并基准价相同的时段
        allPeriod = mergePeriodBySameOriginSalePrice(allPeriod, productItemM);

        // 填充全时段库存信息+最早可订时间+跨天信息
        paddingStockInfo(allPeriod, productItemM.getBookPeriodStocks(), stockGranularity);

        // 填充时段价格信息
        paddingPeriodPriceModel(allPeriod, productItemM, userHoldCard, stockGranularity);

        return allPeriod;
    }

    private void paddingPeriodPriceModel(List<PeriodPriceM> allPeriod, ProductItemM productItemM, boolean userHoldCard, int stockGranularity) {
        if (CollectionUtils.isEmpty(allPeriod)) {
            return;
        }

        String marketPrice = productItemM.getMarketPrice();
        // 当天时间点映射的基准价格Map，同一个时段内的基准价相同
        Map<Long, BigDecimal> time2OriginPriceMap = buildTime2OriginPriceMap(productItemM.getDayDetailPrices());
        // 当天时间点映射的时段优惠模型，同一个时段内容的优惠价可能不同
        Map<Long, PromoPriceM> time2PeriodPromoPriceMap = buildTime2PeriodPromoPriceMap(productItemM.getSkuPromos());
        // 当天时间点映射的时段未来优惠模型，同一个时段内容的优惠价可能不同
        Map<Long, PromoPriceM> time2PeriodFuturePromoPriceMap = buildTime2PeriodFuturePromoPriceMap(
                productItemM.getFutureSkuPromos());

        for (PeriodPriceM periodPriceM : allPeriod) {
            long periodStartTime = periodPriceM.getPeriodStartTime();
            // 库存时间和价格时间不匹配，将库存时间转化到【小于当前时间】且【能整除30分钟】的时间点上
            long effectiveTime = DateAndTimeUtils.convertToNearestSmallerLongTime(periodStartTime, 30);
            periodPriceM.setPeriodMarketPrice(marketPrice);
            periodPriceM.setPeriodOriginSalePrice(buildPeriodOriginSalePrice(time2OriginPriceMap.get(effectiveTime)));
            periodPriceM.setPeriodPromoPrices(
                    buildPeriodPromoPrices(periodPriceM, time2PeriodPromoPriceMap, stockGranularity));
            periodPriceM.setPeriodFuturePromoPrices(
                    buildPeriodPromoPrices(periodPriceM, time2PeriodFuturePromoPriceMap, stockGranularity));
            // 填充到手价+到手价描述
            paddingPeriodSalePriceInfo(periodPriceM, userHoldCard);
        }
    }

    private void paddingPeriodSalePriceInfo(PeriodPriceM periodPriceM, boolean userHoldCard) {
        String periodOriginSalePrice = periodPriceM.getPeriodOriginSalePrice();
        PeriodPromoPriceM periodPromoPrices = periodPriceM.getPeriodPromoPrices();
        PeriodPromoPriceM periodFuturePromoPrices = periodPriceM.getPeriodFuturePromoPrices();
        if (userHoldCard && periodFuturePromoPrices != null && periodFuturePromoPrices.getPeriodPromoPrice() != null) {
            BigDecimal futurePeriodPromoPrice = periodFuturePromoPrices.getPeriodPromoPrice();
            if (periodPromoPrices != null && periodPromoPrices.getPeriodPromoPrice() != null) {
                BigDecimal periodPromoPrice = periodPromoPrices.getPeriodPromoPrice();
                if (futurePeriodPromoPrice.compareTo(periodPromoPrice) >= 0) {
                    periodPriceM.setPeriodSalePrice(
                            periodPromoPrices.getPeriodPromoPrice().stripTrailingZeros().toPlainString());
                    periodPriceM.setPeriodSalePriceDesc(periodPromoPrices.getPeriodPromoPriceDesc());
                    return;
                }
            }
            periodPriceM.setPeriodSalePrice(futurePeriodPromoPrice.stripTrailingZeros().toPlainString());
            periodPriceM.setPeriodSalePriceDesc(periodFuturePromoPrices.getPeriodPromoPriceDesc());
            return;
        }
        if (!userHoldCard && periodPromoPrices != null && periodPromoPrices.getPeriodPromoPrice() != null) {
            periodPriceM
                    .setPeriodSalePrice(periodPromoPrices.getPeriodPromoPrice().stripTrailingZeros().toPlainString());
            periodPriceM.setPeriodSalePriceDesc(periodPromoPrices.getPeriodPromoPriceDesc());
            return;
        }
        periodPriceM.setPeriodSalePrice(periodOriginSalePrice);
    }

    private PeriodPromoPriceM buildPeriodPromoPrices(PeriodPriceM periodPriceM, Map<Long, PromoPriceM> time2PriceMap,
                                                     int stockGranularity) {
        if ( org.apache.commons.collections4.MapUtils.isEmpty(time2PriceMap)) {
            return null;
        }

        HashMap<String, Object> lowestPromoPriceInfo = findLowestPromoPriceInfo(periodPriceM, time2PriceMap,
                stockGranularity);
        if ( org.apache.commons.collections4.MapUtils.isEmpty(lowestPromoPriceInfo)) {
            return null;
        }
        Long lowestPromoPriceTime = (Long)lowestPromoPriceInfo.get("lowestPromoPriceTime");
        Boolean hasDiffPromoPrice = (Boolean)lowestPromoPriceInfo.get("hasDiffPromoPrice");
        PromoPriceM promoPriceM = time2PriceMap.get(lowestPromoPriceTime);

        PeriodPromoPriceM periodPromoPriceM = new PeriodPromoPriceM();
        periodPromoPriceM.setPeriodStartTime(periodPriceM.getPeriodStartTime());
        periodPromoPriceM.setPeriodPromoPrice(promoPriceM.getPromoPrice());
        periodPromoPriceM.setPeriodPromoAmount(promoPriceM.getPromoAmount());
        periodPromoPriceM.setPeriodPromoTag(promoPriceM.getPromoTag());
        periodPromoPriceM.setPeriodShortPromoTag(promoPriceM.getShortPromoTag());
        periodPromoPriceM.setPeriodPromoTagType(promoPriceM.getPromoTagType());
        periodPromoPriceM.setPeriodDiscount(promoPriceM.getDiscount());
        periodPromoPriceM.setPeriodIcon(promoPriceM.getIcon());
        periodPromoPriceM.setPeriodPromoPriceDesc(hasDiffPromoPrice ? DEFAULT_PRICE_DESC : "");
        periodPromoPriceM.setPromoItemList(promoPriceM.getPromoItemList());
        periodPromoPriceM.setTotalPromoPrice(promoPriceM.getTotalPromoPrice());
        periodPromoPriceM.setExtendDisplayInfo(promoPriceM.getExtendDisplayInfo());
        periodPromoPriceM.setPricePromoInfoMap(promoPriceM.getPricePromoInfoMap());
        return periodPromoPriceM;
    }

    private HashMap<String, Object> findLowestPromoPriceInfo(PeriodPriceM periodPriceM,
                                                             Map<Long, PromoPriceM> time2PeriodPromoPriceMap, int stockGranularity) {
        long begin = periodPriceM.getPeriodStartTime();
        long end = periodPriceM.getPeriodEndTime();
        long interval = DateAndTimeUtils.convertToLongTime(stockGranularity, Calendar.MINUTE);
        boolean hasDiffPromoPrice = false;
        Long lowestPriceTime = null;
        BigDecimal lowestPrice = null;

        while (begin < end) {
            long effectiveTime = DateAndTimeUtils.convertToNearestSmallerLongTime(begin, 30);
            PromoPriceM promoPriceM = time2PeriodPromoPriceMap.get(effectiveTime);
            if (promoPriceM == null || promoPriceM.getPromoPrice() == null) {
                begin += interval;
                continue;
            }
            if (lowestPrice == null) {
                lowestPrice = promoPriceM.getPromoPrice();
                lowestPriceTime = begin;
            }
            if (promoPriceM.getPromoPrice().compareTo(lowestPrice) < 0) {
                hasDiffPromoPrice = true;
                lowestPriceTime = begin;
                lowestPrice = promoPriceM.getPromoPrice();
            }
            begin += interval;
        }

        HashMap<String, Object> res = new HashMap<>();
        if (lowestPrice == null) {
            return res;
        }
        res.put("lowestPromoPrice", lowestPrice);
        res.put("lowestPromoPriceTime", lowestPriceTime);
        res.put("hasDiffPromoPrice", hasDiffPromoPrice);
        return res;
    }

    private Map<Long, PromoPriceM> buildTime2PeriodFuturePromoPriceMap(List<PromoPriceM> futureSkuPromos) {
        if (CollectionUtils.isEmpty(futureSkuPromos)) {
            return new HashMap<>();
        }
        return futureSkuPromos.stream().collect(Collectors.toMap(PromoPriceM::getPriceTime, e -> e, (a, b) -> a));
    }

    private Map<Long, PromoPriceM> buildTime2PeriodPromoPriceMap(List<PromoPriceM> skuPromos) {
        if (CollectionUtils.isEmpty(skuPromos)) {
            return new HashMap<>();
        }
        return skuPromos.stream().collect(Collectors.toMap(PromoPriceM::getPriceTime, e -> e, (a, b) -> a));
    }

    private String buildPeriodOriginSalePrice(BigDecimal originSalePrice) {
        return originSalePrice == null ? "" : originSalePrice.stripTrailingZeros().toPlainString();
    }

    private void paddingStockInfo(List<PeriodPriceM> allPeriod, List<PeriodStockM> bookPeriodStocks, int stockGranularity) {
        if (CollectionUtils.isEmpty(allPeriod)) {
            return;
        }

        // 筹备数据
        // 用户访问时间
        long userAccessTime = new Date().getTime();
        // 时段库存信息
        Map<Long, Boolean> time2Available = CollectionUtils.isEmpty(bookPeriodStocks) ? new HashMap<>()
                : bookPeriodStocks.stream()
                .collect(Collectors.toMap(PeriodStockM::getBeginTime, e -> e.getStock() > 0, (a, b) -> a));
        long intervalLongTime = DateAndTimeUtils.convertToLongTime(stockGranularity, Calendar.MINUTE);
        long effectiveBookStartTime = DateAndTimeUtils.convertToNearestBigLongTime(userAccessTime, stockGranularity);
        long tomorrowZeroLongTime = DateAndTimeUtils.convertZeroLongTime(1);

        for (PeriodPriceM periodPriceM : allPeriod) {
            long periodStartTime = periodPriceM.getPeriodStartTime();
            long periodEndTime = periodPriceM.getPeriodEndTime();
            // 设置跨天信息
            periodPriceM.setAcrossDay(periodEndTime >= tomorrowZeroLongTime);
            if (periodStartTime <= effectiveBookStartTime && periodEndTime > effectiveBookStartTime) {
                long begin = effectiveBookStartTime;
                Long earliestBookTime = null;
                boolean canBook = false;
                while (begin < periodEndTime) {
                    if (Boolean.TRUE.equals(time2Available.get(begin))) {
                        earliestBookTime = begin;
                        canBook = true;
                        break;
                    }
                    begin += intervalLongTime;
                }
                periodPriceM.setEarliestBookTime(earliestBookTime);
                periodPriceM.setCanBook(canBook);
            } else if (effectiveBookStartTime < periodStartTime) {
                long begin = periodStartTime;
                Long earliestBookTime = null;
                boolean canBook = false;
                while (begin < periodEndTime) {
                    if (Boolean.TRUE.equals(time2Available.get(begin))) {
                        earliestBookTime = begin;
                        canBook = true;
                        break;
                    }
                    begin += intervalLongTime;
                }
                periodPriceM.setEarliestBookTime(earliestBookTime);
                periodPriceM.setCanBook(canBook);
            } else {
                periodPriceM.setEarliestBookTime(null);
                periodPriceM.setCanBook(false);
            }
        }
    }

    private List<PeriodPriceM> mergePeriodBySameOriginSalePrice(List<PeriodPriceM> allPeriod,
                                                                ProductItemM productItemM) {
        if (CollectionUtils.isEmpty(allPeriod) || productItemM == null) {
            return Lists.newArrayList();
        }

        Map<Long, BigDecimal> time2OriginPriceMap = buildTime2OriginPriceMap(productItemM.getDayDetailPrices());

        // 过滤掉没有基准价的异常时段
        allPeriod = allPeriod.stream().filter(e -> time2OriginPriceMap.get(e.getPeriodStartTime()) != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allPeriod)) {
            return Lists.newArrayList();
        }

        List<PeriodPriceM> res = new ArrayList<>();
        PeriodPriceM lastPeriod = allPeriod.get(0);
        for (int i = 1; i < allPeriod.size(); i++) {
            PeriodPriceM cur = allPeriod.get(i);
            if (checkSameOriginPrice(lastPeriod, cur, time2OriginPriceMap)) {
                lastPeriod = mergePeriod(lastPeriod, cur);
            } else {
                res.add(lastPeriod);
                lastPeriod = cur;
            }
        }
        res.add(lastPeriod);
        return res;
    }

    private boolean checkSameOriginPrice(PeriodPriceM last, PeriodPriceM cur,
                                         Map<Long, BigDecimal> time2OriginPriceMap) {
        BigDecimal lastPrice = time2OriginPriceMap.get(last.getPeriodStartTime());
        BigDecimal curPrice = time2OriginPriceMap.get(cur.getPeriodStartTime());
        return lastPrice.equals(curPrice);
    }

    private Map<Long, BigDecimal> buildTime2OriginPriceMap(List<DayDetailPriceM> dayDetailPrices) {
        if (CollectionUtils.isEmpty(dayDetailPrices)) {
            return Maps.newHashMap();
        }
        List<DayPriceInfoM> dayPrices = dayDetailPrices.stream().filter(Objects::nonNull).map(DayDetailPriceM::getDayPrices).filter(Objects::nonNull).flatMap(Collection::stream).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dayPrices)) {
            return Maps.newHashMap();
        }
        return dayPrices.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(DayPriceInfoM::getDateTime, DayPriceInfoM::getTimePrice, (a, b) -> a));
    }

    private PeriodPriceM mergePeriod(PeriodPriceM last, PeriodPriceM cur) {
        PeriodPriceM periodPriceM = new PeriodPriceM();
        periodPriceM.setPeriodStartTime(last.getPeriodStartTime());
        periodPriceM.setPeriodEndTime(cur.getPeriodEndTime());
        return periodPriceM;
    }

    private List<PeriodPriceM> getAllPeriod(ProductItemM productItemM, long shopTodayScheduleStartTime,
                                            long shopTodayScheduleEndTime, int stockGranularity) {
        // 获取足疗商家设置的优惠时段
        List<PeriodInfoM> periodM = productItemM.getPeriodM() == null ? Lists.newArrayList()
                : productItemM.getPeriodM().getPeriodInfoList();
        return partitionToday(periodM, shopTodayScheduleStartTime, shopTodayScheduleEndTime, stockGranularity);
    }

    private List<PeriodPriceM> partitionToday(List<PeriodInfoM> periodM, long begin, long end,
                                              int stockGranularity) {
        // 库存前闭后闭，结束时间+1个时段，技师库存打通场景15分钟一个片段，其它30分钟一个片段
        end += (long) stockGranularity * 60 * 1000;
        // 商家未设置优惠时段，全天只有1个时段
        if (CollectionUtils.isEmpty(periodM)) {
            return Lists.newArrayList(new PeriodPriceM(begin, end));
        }
        return processPartition(periodM, begin, end);
    }

    private List<PeriodPriceM> processPartition(List<PeriodInfoM> periodM, long start, long end) {
        // 获取所有的时间节点
        List<Long> allTimeNodes = getAllTimeNode(periodM, start, end);

        // 拼接时间段
        List<PeriodPriceM> res = new ArrayList<>();
        for (int i = 0; i < allTimeNodes.size() - 1; i++) {
            res.add(new PeriodPriceM(allTimeNodes.get(i), allTimeNodes.get(i + 1)));
        }
        return res;
    }

    private List<Long> getAllTimeNode(List<PeriodInfoM> periodM, long start, long end) {
        List<Long> timeNodes = new ArrayList<>();
        periodM.forEach(node -> {
            long periodStartTime = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(node.getPeriodStartTime(), 0);
            long periodEndTime = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(node.getPeriodEndTime(), 0);
            if (Boolean.TRUE.equals(node.getAcrossDay())) {
                periodStartTime = periodStartTime < periodEndTime ? DateAndTimeUtils.addDay(periodStartTime, 1)
                        : periodStartTime;
                periodEndTime = DateAndTimeUtils.addDay(periodEndTime, 1);
            }
            if (periodStartTime > start && periodStartTime < end) {
                timeNodes.add(periodStartTime);
            }
            if (periodEndTime > start && periodEndTime < end) {
                timeNodes.add(periodEndTime);
            }
        });
        timeNodes.add(start);
        timeNodes.add(end);
        // 去除重复时间点
        return timeNodes.stream().distinct().sorted().collect(Collectors.toList());
    }

    private ProductItemM buildProductItem(ReserveProductItemDTO item) {
        ProductItemM productItemM = new ProductItemM();
        productItemM.setProductId(item.getProductId());
        productItemM.setPeriodM(buildPeriodM(item.getPeriods()));
        productItemM.setOriginalSalePrice(item.getOriginalSalePrice());
        productItemM.setDayDetailPrices(buildDayDetailPrices(item.getDayDetailPrices()));
        productItemM.setSkuPromos(buildSkuPromos(item.getSkuPromos()));
        productItemM.setFutureSkuPromos(buildSkuPromos(item.getFutureSkuPromos()));
        productItemM.setMarketPrice(buildItemMarketPrice(item.getMarketPrice()));
        productItemM.setBookPeriodStocks(buildBookPeriodStock(item.getDateStocks()));
        return productItemM;
    }

    private List<PeriodStockM> buildBookPeriodStock(List<DateStockDTO> dateStocks) {
        if ( org.apache.commons.collections.CollectionUtils.isEmpty(dateStocks)) {
            return Lists.newArrayList();
        }
        // 多天库存合1个List存储
        return dateStocks.stream().filter(e -> e != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(e.getPeriodStocks())).flatMap(e -> e.getPeriodStocks().stream()).map(this::buildPeriodStockM).collect(Collectors.toList());
    }

    private PeriodStockM buildPeriodStockM(PeriodStockDTO periodStockDTO) {
        PeriodStockM periodStockM = new PeriodStockM();
        periodStockM.setBeginTime(periodStockDTO.getBeginTime());
        periodStockM.setStock(periodStockDTO.getStock());
        periodStockM.setResourceIds(periodStockDTO.getResourceIds());
        periodStockM.setStockGranularity(periodStockDTO.getStockGranularity());
        return periodStockM;
    }

    private String buildItemMarketPrice(BigDecimal marketPrice) {
        return marketPrice == null ? null : marketPrice.stripTrailingZeros().toPlainString();
    }

    private List<PromoPriceM> buildSkuPromos(List<ReserveProductPromoDTO> skuPromos) {
        if ( org.apache.commons.collections.CollectionUtils.isEmpty(skuPromos)) {
            return null;
        }
        return skuPromos.stream().filter(Objects::nonNull).map(this::buildProductItemPromoPriceM).collect(Collectors.toList());
    }

    private PromoPriceM buildProductItemPromoPriceM(ReserveProductPromoDTO reserveProductPromoDTO) {
        PromoPriceM promoPriceM = new PromoPriceM();
        promoPriceM.setSkuId(reserveProductPromoDTO.getSkuId());
        promoPriceM.setPriceTime(reserveProductPromoDTO.getPriceTime());
        promoPriceM.setPromoPrice(reserveProductPromoDTO.getPromoPrice());
        promoPriceM.setBasePrice(reserveProductPromoDTO.getBasePrice());
        promoPriceM.setPromoAmount(reserveProductPromoDTO.getPromoAmount());
        promoPriceM.setPromoTag(reserveProductPromoDTO.getPromoTag());
        promoPriceM.setShortPromoTag(reserveProductPromoDTO.getShortPromoTag());
        promoPriceM.setPromoTagType(reserveProductPromoDTO.getPromoTagType());
        promoPriceM.setDiscount(reserveProductPromoDTO.getDiscount());
        promoPriceM.setIcon(reserveProductPromoDTO.getIcon());
        promoPriceM.setPricePromoInfoMap(buildPricePromoInfoMap(reserveProductPromoDTO.getPricePromoInfoMap()));
        promoPriceM.setExtendDisplayInfo(reserveProductPromoDTO.getExtendDisplayInfo());
        if (reserveProductPromoDTO.getPromoDetail() == null) {
            return promoPriceM;
        }
        promoPriceM.setTotalPromoPrice(reserveProductPromoDTO.getPromoDetail().getTotalPromoAmountValue());
        promoPriceM.setPromoItemList(buildPromoItemList(reserveProductPromoDTO.getPromoDetail().getPromoItems()));
        return promoPriceM;
    }

    private static Map<Integer, List<PromoItemM>> buildPricePromoInfoMap(Map<Integer, List<PromoItemDTO>> pricePromoInfoMap){
        if ( MapUtils.isEmpty(pricePromoInfoMap)){
            return null;
        }
        Map<Integer, List<PromoItemM>> result = Maps.newHashMap();
        for (Map.Entry<Integer, List<PromoItemDTO>> entry : pricePromoInfoMap.entrySet()) {
            Integer key = entry.getKey();
            List<PromoItemDTO> promoItemDTOList = entry.getValue();
            List<PromoItemM> promoItemList = buildPromoItemList(promoItemDTOList);
            if ( org.apache.commons.collections.CollectionUtils.isNotEmpty(promoItemList)){
                result.put(key, promoItemList);
            }
        }
        return result;
    }

    private static List<PromoItemM> buildPromoItemList(List<PromoItemDTO> promoItemDTOList) {
        if ( org.apache.commons.collections.CollectionUtils.isEmpty(promoItemDTOList)) {
            return null;
        }
        return promoItemDTOList.stream().map(promoItem -> {
            PromoItemM promoItemM = new PromoItemM();
            promoItemM.setPromoId(promoItem.getPromoId());
            promoItemM.setPromoTypeCode(promoItem.getPromoTypeCode());
            promoItemM.setDesc(promoItem.getPromoDesc());
            promoItemM.setPromoTag(promoItem.getPromoName());
            promoItemM.setPromoPrice(promoItem.getPromoAmount());
            promoItemM.setSourceType(promoItem.getSourceType());
            promoItemM.setPromoType(promoItem.getPromoName());
            promoItemM.setCanAssign(promoItem.isCanAssign());
            promoItemM.setNewUser(promoItem.isNewUser());
            promoItemM.setPromoNewOldIdentity(promoItem.getPromoIdentity());
            promoItemM.setPromoDiscount(promoItem.getPromoDiscount());
            promoItemM.setUsedPromoTypes(promoItem.getUsedPromoTypes());
            promoItemM.setPriceLimitDesc(promoItem.getPriceLimitDesc());
            promoItemM.setUseTimeDesc(promoItem.getUseTimeDesc());
            promoItemM.setIcon(promoItem.getPromoIcon());
            promoItemM.setCouponId(promoItem.getCouponId());
            promoItemM.setPromotionExplanatoryTags(promoItem.getPromotionExplanatoryTags());
            promoItemM.setPromotionOtherInfoMap(promoItem.getPromotionOtherInfoMap());
            promoItemM.setPromoItemTextM(buildPromoItemTextM(promoItem.getPromoItemTextDTO()));
            promoItemM.setAmount(promoItem.getAmount());
            promoItemM.setMinConsumptionAmount(promoItem.getMinConsumptionAmount());
            promoItemM.setCouponGroupId(promoItem.getCouponGroupId());
            promoItemM.setCouponTitle(promoItem.getCouponTitle());
            promoItemM.setStartTime(promoItem.getStartTime());
            promoItemM.setEndTime(promoItem.getEndTime());
            promoItemM.setPromotionDisplayTextMap(promoItem.getPromotionDisplayTextMap());
            return promoItemM;
        }).collect(Collectors.toList());
    }

    private static PromoItemTextM buildPromoItemTextM(PromoItemTextDTO promoItemTextDTO) {
        if (promoItemTextDTO == null) {
            return null;
        }
        PromoItemTextM promoItemTextM = new PromoItemTextM();
        promoItemTextM.setTitle(promoItemTextDTO.getTitle());
        promoItemTextM.setPromoName(promoItemTextDTO.getPromoName());
        promoItemTextM.setSubTitle(promoItemTextDTO.getSubTitle());
        promoItemTextM.setIcon(promoItemTextDTO.getIcon());
        promoItemTextM.setAtmosphereBarIcon(promoItemTextDTO.getAtmosphereBarIcon());
        promoItemTextM.setAtmosphereBarText(promoItemTextDTO.getAtmosphereBarText());
        promoItemTextM.setAtmosphereBarButtonText(promoItemTextDTO.getAtmosphereBarButtonText());
        promoItemTextM.setAtmosphereBarButtonUrl(promoItemTextDTO.getAtmosphereBarButtonUrl());
        promoItemTextM.setPromoStatusText(promoItemTextDTO.getPromoStatusText());
        promoItemTextM.setPromoDivideType(promoItemTextDTO.getPromoDivideType());
        promoItemTextM.setPromoDivideTypeDesc(promoItemTextDTO.getPromoDivideTypeDesc());
        return promoItemTextM;
    }

    private PeriodM buildPeriodM(List<PeriodDTO> periods) {
        if ( org.apache.commons.collections.CollectionUtils.isEmpty(periods)) {
            return null;
        }
        PeriodM periodM = new PeriodM();
        List<PeriodInfoM> periodInfoList = periods.stream().filter(Objects::nonNull).map(this::buildPeriodInfoM).collect(Collectors.toList());
        periodM.setPeriodInfoList(periodInfoList);
        return periodM;
    }

    private PeriodInfoM buildPeriodInfoM(PeriodDTO period) {
        PeriodInfoM periodInfoM = new PeriodInfoM();
        periodInfoM.setPeriodStartTime(period.getStartTime());
        periodInfoM.setPeriodEndTime(period.getEndTime());
        periodInfoM.setPeriodOriginSalePrice(period.getOriginalSalePrice());
        periodInfoM.setAcrossDay(period.getAcrossDay());
        return periodInfoM;
    }

    private List<DayDetailPriceM> buildDayDetailPrices(List<DayDetailPriceDTO> dayDetailPrices) {
        if ( org.apache.commons.collections.CollectionUtils.isEmpty(dayDetailPrices)) {
            return null;
        }
        return dayDetailPrices.stream().filter(Objects::nonNull).map(this::buildDayDetailPriceM).collect(Collectors.toList());
    }

    private DayDetailPriceM buildDayDetailPriceM(DayDetailPriceDTO dayDetailPriceDTO) {
        DayDetailPriceM dayDetailPriceM = new DayDetailPriceM();
        dayDetailPriceM.setDate(dayDetailPriceDTO.getDate());
        dayDetailPriceM.setDayPrices(buildDayPriceList(dayDetailPriceDTO.getDayPrices()));
        return dayDetailPriceM;
    }

    private List<DayPriceInfoM> buildDayPriceList(List<DayPriceDTO> dayPrices) {
        if ( org.apache.commons.collections.CollectionUtils.isEmpty(dayPrices)) {
            return null;
        }
        return dayPrices.stream().filter(Objects::nonNull).map(this::buildDayPriceInfoM).collect(Collectors.toList());
    }

    private DayPriceInfoM buildDayPriceInfoM(DayPriceDTO dayPriceDTO) {
        DayPriceInfoM dayPriceInfoM = new DayPriceInfoM();
        dayPriceInfoM.setDateTime(dayPriceDTO.getDateTime());
        dayPriceInfoM.setTimePrice(dayPriceDTO.getTimePrice());
        return dayPriceInfoM;
    }

    @Data
    public static class MidReserveConfig implements Serializable {
        private boolean stockGranularitySwitch;
        private Map<String, Object> expParams;
        private List<String> whiteList;
    }
}
