package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.builder.model.ServiceProjectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:12
 * @Description: 老团详服务项目Fetcher
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class
)
public class ProductServiceProjectFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        ProductServiceProject> {

    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder
                .serviceProject(ServiceProjectBuilder.builder().all())//服务项目
        ;
    }

    @Override
    protected FetcherResponse<ProductServiceProject> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        DealGroupServiceProjectDTO serviceProjectDTO = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getServiceProject)
                .orElse(null);
        return FetcherResponse.succeed(new ProductServiceProject(serviceProjectDTO));
    }
}
