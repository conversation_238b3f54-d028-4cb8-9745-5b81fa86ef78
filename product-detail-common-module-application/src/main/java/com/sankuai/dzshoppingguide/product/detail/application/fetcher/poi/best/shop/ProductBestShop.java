package com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/2/6 15:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductBestShop extends FetcherReturnValueDTO {

    private long dpShopId;

    private int geoDpCityId;
    /**
     * 适用门店总数
     */
    private long totalCount;

}
