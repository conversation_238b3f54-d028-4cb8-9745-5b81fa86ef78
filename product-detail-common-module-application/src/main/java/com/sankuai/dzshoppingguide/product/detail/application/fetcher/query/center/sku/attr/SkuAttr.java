package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr;

import com.alibaba.fastjson.JSON;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:13
 */
@EqualsAndHashCode(callSuper = true)
public class SkuAttr extends FetcherReturnValueDTO {

    public SkuAttr(Map<Long, Map<String, AttrDTO>> skuAttr) {
        this.skuAttr = skuAttr;
    }

    /**
     * SkuId,attrName,attrValue
     */
    private final Map<Long, Map<String, AttrDTO>> skuAttr;

    public Map<String, AttrDTO> getSkuAttrs(Long skuId) {
        return Optional.ofNullable(skuAttr)
                .map(map -> map.get(skuId))
                .orElse(new HashMap<>());
    }

    public Optional<AttrDTO> getSkuAttr(long skuId, String attrName) {
        return Optional.ofNullable(skuAttr)
                .map(map -> map.get(skuId))
                .map(map -> map.get(attrName));
    }

    public List<String> getSkuAttrValue(long skuId, String attrName) {
        AttrDTO  attrDTO = getSkuAttr(skuId, attrName).get();
        if (Objects.isNull(attrDTO)) {
            return null;
        }
        return attrDTO.getValue();
    }

    public String getSkuAttrValueJson(long skuId, String attrName) {
        AttrDTO  attrDTO = getSkuAttr(skuId, attrName).get();
        if (Objects.isNull(attrDTO)) {
            return null;
        }
        List<String> attrValue =  attrDTO.getValue();
        if (CollectionUtils.isEmpty(attrValue)) {
            return null;
        }
        return JSON.toJSONString(attrValue);
    }

    public String getSkuAttrFirstValue(long skuId, String attrName) {
        Optional<AttrDTO> skuAttr = getSkuAttr(skuId, attrName);
        if (!skuAttr.isPresent()) {
            return null;
        }
        AttrDTO attrDTO = skuAttr.get();
        List<String> attrValue =  attrDTO.getValue();
        if (CollectionUtils.isEmpty(attrValue)) {
            return null;
        }
        return attrValue.get(0);
    }

    public static String joinListByDelimiter(List<String> values, String delimiter){
        if (CollectionUtils.isEmpty(values) || Objects.isNull(delimiter)) {
            return null;
        }
        return String.join(delimiter, values);
    }


}
