package com.sankuai.dzshoppingguide.product.detail.application.builder.common;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.common.CommonDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/20 15:32
 */
@Builder(
        moduleKey = ModuleKeyConstants.COMMON_DATA,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class,
                ProductCategoryFetcher.class,
                ShopInfoFetcher.class
        }
)
public class CommonDataBuilder extends BaseBuilder<CommonDataVO> {

    @Override
    public CommonDataVO doBuild() {
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ShopInfo shopInfo = getDependencyResult(ShopInfoFetcher.class);
        if (productBaseInfo == null || productCategory == null || shopInfo == null) {
            throw new IllegalArgumentException("productBaseInfo or productCategory or shopInfo is null");
        }
        CommonDataVO vo = new CommonDataVO();
        vo.setProductType(productBaseInfo.getProductIdDTO().getProductType().getCode());
        vo.setDpProductId(productBaseInfo.getProductIdDTO().getDpProductId());
        vo.setMtProductId(productBaseInfo.getProductIdDTO().getMtProductId());
        vo.setProductId(request.getProductId());
        vo.setProductFirstCategoryId(productCategory.getProductFirstCategoryId());
        vo.setProductSecondCategoryId(productCategory.getProductSecondCategoryId());
        vo.setProductThirdCategoryId(productCategory.getProductThirdCategoryId());
        vo.setDpShopId(Optional.of(shopInfo).map(ShopInfo::getDpPoiDTO).map(DpPoiDTO::getShopId).orElse(0L));
        vo.setMtShopId(Optional.of(shopInfo).map(ShopInfo::getMtPoiDTO).map(MtPoiDTO::getMtPoiId).orElse(0L));
        // poiId已在ShopIdMapperFetcher回写
        vo.setShopId(request.getPoiId());
        vo.setShopCategoryId(Optional.of(shopInfo).map(ShopInfo::getDpPoiDTO).map(DpPoiDTO::getMainCategoryId).orElse(0));
        return vo;
    }

}
