package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.domain.cpv.ProductAttrConfigService;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:12
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class,
        fillRequestDependencies = {
                ProductCategoryFetcher.class
        }
)
public class ProductAttrFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        ProductAttr> {

    @Resource
    private ProductAttrConfigService productAttrConfigService;

    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        Set<String> productAttrConfig = productAttrConfigService.getProductAttrConfig(
                request.getProductTypeEnum(), productCategory.getProductSecondCategoryId()
        );
        // requestBuilder.allDealGroupAttrs();
        if (CollectionUtils.isNotEmpty(productAttrConfig)) {
            requestBuilder.attrsByKey(AttrSubjectEnum.DEAL_GROUP, productAttrConfig);
        }
    }

    @Override
    protected FetcherResponse<ProductAttr> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        Map<String, AttrDTO> attrMap = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getAttrs)
                .orElse(new ArrayList<>()).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        AttrDTO::getName,
                        v -> v,
                        (v1, v2) -> v1
                ));
        return FetcherResponse.succeed(new ProductAttr(attrMap));
    }

}
