package com.sankuai.dzshoppingguide.product.detail.application.constants;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON><PERSON>@meituan.com
 * @Date: 2025/2/12
 */
public class Constants {
    // 收藏相关
    public static final int DP_FAVOR_BIZ_TYPE = 100;
    public static final int MT_FAVOR_COLL_TYPE = 1;
    public static final short MT_FAVOR_SOURCE_TYPE = 45;

    /**
     * cpv进场零售属性品牌key
     */
    public static final String BRAND_ID = "pinpai";

    public static final String ADDRESS = "chandi";

    //（CPV）变更自动联动 extMap  扩张属性
    public static final String PLAT_CATEGORY_ID = "platCategoryId";
    // 二级分类id
    public static final String BP_CATEGORY_ID = "bpCategoryId";
    // 三级分类名
    public static final String BP_SERVICE_TYPE_ID = "bpServiceTypeId";
    // 三四级分类名
    public static final String BP_SERVICE_TYPE = "bpServiceType";
    // 团购交易类型
    public static final String TRADE_TYPE = "tradeType";
    // 轻舟流程ID
    public static final String ARK_PROCESS_ID = "arkProcessId";

    public static final String SELL_DIFFERENT_GRADES = "SellDifferentGrades";

    /**
     * 足疗
     */
    public static final String WHOLE_BODY = "全身";
}
