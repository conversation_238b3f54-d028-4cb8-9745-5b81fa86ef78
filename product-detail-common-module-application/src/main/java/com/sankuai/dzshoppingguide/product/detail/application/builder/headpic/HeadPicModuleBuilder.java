package com.sankuai.dzshoppingguide.product.detail.application.builder.headpic;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.lion.facade.LionFacade;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.ProductIdDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ImageHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.HeadPicModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ImagesSize;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.SpritePicModule;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ContentType;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ImageScaleEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.headpic.vo.HeadPicModulesVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
import com.sankuai.general.product.query.center.client.dto.video.DealGroupVideoDTO;
import com.sankuai.general.product.query.center.client.dto.video.ExtendVideoDTO;
import com.sankuai.general.product.query.center.client.dto.video.SpriteImageDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 17:04
 */
@Builder(
        moduleKey = ModuleKeyConstants.HEAD_PIC,
        startFetcher = CommonModuleStarter.class, dependentFetchers = {ProductCategoryFetcher.class, ProductBaseInfoFetcher.class}
)
@Slf4j
public class HeadPicModuleBuilder extends BaseBuilder<HeadPicModulesVO> {

    private static final String PIC_WIDTH = "width";
    private static final String PIC_HEIGHT = "height";
    // 雪碧图 行列数： 10
    private static final int TEN = 10;
    public static final String COLON_SIGN = ":";

    private ProductBaseInfo baseInfo;

    private int secondCategoryId;

    private boolean isMt;

    @Override
    public HeadPicModulesVO doBuild() {
        isMt = request.getClientTypeEnum().isMtClientType();
        HeadPicModulesVO headPicModulesVO = new HeadPicModulesVO();
        headPicModulesVO.setHeadPicModules(Lists.newArrayList());
        this.baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        this.secondCategoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);
        if (Objects.isNull(baseInfo) || Objects.isNull(baseInfo.getImage()) ){
            return null;
        }

        List<HeadPicModuleVO> headPicModules = headPicModulesVO.getHeadPicModules();
        List<HeadPicModuleVO> videosResult = assembleVideos();
        headPicModules.addAll(videosResult);
        List<HeadPicModuleVO> picResult = assemblePictures();
        headPicModules.addAll(picResult);
        return headPicModulesVO;
    }


    public List<HeadPicModuleVO> assemblePictures() {

        List<HeadPicModuleVO> result = Lists.newArrayList();
        if ( Objects.isNull(baseInfo) || Objects.isNull(baseInfo.getImage()) ) {
            return result;
        }
        Integer width = getPicSize(baseInfo, PIC_WIDTH, secondCategoryId);
        Integer height = getPicSize(baseInfo, PIC_HEIGHT, secondCategoryId);
        if ( width == null || height == null ) {
            width = ImageHelper.getOriginalImageWidth();
            height = ImageHelper.getOriginalImageHeight();
        }

        // TODO 图片的取数逻辑需要再确认下
        DealGroupImageDTO image = baseInfo.getImage();
        String defaultPicPath = image.getDefaultPicPath();
        String allPicPaths = image.getAllPicPaths();

        if ( StringUtils.isNotBlank(defaultPicPath) ) {
            String picUrl = ImageHelper.format(defaultPicPath, width, height, isMt);
            HeadPicModuleVO defPic = new HeadPicModuleVO(ContentType.PIC.getType(), picUrl);
            result.add(defPic);
        }
        if ( StringUtils.isNotBlank(allPicPaths) ) {
            final int finalWidth = width;
            final int finalHeight = height;
            // TODO 分隔符这个地方可能有点问题,后面需要具体的断点看下
            List<HeadPicModuleVO> otherPic = Arrays.stream(allPicPaths.split("\\|"))
                    .filter(input -> input != null && !defaultPicPath.equals(input))
                    .map(input -> new HeadPicModuleVO(ContentType.PIC.getType(), ImageHelper.format(input, finalWidth, finalHeight, isMt)))
                    .collect(Collectors.toList());
            result.addAll(otherPic);
        }

        result.forEach(item -> {
            if ( item.getType() == ContentType.PIC.getType() || item.getType() == ContentType.VIDEO.getType() ) {
                // TODO 先设置成默认的16:9的尺寸
                item.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            }
        });
        return result;
    }


    private Integer getPicSize(final ProductBaseInfo baseInfo,
                               final String heightWidth,
                               final int secondCategoryId) {
        try {
            // 头图比例配置化
            Map<Integer, Map<String, Integer>> picAspectRatioMap = LionFacade.get(LionConstants.PICSIZE_CONFIG, new TypeReference<Map<Integer, Map<String, Integer>>>() {
            });
            if ( MapUtils.isEmpty(picAspectRatioMap) ) {
                return null;
            }
            for (Integer categoryIdCf : picAspectRatioMap.keySet()) {
                if ( categoryIdCf == null ) {
                    continue;
                }

                if (secondCategoryId != categoryIdCf) {
                    continue;
                }

                Map<String, Integer> picsizeMap = picAspectRatioMap.get(secondCategoryId);
                if ( MapUtils.isEmpty(picsizeMap) ) {
                    continue;
                }
                return picsizeMap.get(heightWidth);
            }

        } catch (Exception e) {
            int mtProductId = Optional.ofNullable(baseInfo).map(ProductBaseInfo::getProductIdDTO).map(ProductIdDTO::getMtProductId).map(Long::intValue).orElse(0);
            log.error("putPicAspectRatio is error,mtProductId is {}", mtProductId, e);
        }
        return null;
    }


    private List<HeadPicModuleVO> assembleVideos() {
        List<HeadPicModuleVO> result = Lists.newArrayList();
        List<DealGroupVideoDTO> videos = baseInfo.getImage().getAllVideos();

        // todo 查询中心视频有数据后一定要删除这部分逻辑
        // if ( CollectionUtils.isEmpty(videos) ) {
        //     DealGroupVideoDTO video = new DealGroupVideoDTO();
        //     video.setVideoPath("https://msstest.vip.sankuai.com/v1/mss_13ead3cf7c264613878fe7703bf4ce06/dp-merchant-upload/ba1c7b92-d211-49f8-b543-de64b71ced8e.mp4");
        //     video.setVideoCoverPath("http://p0.inf.test.sankuai.com/dpmerchantpic/655d04f547e2c4aa4b84384b4b92020c365173.png");
        //     videos = videos == null ? Lists.newArrayList() : videos;
        //     videos.add(video);
        //     baseInfo.getImage().setAllVideos(videos);
        // }

        // todo 查询中心视频有数据后解除封印
        if ( CollectionUtils.isEmpty(videos) ) {
            return result;
        }

        for (DealGroupVideoDTO videoDTO : videos) {
            String picUrl = getPicUlr(videoDTO.getVideoCoverPath(), isMt);
            String desc = String.format("当前Wi-Fi环境确定播放？预计花费流量%.2fM", videoDTO.getVideoSize() == null ? 1024L : videoDTO.getVideoSize() / 1024.0);
            HeadPicModuleVO video = new HeadPicModuleVO(ContentType.VIDEO.getType(), picUrl);
            video.setVideoUrl(videoDTO.getVideoPath());
            video.setDesc(desc);
            video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            video.setVideoId(Objects.isNull(videoDTO.getVideoId()) ? StringUtils.EMPTY : String.valueOf(videoDTO.getVideoId()));
            // 默认，下发视频原始比例雪碧图
            assembleOriginVideoSpritePic(videoDTO, video);
            // 根据团详样式版本下发不同比例视频
            if (LionConfigUtils.distributeImageScaleByVersion(secondCategoryId)) {
                distributeImageScale(baseInfo, video, videoDTO);
            }
            result.add(video);
        }
        return result;
    }


    private String getPicUlr(String videoCoverPath, boolean isMt) {
        return Lion.getBoolean(LionConstants.DZTGDETAIL_APPKEY, LionConstants.COMPRESS_VIDEO_COVER_PIC, true)
                ? ImageHelper.mediumSize(videoCoverPath, isMt)
                : videoCoverPath;
    }

    /**
     * 下发视频 原始比例雪碧图
     *
     * @param dealGroupVideoDTO
     * @param video
     */
    public void assembleOriginVideoSpritePic(DealGroupVideoDTO dealGroupVideoDTO, HeadPicModuleVO video) {
        if ( Objects.isNull(dealGroupVideoDTO)
                || CollectionUtils.isEmpty(dealGroupVideoDTO.getExtendVideos()) ) {
            return;
        }
        List<ExtendVideoDTO> spriteExtendVideoDTOS = dealGroupVideoDTO.getExtendVideos().stream()
                .filter(e -> CollectionUtils.isNotEmpty(e.getSpriteImageList()) && Objects.nonNull(e.getSpriteImageList().get(0)))
                .collect(Collectors.toList());
        // 找出原始比例的雪碧图
        if ( CollectionUtils.isNotEmpty(spriteExtendVideoDTOS)
                && Objects.nonNull(spriteExtendVideoDTOS.get(0)) ) {
            // 组装雪碧图
            buildSpritePicByExtendVideoDto(video, spriteExtendVideoDTOS.get(0));
        }
    }

    /**
     * 下发 雪碧图
     *
     * @param video
     * @param extendVideoDTO
     */
    public void buildSpritePicByExtendVideoDto(HeadPicModuleVO video, ExtendVideoDTO extendVideoDTO) {
        if ( Objects.isNull(extendVideoDTO)
                || CollectionUtils.isEmpty(extendVideoDTO.getSpriteImageList())
                || Objects.isNull(extendVideoDTO.getSpriteImageList().get(0)) ) {
            return;
        }
        SpriteImageDTO spriteImageDTO = extendVideoDTO.getSpriteImageList().get(0);
        SpritePicModule.SpritePicModuleBuilder spritePicVOBuilder = SpritePicModule.builder()
                .spritePicUrl(spriteImageDTO.getPath())
                .totalCount(spriteImageDTO.getSubImageCount())
                .row(TEN)
                .column(TEN)
                .spriteCellSize(buildSpritePicCellSize(spriteImageDTO))
                .allSpriteImageSize(buildAllSpritePicSize(spriteImageDTO));
        video.setSpritePic(spritePicVOBuilder.build());
    }

    /**
     * 构造雪碧图--小图尺寸信息
     *
     * @param spriteImageDTO
     * @return
     */
    private ImagesSize buildSpritePicCellSize(SpriteImageDTO spriteImageDTO) {
        if ( Objects.isNull(spriteImageDTO) ) {
            return null;
        }
        ImagesSize.ImagesSizeBuilder imageSizeBuilder = ImagesSize.builder();
        imageSizeBuilder.width(spriteImageDTO.getWidth())
                .height(spriteImageDTO.getHeight());
        return imageSizeBuilder.build();
    }

    /**
     * 构造雪碧图--小图尺寸信息
     *
     * @param spriteImageDTO
     * @return
     */
    private ImagesSize buildAllSpritePicSize(SpriteImageDTO spriteImageDTO) {
        if ( Objects.isNull(spriteImageDTO)
                || Objects.isNull(spriteImageDTO.getWidth())
                || Objects.isNull(spriteImageDTO.getHeight()) ) {
            return null;
        }
        ImagesSize.ImagesSizeBuilder imageSizeBuilder = ImagesSize.builder();
        imageSizeBuilder.width(spriteImageDTO.getWidth() * TEN)
                .height(spriteImageDTO.getHeight() * TEN);
        return imageSizeBuilder.build();
    }

    private void distributeImageScale(ProductBaseInfo baseInfo, HeadPicModuleVO video, DealGroupVideoDTO videoDTO) {
        // 新团详及之前版本只能使用16:9
        assembleVideoContentPBO(baseInfo, video, videoDTO);
        // if (ctx.isEnableCardStyleV2()) {
        //     assembleVideoContentPBO(dealGroupDTO,video, videoDTO);
        // } else {
        //     video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
        // }
    }

    private void assembleVideoContentPBO(ProductBaseInfo baseInfo, HeadPicModuleVO video, DealGroupVideoDTO videoDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.service.DealContentBuilderService.assembleVideoContentPBO(DealCtx,ContentPBO)");
        video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
        if ( Objects.isNull(baseInfo) || Objects.isNull(baseInfo.getImage()) || CollectionUtils.isEmpty(baseInfo.getImage().getExtendVideos()) ) {
            return;
        }

        List<ExtendVideoDTO> extendVideoDTOS = Objects.nonNull(videoDTO) ? videoDTO.getExtendVideos() : baseInfo.getImage().getExtendVideos();
        // 剔除第一个16:9，可能原尺寸也是16:9
        extendVideoDTOS.remove(extendVideoDTOS.stream().filter(e -> e.getRatio().equals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale())).findFirst().orElse(null));
        ExtendVideoDTO extendVideoDTO = extendVideoDTOS.stream().findFirst().orElse(null);
        // 如果从ExtendVideos中没有取到原始尺寸，则使用16:9进行兜底返回
        if ( Objects.isNull(extendVideoDTO) || !extendVideoDTO.getRatio().contains(COLON_SIGN) || extendVideoDTO.getRatio().split(COLON_SIGN).length != 2 ) {
            video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
            return;
        }

        String[] ratioArray = extendVideoDTO.getRatio().split(COLON_SIGN);
        Integer compareResult = NumberUtils.toScaledBigDecimal(ratioArray[0]).divide(NumberUtils.toScaledBigDecimal(ratioArray[1]), RoundingMode.CEILING).compareTo(BigDecimal.valueOf(1));
        // 如果尺寸比值大于0则置为16:9，否则为3:4
        if ( compareResult > 0 ) {
            video.setScale(ImageScaleEnum.SIXTEEN_TO_NINE.getScale());
        } else {
            video.setScale(ImageScaleEnum.THREE_TO_FOUR.getScale());
        }
        video.setVideoUrl(extendVideoDTO.getPath());
        video.setContent(extendVideoDTO.getCoverPath());
    }
}
