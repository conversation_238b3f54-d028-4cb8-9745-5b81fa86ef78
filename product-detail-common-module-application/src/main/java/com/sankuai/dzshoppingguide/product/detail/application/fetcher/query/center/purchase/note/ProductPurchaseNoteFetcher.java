package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.general.product.query.center.client.builder.model.PurchaseNoteBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO;
import com.sankuai.general.product.query.center.client.enums.PurchaseNoteSceneEnum;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:12
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class,
        fillRequestDependencies = {
                SkuDefaultSelectFetcher.class, ShopIdMapperFetcher.class
        }
)
public class ProductPurchaseNoteFetcher extends ComponentFetcherContext<QueryByDealGroupIdRequestBuilder, QueryCenterAggregateReturnValue, ProductPurchaseNote> {

    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        Long mtShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getMtBestShopId).orElse(0L);
        Long dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);
        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        long skuId = Optional.ofNullable(skuDefaultSelect).map(SkuDefaultSelect::getSelectedSkuId).orElse(0L);
        PurchaseNoteBuilder purchaseNoteBuilder = PurchaseNoteBuilder.builder().all()
                // 购买须知/须知条/保障条
                .scenes(Arrays.asList(
                        PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE
                        , PurchaseNoteSceneEnum.PRODUCT_DETAIL_BOOK_INFORMATION
                        , PurchaseNoteSceneEnum.PRODUCT_DETAIL_BOOK_GUARANTEE)
                )
                // 限制门店id
                .dpShopId(dpShopId)
                .mtShopId(mtShopId)
                .dealId(skuId)
                ;
        requestBuilder.purchaseNote(purchaseNoteBuilder);
        // if (request.getProductTypeEnum().equals(ProductTypeEnum.RESERVE)) {
        // } else {
        //     //购买须知
        //     requestBuilder.purchaseNote(PurchaseNoteBuilder.builder().all());
        // }
    }

    @Override
    protected FetcherResponse<ProductPurchaseNote> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        PurchaseNoteDTO purchaseNoteDTO = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getPurchaseNote)
                .orElse(null);

        List<PurchaseNoteDTO> purchaseNotes = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getPurchaseNotes)
                .orElse(null);
        return FetcherResponse.succeed(new ProductPurchaseNote(purchaseNoteDTO, purchaseNotes));
    }

}
