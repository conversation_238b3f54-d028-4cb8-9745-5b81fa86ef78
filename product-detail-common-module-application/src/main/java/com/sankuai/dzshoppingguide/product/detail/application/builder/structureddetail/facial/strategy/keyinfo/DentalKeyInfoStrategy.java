package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.keyinfo;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SecondCategoryEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.SubModuleKey;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.strategy.AbstractSubModuleBuildStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.facial.DealDetailStructuredUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.enums.DentalThirdCategory.*;

/**
 * 口腔
 * 建议对照着prd看这部分逻辑,prd: https://km.sankuai.com/collabpage/2705279832#b-881203cd5b6848bf96fb0bd02fbeabde
 * <AUTHOR>
 * @date 2025/4/14 20:03
 */
@Component
public class DentalKeyInfoStrategy extends AbstractSubModuleBuildStrategy {

    @Override
    public String getSubModuleName() {
        return SubModuleKey.KEY_INFO;
    }

    @Override
    public boolean isHit(DealDetailBuildContext context) {
        ProductCategory productCategory = context.getProductCategory();
        if (productCategory == null) {
            return false;
        }
        int categoryId = productCategory.getProductSecondCategoryId();
        return categoryId == SecondCategoryEnum.ORAL_DENTAL.getSecondCategoryId();
    }

    @Override
    public List<DealDetailStructuredDetailVO> build(DealDetailBuildContext context) {
        ProductAttr productAttr = context.getProductAttr();
        if (productAttr == null || CollectionUtils.isEmpty(productAttr.getSkuAttrList())) {
            return Collections.emptyList();
        }

        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        // 根据类目获取对应的构建器并执行,填充json content
        int thirdCategoryId = getThirdCategoryId(context);
        // 填充标题
        buildTitle(context,productAttr).ifPresent(titleVO -> {
            setTitleFloatData(titleVO,thirdCategoryId,productAttr);
            result.add(titleVO);
        });

        
        List<DealDetailStructuredDetailVO> contents = genContents(thirdCategoryId, productAttr);
        if (CollectionUtils.isNotEmpty(contents)) {
            DealDetailStructuredDetailVO jsonContent = DealDetailStructuredUtils.buildContentFromAttr(JSON.toJSONString(contents));
            result.add(jsonContent);
        }

        // 增加分隔符
        DealDetailStructuredDetailVO separator = DealDetailStructuredUtils.buildLimiter();
        result.add(separator);
        return result;
    }

    private int getThirdCategoryId(DealDetailBuildContext context) {
        ProductCategory productCategory = context.getProductCategory();
        return Optional.ofNullable(productCategory).map(ProductCategory::getProductThirdCategoryId).orElse(0);
    }

    /**
     * 根据类目生成对应的内容
     * @param thirdCategoryId
     * @param productAttr
     * @return
     */
    private List<DealDetailStructuredDetailVO> genContents(int thirdCategoryId, ProductAttr productAttr) {
        List<DealDetailStructuredDetailVO> contents = new ArrayList<>();

        // 获取对应类别的构建器并执行
        Optional.of(getCategoryBuilders())
                .ifPresent(builders -> builders.forEach(builder ->
                    builder.apply(productAttr,thirdCategoryId).ifPresent(contents::add)));

        if (CollectionUtils.isEmpty(contents)) {
            return Collections.emptyList();
        }

        // 重点展示信息的模块内 添加浮层信息
        setContentFloatData(contents,thirdCategoryId,productAttr);

        // 填充售后质保浮层信息
        setQualityAssuranceFloatData(contents,productAttr,thirdCategoryId);


        return contents;
    }

    private void setUnit(DealDetailStructuredDetailVO item,String unit) {
        if (item == null || StringUtils.isBlank(unit)) {
            return;
        }
        String content = item.getContent();
        if (StringUtils.isBlank(content)) {
            return;
        }
        item.setContent(content + unit);
    }

    private void setQualityAssuranceFloatData(List<DealDetailStructuredDetailVO> contents,ProductAttr productAttr,int thirdCategoryId) {
        if (CollectionUtils.isEmpty(contents)) {
            return;
        }
        contents.stream().filter(Objects::nonNull).forEach(content -> setQualityAssurance(content,productAttr,thirdCategoryId));
    }

    private void setQualityAssurance(DealDetailStructuredDetailVO content,ProductAttr productAttr,int thirdCategoryId) {
        if (content == null || productAttr == null || !StringUtils.equals(content.getTitle(),"售后质保")) {
            return;
        }

        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        genGuaranteeInfo(productAttr,"AntiColorGuarantee2","ColorInverseGuaranteeUnit","反色保障",thirdCategoryId).ifPresent(result::addAll);
        genGuaranteeInfo(productAttr,"SensitiveRelief2","SensitiveReliefUnit2","敏感缓解",thirdCategoryId).ifPresent(result::addAll);
        genGuaranteeInfo(productAttr,"Defectiveguarantee","Abscissionassuranceunit","脱落保障",thirdCategoryId).ifPresent(result::addAll);
        genGuaranteeInfo(productAttr,"BustCeramicInsurance","Bustceramicinsuranceunit","崩瓷保障",thirdCategoryId).ifPresent(result::addAll);
        genGuaranteeInfo(productAttr,"ColorEnsure","ColorAssuranceUnit","颜色保障",thirdCategoryId).ifPresent(result::addAll);
        genGuaranteeInfo(productAttr,"Biteassurance","BiteVerificationUnit","咬合保障",thirdCategoryId).ifPresent(result::addAll);
        genGuaranteeInfo(productAttr,"SecondaryTreatmentAssurance4","SecondaryTreatmentAssuranceUnit3","二次治疗保障",thirdCategoryId).ifPresent(result::addAll);

        if (CollectionUtils.isEmpty(result)) {
            return;
        }

        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder = DealDetailStructuredDetailVO.builder();
        builder.title("售后质保");
        // propupData里面的type必须有值,结构化的是1,tab切换的是2
        builder.type(ViewComponentTypeEnum.POPUP_STRUCTURED.getType());
        builder.content(JSON.toJSONString(result));
        content.setPopupData(JSON.toJSONString(builder.build()));
    }

    /**
     * 反色保障 AntiColorGuarantee2 ColorInverseGuaranteeUnit
     */
    private Optional<List<DealDetailStructuredDetailVO>> genGuaranteeInfo(ProductAttr productAttr,String key,String value,String title,int thirdCategoryId) {
        String guaranteeQuantity = productAttr.getSkuAttrFirstValue(key);
        String unit = productAttr.getSkuAttrFirstValue(value);
        if (StringUtils.isBlank(guaranteeQuantity) || StringUtils.isBlank(unit)) {
            return Optional.empty();
        }

        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder1 = DealDetailStructuredDetailVO.builder();
        DealDetailStructuredDetailVO titleVO = builder1.type(ViewComponentTypeEnum.GUARANTEE_TITLE.getType()).title(title).backgroundColor("#f6f6f6").endBackgroundColor("#fdfdfd").build();


        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder2 = DealDetailStructuredDetailVO.builder();
        String guaranteeSuffix = guaranteeSuffix(productAttr, thirdCategoryId, title);
        DealDetailStructuredDetailVO content = builder2.type(ViewComponentTypeEnum.NORMAL_TEXT.getType())
                .titleFontWeight("600")
                .content(guaranteeQuantity + unit + guaranteeSuffix).contentColor("#888").build();

        return Optional.of(Lists.newArrayList(titleVO,content));
    }

    private String guaranteeSuffix(ProductAttr productAttr, int thirdCategoryId, String title) {
        String technique = getTechnique(productAttr);

        // 补牙和儿童补牙
        if ((thirdCategoryId == TEETH_FILLING || thirdCategoryId == CHILD_TEETH_FILLING) 
            && StringUtils.equals(title, "脱落保障")) {
            return "内脱落免费重补";
        }
        
        // 根管/根尖治疗和儿童根管治疗
        if ((thirdCategoryId == ROOT_CANAL_TREATMENT || thirdCategoryId == CHILD_ROOT_CANAL_TREATMENT)
            && StringUtils.equals(title, "二次治疗保障")) {
            return "内复发免费二次治疗";
        }
        
        // 窝沟封闭
        if (thirdCategoryId == PIT_AND_FISSURE_SEALANT && StringUtils.equals(title, "脱落保障")) {
            return "内脱落免费重补";
        }
        
        // 美白
        if (thirdCategoryId == TEETH_WHITENING) {
            if (StringUtils.equals(title, "反色保障")) {
                return "内反色免费重做";
            }
            if (StringUtils.equals(title, "敏感缓解")) {
                return "内敏感牙免费脱敏环节";
            }
        }
        
        // 牙贴面修复
        if (thirdCategoryId == DENTAL_VENEER) {
            if (StringUtils.equals(title, "脱落保障")) {
                return "内脱落免费重做并安装";
            }
            if (StringUtils.equals(title, "崩瓷保障")) {
                return "内免费局部修补";
            }
            if (StringUtils.equals(title, "颜色保障")) {
                return "内可免费调整";
            }
        }
        
        // 牙冠修复和儿童牙冠修复
        if ((thirdCategoryId == DENTAL_CROWN || thirdCategoryId == CHILD_DENTAL_CROWN)) {
            if (StringUtils.equals(title, "脱落保障")) {
                return "内脱落免费重做并安装";
            }
            if (StringUtils.equals(title, "崩瓷保障")) {
                return "内免费局部修补";
            }
        }
        
        // 义齿/假牙
        if (thirdCategoryId == DENTURE) {
            if (StringUtils.equals(title, "脱落保障")) {
                return "内脱落免费重做并安装";
            }
            if (StringUtils.equals(title, "崩瓷保障")) {
                return "内免费局部修补";
            }
            if (StringUtils.equals(title, "咬合保障")) {
                return "内可免费重新设计并调整";
            }
        }
        
        return "";
    }

    private void setTitleFloatData(DealDetailStructuredDetailVO titleVO, int thirdCategoryId,ProductAttr productAttr) {
        if (thirdCategoryId <= 0 || titleVO == null || StringUtils.isBlank(titleVO.getTitle())) {
            return;
        }

        // 处理洁牙类目
        // if (isCleaningCategory(thirdCategoryId) && CLEANING_TYPES.contains(title)) {
        if (isCleaningCategory(thirdCategoryId)) {
            titleVO.setPopupData(genSciencePopupData1(PopupType1.TOOTH_WASH));
            titleVO.setDetail("洗牙怎么选");
            return;
        }

        // 处理补牙类目
        // if (isFillingCategory(thirdCategoryId) && FILLING_TYPES.contains(title)) {
        if (isFillingCategory(thirdCategoryId)) {
            titleVO.setPopupData(genSciencePopupData2(PopupType2.MATERIAL_CHOOSE,productAttr));
            titleVO.setDetail("材料怎么选");
        }
    }

    private boolean isCleaningCategory(int thirdCategoryId) {
        return thirdCategoryId == TEETH_CLEANING || thirdCategoryId == CHILD_TEETH_CLEANING;
    }

    private boolean isFillingCategory(int thirdCategoryId) {
        return thirdCategoryId == TEETH_FILLING || thirdCategoryId == CHILD_TEETH_FILLING;
    }

    private void setContentFloatData(List<DealDetailStructuredDetailVO> contents, int thirdCategoryId,ProductAttr productAttr) {
        if (thirdCategoryId <= 0 || CollectionUtils.isEmpty(contents)) {
            return;
        }

        contents.stream()
            .filter(Objects::nonNull)
            .forEach(content -> setPopupDataForContent(content, thirdCategoryId,productAttr));
    }

    private void setPopupDataForContent(DealDetailStructuredDetailVO content, int thirdCategoryId,ProductAttr productAttr) {
        if (isCleaningCategory(thirdCategoryId)) {
            setCleaningPopupData(content);
            return;
        }

        if (isFillingCategory(thirdCategoryId)) {
            setFillingPopupData(content,productAttr);
        }
    }

    private void setCleaningPopupData(DealDetailStructuredDetailVO content) {
        String title = content.getTitle();
        if ("是否含喷砂".equals(title)) {
            content.setPopupData(genSciencePopupData1(PopupType1.SANDBLASTING));
        } else if ("是否含抛光".equals(title)) {
            content.setPopupData(genSciencePopupData1(PopupType1.POLISHING));
        }
    }

    private void setFillingPopupData(DealDetailStructuredDetailVO content,ProductAttr productAttr) {
        if ("适用牙位".equals(content.getTitle())) {
            content.setPopupData(genSciencePopupData2(PopupType2.TOOTH_INTRODUCE,productAttr));
        }
        if ("材料型号".equals(content.getTitle())) {
            content.setPopupData(genSciencePopupData2(PopupType2.MATERIAL_COMPARISON,productAttr));
        }
    }

    private enum PopupType1 {
        TOOTH_WASH,
        SANDBLASTING,
        POLISHING
    }

    private enum PopupType2 {
        MATERIAL_CHOOSE, // 材料怎么选
        TOOTH_INTRODUCE, // 牙位介绍
        MATERIAL_COMPARISON // 树脂材料对比
    }

    /**
     * 科普类说明浮层2
     * @param type
     * @return
     */
    private String genSciencePopupData2(PopupType2 type, ProductAttr productAttr) {
        if (type == null) {
            return StringUtils.EMPTY;
        }
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder1 = DealDetailStructuredDetailVO.builder();
        if (PopupType2.MATERIAL_CHOOSE == type) {
            builder1.detail("selected");
        }
        DealDetailStructuredDetailVO toothWash = builder1.title("材料怎么选")
                .icon(hwoToChooseMaterial(productAttr))
                .build();

        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder2 = DealDetailStructuredDetailVO.builder();
        if (PopupType2.TOOTH_INTRODUCE == type) {
            builder2.detail("selected");
        }
        DealDetailStructuredDetailVO polishing = builder2.title("牙位介绍")
                .icon("https://p0.meituan.net/travelcube/59181a653b186c0b0925974722aa616049956.png")
                .content("以上描述仅供参考，选择补牙材料应综合考虑牙齿位置、损伤程度、美观要求、咀嚼习惯及经济条件，并咨询专业牙医的建议。")
                .build();

        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder3 = DealDetailStructuredDetailVO.builder();
        if (PopupType2.MATERIAL_COMPARISON == type) {
            builder3.detail("selected");
        }
        DealDetailStructuredDetailVO toothMaterialComparison = null;
        // 当Technique=树脂补牙；且当MaterialBrand=3M时，才会有这个科普信息的入口
        String technique = productAttr.getSkuAttrFirstValue("Technique");
        String materialBrand = productAttr.getSkuAttrFirstValue("MaterialBrand");
        if (Objects.equals(technique, "树脂补牙") && Objects.equals(materialBrand, "3M")) {
            String materialModel = productAttr.getSkuAttrFirstValue("MaterialModel");
            materialModel = StringUtils.isNotBlank(materialModel) ? materialModel.toUpperCase() : "";
            Map<String, String> materialComparisonPicMap = LionConfigUtils.getMaterialComparisonPicMap();
            String materialModelIcon = materialComparisonPicMap.get(materialModel);
            toothMaterialComparison = builder3.title("3M树脂材料系列对比")
                    .icon(materialModelIcon)
                    .build();
        }
        List<DealDetailStructuredDetailVO> toothKeyInfo = Lists.newArrayList(toothWash, polishing, toothMaterialComparison)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        String content = JSON.toJSONString(toothKeyInfo);
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder result = DealDetailStructuredDetailVO.builder();
        return JSON.toJSONString(result.title("材料怎么选").type(ViewComponentTypeEnum.POPUP_DATA.getType()).content(content).build());
    }

    private static final String DEFAULT_MATERIAL_DESC = "https://p0.meituan.net/ingee/4f9cbd0c7d57406109cd824472ef52f0128341.png";
    private String hwoToChooseMaterial(ProductAttr productAttr){
        String technique = getTechnique(productAttr);

        Map<String, String> dentalMaterialMap = LionConfigUtils.getDentalMaterialMap();

        if (StringUtils.isBlank(technique) || MapUtils.isEmpty(dentalMaterialMap)) {
            return DEFAULT_MATERIAL_DESC;
        }

        return dentalMaterialMap.getOrDefault(technique, DEFAULT_MATERIAL_DESC);
    }


    /**
     * 科普类说明浮层1
     * @param type
     * @return
     */
    private String genSciencePopupData1(PopupType1 type) {
        if (type == null) {
            return StringUtils.EMPTY;
        }
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder1 = DealDetailStructuredDetailVO.builder();
        if (PopupType1.TOOTH_WASH == type) {
            builder1.detail("selected");
        }
        DealDetailStructuredDetailVO toothWash = builder1.title("洗牙技术怎么选")
            .icon("https://p0.meituan.net/ingee/77782c274ef6ccacb3f0e21101197d0137510.png")
            .build();

        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder2 = DealDetailStructuredDetailVO.builder();
        if (PopupType1.POLISHING == type) {
            builder2.detail("selected");
        }
        DealDetailStructuredDetailVO polishing = builder2.title("抛光的作用")
            .icon("https://p0.meituan.net/ingee/79ba5fedd63a997515c064289151563a435863.png")
            .build();

        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder builder3 = DealDetailStructuredDetailVO.builder();
        if (PopupType1.SANDBLASTING == type) {
            builder3.detail("selected");
        }
        DealDetailStructuredDetailVO sandblasting = builder3.title("喷砂的作用")
            .icon("https://p0.meituan.net/ingee/348967d1c9f1dc213813a11dd578d3d6489357.png")
            .content("以上描述仅供参考，定期洗牙有助于维护口腔健康，应综合考虑个人口腔健康状况进行选择。")
            .build();

        String content = JSON.toJSONString(Arrays.asList(toothWash, polishing, sandblasting));
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder result = DealDetailStructuredDetailVO.builder();
        return JSON.toJSONString(result.title("洗牙小贴士").type(ViewComponentTypeEnum.POPUP_DATA.getType()).content(content).build());
    }

    /**
     * 根据类目ID获取对应的构建器列表
     * @return
     */
    private List<BiFunction<ProductAttr,Integer,Optional<DealDetailStructuredDetailVO>>> getCategoryBuilders() {
        return Arrays.asList(
                this::machineBrand,
                this::buildSandblasting,
                this::buildSandBlastingFlag,
                this::buildPolishingFlag,
                this::buildOperators,
                this::buildApplicableTime,
                this::buildApplicableAge,
                this::buildToothType,
                this::buildApplicableDentalPositions,
                this::buildQuantity,
                this::buildPackageIncludes,
                this::buildMaterialBrand,
                this::buildMaterialModel,
                this::buildQualityAssuranceType,
                this::buildType,
                this::buildTreatmentScope,
                this::buildMaterial2,
                this::buildDecryptionMethod,
                this::buildResetWay,
                this::buildTweakingApproach,
                this::buildProject,
                this::buildCount,
                this::buildWhiteningAgentConcentration,
                this::buildPilecore,
                this::buildDentalCrownBrand,
                this::buildGetMethod,
                this::buildDesirableDuration,
                this::buildNetContent,
                this::buildCount2
        );
    }

    /**
     * 1
     * 洁牙机品牌 Dentalcleaningmachinebrand
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> machineBrand(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("Dentalcleaningmachinebrand", "洁牙机品牌", productAttr);
    }

    /**
     * 2
     * 喷砂粉品牌
     * Sandblasting
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildSandblasting(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("Sandblasting", "喷砂粉品牌", productAttr);
    }

    /**
     * 3
     * 是否含喷砂
     * DoesItContainSandBlasting
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildSandBlastingFlag(ProductAttr productAttr,int thirdCategoryId) {
        Optional<DealDetailStructuredDetailVO> detailVO = DealDetailStructuredUtils.buildContentFromAttr("DoesItContainSandBlasting", "是否含喷砂", productAttr);
        if (detailVO.isPresent()) {
            DealDetailStructuredDetailVO detail = detailVO.get();
            detail.setPopupData(genSciencePopupData1(PopupType1.SANDBLASTING));
        }
        return detailVO;
    }

    /**
     * 4
     * 是否含抛光
     * DoesItContainPolishing
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildPolishingFlag(ProductAttr productAttr,int thirdCategoryId) {
        Optional<DealDetailStructuredDetailVO> detailVO = DealDetailStructuredUtils.buildContentFromAttr("DoesItContainPolishing", "是否含抛光", productAttr);
        if (detailVO.isPresent()) {
            DealDetailStructuredDetailVO detail = detailVO.get();
            detail.setPopupData(genSciencePopupData1(PopupType1.POLISHING));
        }
        return detailVO;
    }

    /**
     * 5
     * 操作人员
     * Operators
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildOperators(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("Operators", "操作人员", productAttr);
    }

    /**
     * 6
     * 适用时间
     * ApplicableTime
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildApplicableTime(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("ApplicableTime", "适用时间", productAttr);
    }

    /**
     * 7
     * 适用年龄 applicable_age_min applicable_age_max
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildApplicableAge(ProductAttr productAttr,int thirdCategoryId) {
        String minAge = productAttr.getSkuAttrFirstValue("applicable_age_min");
        String maxAge = productAttr.getSkuAttrFirstValue("applicable_age_max");
        if (StringUtils.isBlank(minAge) || StringUtils.isBlank(maxAge)) {
            return Optional.empty();
        }
        return Optional.of(DealDetailStructuredDetailVO.builder().title("适用年龄").content(minAge + "-" + maxAge + "岁").build());
    }

    /**
     * 8
     * 牙齿类型
     * ToothType
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildToothType(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("ToothType", "牙齿类型", productAttr);
    }

    /**
     * 9
     * 适用牙位
     * Applicabledentalpositions
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildApplicableDentalPositions(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("Applicabledentalpositions", "适用牙位", productAttr);
    }

    /**
     * 10
     * 数量
     * Quantity3
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildQuantity(ProductAttr productAttr,int thirdCategoryId) {
        String quantityTitle = getQuantityTitle(thirdCategoryId);
        String result = productAttr.getSkuAttrFirstValue("Quantity3");
        String unit = getQuantityUnit(productAttr,thirdCategoryId);
        if (StringUtils.isBlank(result)) {
            return Optional.empty();
        }
        return DealDetailStructuredUtils.buildTitleAndContent(quantityTitle,result + unit);
    }

    private String getQuantityUnit(ProductAttr productAttr,int thirdCategoryId) {
        Map<Integer, String> categoryUnitMap = new HashMap<>();
        categoryUnitMap.put(TEETH_EXTRACTION, "颗");
        categoryUnitMap.put(MILK_TEETH_EXTRACTION, "颗");
        categoryUnitMap.put(TEETH_FILLING, "颗");
        categoryUnitMap.put(CHILD_TEETH_FILLING, "颗");
        categoryUnitMap.put(ROOT_CANAL_TREATMENT, "颗");
        categoryUnitMap.put(CHILD_ROOT_CANAL_TREATMENT, "颗");
        categoryUnitMap.put(PERIODONTAL_TREATMENT, "颗");
        categoryUnitMap.put(DENTAL_CROWN, "颗");
        categoryUnitMap.put(CHILD_DENTAL_CROWN, "颗");
        categoryUnitMap.put(LOOSE_TEETH_FIXATION, "颗");
        categoryUnitMap.put(ORTHODONTICS, "副");
        categoryUnitMap.put(EARLY_ORTHODONTICS, "副");
        categoryUnitMap.put(OTHER_DENTAL_TREATMENT, "份");
        if (thirdCategoryId == ORAL_CARE_PRODUCTS) {
            String technique = getTechnique(productAttr);
            if (StringUtils.isBlank(technique)) {
                return StringUtils.EMPTY;
            }
            if (Lists.newArrayList("磨牙保护牙套", "运动护牙套").contains(technique)) {
                return "副";
            } else if (Lists.newArrayList("牙膏", "牙刷", "漱口水", "牙线").contains(technique)) {
                return "份";
            }
            return StringUtils.EMPTY;
        }
        return categoryUnitMap.getOrDefault(thirdCategoryId,StringUtils.EMPTY);
    }

    private String getQuantityTitle(int thirdCategoryId) {
        switch (thirdCategoryId) {
            case TEETH_FILLING:
            case CHILD_TEETH_FILLING:
                return "颗数";
            case TEETH_EXTRACTION:
            case MILK_TEETH_EXTRACTION:
                return "拔牙颗数";
            case ORTHODONTICS:
                return "牙套数量";
            case DENTAL_CROWN:
            case CHILD_DENTAL_CROWN:
            case DENTURE:
            case DENTAL_VENEER:
                return "修复数量";
            case EARLY_ORTHODONTICS:
                return "矫治器数量";
            case PIT_AND_FISSURE_SEALANT:
                return "封闭颗数";
            case PERIODONTAL_TREATMENT:
            case ROOT_CANAL_TREATMENT:
            case CHILD_ROOT_CANAL_TREATMENT:
                return "治疗颗数";
            case LOOSE_TEETH_FIXATION:
                return "固定颗数";
            default:
                return "数量";
        }
    }

    /**
     * 11
     * 套餐包含
     * PackageIncludes
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildPackageIncludes(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttrs("PackageIncludes", "套餐包含", productAttr);
    }

    /**
     * 12
     * 材料品牌
     * MaterialBrand
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildMaterialBrand(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("MaterialBrand", "材料品牌", productAttr);
    }

    /**
     * 13
     * 材料型号
     * MaterialModel
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildMaterialModel(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("MaterialModel", "材料型号", productAttr);
    }

    /**
     * 14
     * 售后质保 QualityAssuranceType
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildQualityAssuranceType(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttrs("QualityAssuranceType", "售后质保", productAttr);
    }

    /**
     * 15
     * 类型
     * Type
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildType(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("Type", "类型", productAttr);
    }

    /**
     * 16
     * 治疗范围
     * TreatmentScope
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildTreatmentScope(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("TreatmentScope", "治疗范围", productAttr);
    }

    /**
     * 17
     * 材料
     * Material2
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildMaterial2(ProductAttr productAttr,int thirdCategoryId) {
        switch (thirdCategoryId) {
            case TEETH_FILLING:
            case CHILD_TEETH_FILLING:
                return DealDetailStructuredUtils.buildContentFromAttr("Material2", "补牙材料", productAttr);
            case FLUORIDE:
                return DealDetailStructuredUtils.buildContentFromAttr("Material2", "氟化物材料", productAttr);
            case PIT_AND_FISSURE_SEALANT:
                return DealDetailStructuredUtils.buildContentFromAttr("Material2", "封闭材料", productAttr);
            case TEETH_DESENSITIZATION:
                return DealDetailStructuredUtils.buildContentFromAttr("Material2", "脱敏材料", productAttr);
            default:
                return DealDetailStructuredUtils.buildContentFromAttr("Material2", "材料", productAttr);
        }

    }

    /**
     * 18
     * 脱敏方式
     * DecryptionMethod
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildDecryptionMethod(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("DecryptionMethod", "脱敏方式", productAttr);
    }

    /**
     * 19
     * 复位方式
     * ResetWay
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildResetWay(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("ResetWay", "复位方式", productAttr);
    }

    /**
     * 20
     * 修整方式
     * TweakingApproach
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildTweakingApproach(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("TweakingApproach", "修整方式", productAttr);
    }

    /**
     * 21
     * 项目
     * Project
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildProject(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("Project", "项目", productAttr);
    }

    /**
     * 22
     * 次数
     * Count
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildCount(ProductAttr productAttr,int thirdCategoryId) {
        switch (thirdCategoryId) {
            case TEETH_WHITENING:
                return buildCount("美白次数", productAttr);
            case FLUORIDE:
                return buildCount("涂氟次数", productAttr);
            case TEETH_CLEANING:
            default:
                return buildCount("次数", productAttr);
        }
    }

    private Optional<DealDetailStructuredDetailVO> buildCount(String title, ProductAttr productAttr) {
        String result = productAttr.getSkuAttrFirstValue("Count");
        if (StringUtils.isBlank(result) || StringUtils.isBlank(title)) {
            return Optional.empty();
        }
        return DealDetailStructuredUtils.buildTitleAndContent(title,result + "次");
    }

    /**
     * 23
     * 美白剂浓度
     * WhiteningAgentConcentration
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildWhiteningAgentConcentration(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("WhiteningAgentConcentration", "美白剂浓度", productAttr);
    }

    /**
     * 24
     * 桩核
     * Pilecore
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildPilecore(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("Pilecore", "桩核", productAttr);
    }

    /**
     * 25
     * 牙冠品牌
     * DentalCrownBrand
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildDentalCrownBrand(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("DentalCrownBrand", "牙冠品牌", productAttr);
    }

    /**
     * 26
     * 领取方式
     * GetMethod
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildGetMethod(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttrsWithLimit("get", "领取方式", productAttr);
    }

    /**
     * 27
     * 可取时长
     * DesirableDuration
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildDesirableDuration(ProductAttr productAttr,int thirdCategoryId) {
        String desirableDuration = productAttr.getSkuAttrFirstValue("DesirableDuration");
        if (StringUtils.isBlank(desirableDuration)) {
            return Optional.empty();
        }
        String specifyDuration = productAttr.getSkuAttrFirstValue("SpecifyDuration");

        if ("当日可取".equals(desirableDuration)) {
            return DealDetailStructuredUtils.buildTitleAndContent("可取时长","当日可取");
        } else if("指定天数后可取".equals(desirableDuration) && StringUtils.isNotBlank(specifyDuration)) {
            return DealDetailStructuredUtils.buildTitleAndContent("可取时长",specifyDuration + "天后可取");
        } else {
            return Optional.empty();
        }
    }

    /**
     * 28
     * 净含量
     * NetContent
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildNetContent(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("NetContent", "净含量", productAttr);
    }

    /**
     * 29
     * 支数
     * Count2
     * @param productAttr
     * @return
     */
    private Optional<DealDetailStructuredDetailVO> buildCount2(ProductAttr productAttr,int thirdCategoryId) {
        return DealDetailStructuredUtils.buildContentFromAttr("Count2", "支数", productAttr);
    }

    private Optional<DealDetailStructuredDetailVO> buildTitle(DealDetailBuildContext context,ProductAttr productAttr) {
        int thirdCategoryId = getThirdCategoryId(context);
        switch (thirdCategoryId) {
            case TEETH_CLEANING:
            case CHILD_TEETH_CLEANING:
            case TEETH_FILLING:
            case CHILD_TEETH_FILLING:
            case CHILD_DENTAL_CROWN:
                return buildPeopleAndTechniqueTitle(productAttr);
            case MILK_TEETH_EXTRACTION:
            case PERIODONTAL_TREATMENT:
            case ROOT_CANAL_TREATMENT:
            case CHILD_ROOT_CANAL_TREATMENT:
            case LOOSE_TEETH_FIXATION:
            case DENTAL_CROWN:
            case DENTAL_VENEER:
            case TEETH_WHITENING:
            case ORTHODONTICS:
            case ORAL_CARE_PRODUCTS:
            case OTHER_DENTAL_TREATMENT:
            case FLUORIDE:
            case PIT_AND_FISSURE_SEALANT:
            case EARLY_ORTHODONTICS:
                return buildTechniqueTitle(productAttr);
            case TEETH_EXTRACTION:
                return buildSpecialTitle1(productAttr);
            case TEETH_DESENSITIZATION:
                return buildSpecialTitle2(productAttr);
            case DENTURE:
                return buildSpecialTitle3(productAttr);
            default:
                return Optional.empty();
        }
    }

    /**
     * 适用人群+术式
     */
    private Optional<DealDetailStructuredDetailVO> buildPeopleAndTechniqueTitle(ProductAttr productAttr) {
        // 服务流程信息
        String people = productAttr.getSkuAttrFirstValue("tooth_suit_people");
        // String technique = productAttr.getSkuAttrFirstValue("Technique");
        String technique = getTechnique(productAttr);
        if (StringUtils.isAnyBlank(people,technique)) {
            return Optional.empty();
        }
        return Optional.of(DealDetailStructuredUtils.buildTitle(people + technique));
    }

    private String getTechnique(ProductAttr productAttr) {
        if (productAttr == null) {
            return null;
        }
        String technique = productAttr.getSkuAttrFirstValue("Technique");
        String technique4 = productAttr.getSkuAttrFirstValue("Technique4");
        if (StringUtils.isNotBlank(technique)) {
            return technique;
        }
        return technique4;
    }

    /**
     * 术式
     */
    private Optional<DealDetailStructuredDetailVO> buildTechniqueTitle(ProductAttr productAttr) {
        // 服务流程信息
        String technique = getTechnique(productAttr);
        if (StringUtils.isBlank(technique)) {
            return Optional.empty();
        }
        return Optional.of(DealDetailStructuredUtils.buildTitle(technique));
    }

    /**
     * 还有三个特殊逻辑 28
     * 当【术式Technique】=智齿拔除时，展示为"智齿拔除"当【术式Technique】=普通拔牙时，展示为"拔牙"
     */
    private Optional<DealDetailStructuredDetailVO> buildSpecialTitle1(ProductAttr productAttr) {
        // 服务流程信息
        String technique = getTechnique(productAttr);
        if (StringUtils.isBlank(technique)) {
            return Optional.empty();
        }
        if (StringUtils.equals("智齿拔除",technique)) {
            return Optional.of(DealDetailStructuredUtils.buildTitle("智齿拔除"));
        }
        if (StringUtils.equals("普通拔牙",technique)) {
            return Optional.of(DealDetailStructuredUtils.buildTitle("拔牙"));
        }
        return buildPeopleAndTechniqueTitle(productAttr);
    }

    /**
     * 157001 当【适用人群tooth_suit_people】=成人时，展示为"牙齿脱敏"当【适用人群tooth_suit_people】=儿童时，展示为"儿童牙齿脱敏"
     */
    private Optional<DealDetailStructuredDetailVO> buildSpecialTitle2(ProductAttr productAttr) {
        // 服务流程信息
        String people = productAttr.getSkuAttrFirstValue("tooth_suit_people");
        if (StringUtils.isBlank(people)) {
            return Optional.empty();
        }
        if (StringUtils.equals("成人",people)) {
            return Optional.of(DealDetailStructuredUtils.buildTitle("牙齿脱敏"));
        }
        if (StringUtils.equals("儿童",people)) {
            return Optional.of(DealDetailStructuredUtils.buildTitle("儿童牙齿脱敏"));
        }
        return buildPeopleAndTechniqueTitle(productAttr);
    }


    /**
     * 151001 当【术式Technique】=固定义齿修复时，展示为"固定义齿(假牙)"当【术式Technique】=活动义齿修复时，展示为"活动义齿(假牙)"
     */
    private Optional<DealDetailStructuredDetailVO> buildSpecialTitle3(ProductAttr productAttr) {
        // 服务流程信息
        // String technique = productAttr.getSkuAttrFirstValue("Technique");
        String technique = getTechnique(productAttr);
        if (StringUtils.isBlank(technique)) {
            return Optional.empty();
        }
        if (StringUtils.equals("固定义齿修复",technique)) {
            return Optional.of(DealDetailStructuredUtils.buildTitle("固定义齿(假牙)"));
        }
        if (StringUtils.equals("活动义齿修复",technique)) {
            return Optional.of(DealDetailStructuredUtils.buildTitle("活动义齿(假牙)"));
        }
        return buildPeopleAndTechniqueTitle(productAttr);
    }

    @Override
    protected List<DealDetailStructuredDetailVO> doBuild(DealDetailBuildContext context) {
        return Collections.emptyList();
    }

}
