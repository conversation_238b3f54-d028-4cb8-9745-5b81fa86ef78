package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/11 19:45
 */
@Fetcher(
        previousLayerDependencies = {CommonModuleStarter.class}
)
@Slf4j
public class ProductCategoryFetcher extends NormalFetcherContext<ProductCategory> {

    @Override
    protected CompletableFuture<ProductCategory> doFetch() {
        try {
            int productFirstCategoryId = NumberUtils.toInt(this.request.getCustomParam(RequestCustomParamEnum.productFirstCategoryId), -1);
            int productSecondCategoryId = NumberUtils.toInt(this.request.getCustomParam(RequestCustomParamEnum.productSecondCategoryId), -1);
            int productThirdCategoryId = NumberUtils.toInt(this.request.getCustomParam(RequestCustomParamEnum.productThirdCategoryId), 0);
            if (productSecondCategoryId < 0) {
                //productCategory在网关层没有获取到，理论上肯定有
                throw new ProductDetailFatalError("productCategory在网关层没有获取到，理论上肯定有");
            } else {
                return CompletableFuture.completedFuture(new ProductCategory(
                        productFirstCategoryId, productSecondCategoryId, productThirdCategoryId
                ));
            }
        } catch (Exception e) {
            log.error("ProductCategoryFetcher,request:{}", JSON.toJSONString(this.request), e);
            throw e;
        }
    }

}
