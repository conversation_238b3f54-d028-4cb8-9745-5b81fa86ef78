package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import com.dianping.csc.center.engine.access.dto.AccessRequestDTO;
import com.dianping.csc.center.engine.access.dto.AccessResponseDTO;
import com.dianping.csc.center.engine.access.service.AccessInService;
import com.dianping.deal.tag.DealMetaTagQueryService;
import com.dianping.deal.tag.SubjectTagJudgeService;
import com.dianping.deal.tag.dto.MetaTagDTO;
import com.dianping.deal.tag.dto.MultiSubjectTagBatchJudgeRequest;
import com.dianping.deal.tag.dto.MultiSubjectTagBatchJudgeResponse;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.reviewremote.remote.ReviewDealGroupServiceV2;
import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import com.dianping.ugc.review.remote.enums.ReviewPlatFormEnum;
import com.dianping.ugc.review.remote.mt.MTReviewQueryService;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.sankuai.athena.inf.rpc.CallType;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzcard.navigation.api.dto.QueryCardInfoDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryDiscountCardReq;
import com.sankuai.dzim.cliententry.ClientEntryService;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.dztheme.massagebook.theme.ReserveThemeQueryService;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupDisplayShopBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.mdp.dzrank.scenes.api.RankLabelQuery;
import com.sankuai.mdp.dzrank.scenes.api.request.RankLabelDataRequest;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
import com.sankuai.mdp.dzshoplist.rank.api.response.Response;
import com.sankuai.meituan.shangou.standardquery.thrift.command.ListSgBrandByIdsCommand;
import com.sankuai.meituan.shangou.standardquery.thrift.domain.BrandVo;
import com.sankuai.meituan.shangou.standardquery.thrift.result.TBListBrandResult;
import com.sankuai.meituan.shangou.standardquery.thrift.service.BrandThriftService;
import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.response.BizProductIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.service.IdService;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.service.GuaranteeQueryService;
import com.sankuai.trade.general.reserve.response.ReserveResponse;
import com.sankuai.trade.general.reserve.service.ShopAndProductReserveStatusService;
import com.sankuai.zdc.tag.apply.api.PoiTagDisplayRPCService;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-02-13
 * @desc
 */
@Slf4j
@Component
public class CompositeAtomServiceImpl implements CompositeAtomService {

    // @MdpPigeonClient(url = "com.sankuai.dz.srcm.flow.service.FlowEntryWxMaterialService"
    //         , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    // private FlowEntryWxMaterialService flowEntryWxMaterialService;

    @MdpPigeonClient(url="com.sankuai.mdp.dzrank.scenes.api.RankLabelQuery",callType = CallMethod.CALLBACK,timeout=500, testTimeout = 50000)
    private RankLabelQuery rankLabelQuery;

    @MdpPigeonClient(url = "com.sankuai.dztheme.massagebook.ReserveThemeQueryService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private ReserveThemeQueryService reserveThemeQueryService;

    @MdpThriftClient(
            timeout = 1000, testTimeout = 5000,
            remoteAppKey = "com.sankuai.productuser.query.center"
    )
    public DealGroupQueryService dealGroupQueryServiceAtom;

    @MdpPigeonClient(url = "com.dianping.deal.tag.SubjectTagJudgeService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private SubjectTagJudgeService subjectTagJudgeService;

    @MdpPigeonClient(url = "com.dianping.deal.tag.DealMetaTagQueryService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private DealMetaTagQueryService dealMetaTagQueryServiceFuture;
    
    @RpcClient(url = "com.dianping.deal.tag.DealMetaTagQueryService", timeout = 1000,callType = CallType.SYNC)
    private DealMetaTagQueryService dealMetaTagQueryServiceSync;

    // 美团评价
    @MdpPigeonClient(url = "UGCReviewService.MTReviewQueryService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private MTReviewQueryService mtReviewQueryService;

    // 点评评价
    @MdpPigeonClient(url = "ReviewService.ReviewDealGroupService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private ReviewDealGroupServiceV2 reviewDealGroupServiceV2;

    @MdpPigeonClient(url = "com.sankuai.trade.general.reserve.service.ShopAndProductReserveStatusService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private ShopAndProductReserveStatusService shopAndProductReserveStatusService;

    @MdpPigeonClient(url = "com.sankuai.dzcard.navigation.api.DzCardExposureService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private DzCardExposureService dzCardExposureService;

    @MdpPigeonClient(url = "com.sankuai.dzim.cliententry.ClientEntryService", callType = CallMethod.CALLBACK, timeout = 500, testTimeout = 5000)
    private ClientEntryService clientEntryService;

    @MdpPigeonClient(url = "com.dianping.csc.center.engine.access.service.AccessInService", callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 5000)
    private AccessInService cscAccessInServiceFuture;

    @MdpPigeonClient(url = "http://service.dianping.com/ZDCTagApplyService/poiTagDisplayRPCService_1.0.0", callType = CallMethod.FUTURE, timeout = 1000, testTimeout = 50000)
    private PoiTagDisplayRPCService poiTagDisplayRPCService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.priceoperation.service", timeout = 500, testTimeout = 5000,async = true)
    private GuaranteeQueryService guaranteeQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.mpproduct.idservice", timeout = 500, testTimeout = 5000,async = true)
    private IdService idService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.nautilus.product.standardquery", timeout = 500, testTimeout = 5000,async = true)
    private BrandThriftService.AsyncIface brandThriftService;

    @Override
    public CompletableFuture<BrandVo> queryBrandInfoByBrandId(ListSgBrandByIdsCommand command) {
        try {
            OctoThriftCallback<BrandThriftService.AsyncClient.listSgBrandByIds_call, TBListBrandResult> thriftCallback = new OctoThriftCallback<>();
            brandThriftService.listSgBrandByIds(command,thriftCallback);
            CompletableFuture<TBListBrandResult> future = ThriftAsyncUtils.getThriftFuture(thriftCallback);
            return future.thenApply(
                    result -> Optional.ofNullable(result)
                            .map(TBListBrandResult::getBrandList)
                            .orElse(Collections.emptyList())
                            .stream()
                            .filter(Objects::nonNull)
                            .findFirst()
                            .orElse(null)
            );
        } catch (Exception e) {
            log.error("queryBrandInfoByBrandId error, command={}", JsonCodec.encodeWithUTF8(command), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<QueryCardInfoDTO> queryDiscountCardInfo(QueryDiscountCardReq request) {
        try {
            CompletableFuture<QueryCardInfoDTO> future = PigeonCallbackUtils.setPigeonCallback(QueryCardInfoDTO.class);
            dzCardExposureService.queryCardInfo(request);
            return future;
        } catch (Exception e) {
            log.error("queryDiscountCardInfo error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<Boolean> queryDealGroupOnlineReserve(long dpDealGroupId) {
        try {
            if (dpDealGroupId <= 0) {
                return CompletableFuture.completedFuture(null);
            }
            CompletableFuture<ReserveResponse> future = PigeonCallbackUtils.setPigeonCallback(ReserveResponse.class);
            shopAndProductReserveStatusService.judgeProductOneOfShopReserve((int)dpDealGroupId);
            return future.thenApply(res -> {
                if (res == null || !res.isSuccess() || res.getResult() == null) {
                    return false;
                }
                return (Boolean) res.getResult();
            });
        } catch (Exception e) {
            log.error("queryDealGroupOnlineReserve error, dpDealGroupId={}", dpDealGroupId, e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<List<ReviewStarDistributionDTO>> queryDpReview(long dpDealGroupId) {
        try {
            if (dpDealGroupId <= 0) {
                return CompletableFuture.completedFuture(null);
            }
            CompletableFuture<List> future = PigeonCallbackUtils.setPigeonCallback(List.class);
            reviewDealGroupServiceV2.getReviewStarByReferIds(Lists.newArrayList((int)dpDealGroupId));
            return future.thenApply(res -> {
                // ReviewStarDistributionDTO
                if (res == null || res.isEmpty()) {
                    return null;
                }
                return (List<ReviewStarDistributionDTO>)res;
            });
        } catch (Exception e) {
            log.error("queryDpReview error, dpDealGroupId={}", dpDealGroupId, e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<ReviewCount> queryMtReview(long mtDealGroupId) {
        try {
            if (mtDealGroupId <= 0) {
                return CompletableFuture.completedFuture(null);
            }
            CompletableFuture<Map> future = PigeonCallbackUtils.setPigeonCallback(Map.class);
            mtReviewQueryService.getReviewCountByDealIds(Lists.newArrayList((int)mtDealGroupId), ReviewPlatFormEnum.MT.value);
            return future.thenApply(res -> {
                if (MapUtils.isEmpty(res)) {
                    return null;
                }
                Object reviewCountObj = res.getOrDefault((int)mtDealGroupId, null);
                if (reviewCountObj == null) {
                    return null;
                }
                if (reviewCountObj instanceof ReviewCount) {
                    return (ReviewCount) reviewCountObj;
                }
                return null;
            });
        } catch (Exception e) {
            log.error("queryMtReview error, mtDealGroupId={}", mtDealGroupId, e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<RankingResult> queryProductRankTag(RankLabelDataRequest request) {
        try {
            CompletableFuture<Response> future = PigeonCallbackUtils.setPigeonCallback(Response.class);
            rankLabelQuery.queryProductFirstRank(request);
            return future.thenApply(res -> {
                if (res == null || !res.isSuccess() || res.getResult() == null) {
                    return null;
                }
                Object data = res.getResult();
                if (data instanceof RankingResult ) {
                    return (RankingResult) data;
                }
                return null;
            });
        } catch (Exception e) {
            log.error("queryProductRankTag error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    // todo 企业微信入口信息查询,别删,后面可能会用得到
    // @Override
    // public CompletableFuture<CorpWxFlowMaterialDTO> queryFlowEntryWxMaterial(FlowEntryWxMaterialRequest request) {
    //     try {
    //         CompletableFuture<RemoteResponse> future = PigeonCallbackUtils.setPigeonCallback(RemoteResponse.class);
    //         flowEntryWxMaterialService.getEntryWxMaterial(request);
    //         return future.thenApply(res -> {
    //             if (res == null || !res.isSuccess() || res.getData() == null) {
    //                 return null;
    //             }
    //             Object data = res.getData();
    //             if (data instanceof CorpWxFlowMaterialDTO ) {
    //                 return (CorpWxFlowMaterialDTO) data;
    //             }
    //             return null;
    //         });
    //     } catch (Exception e) {
    //         log.error(XMDLogFormat.build()
    //                 .putTag("scene", "compositeAtomService")
    //                 .putTag("method", "queryFlowEntryWxMaterial")
    //                 .message(String.format("queryFlowEntryWxMaterial error, request : %s", JsonCodec.encodeWithUTF8(request))));
    //         return CompletableFuture.completedFuture(null);
    //     }
    // }

    @Override
    public CompletableFuture<MultiSubjectTagBatchJudgeResponse> queryTagIdByProductId(MultiSubjectTagBatchJudgeRequest request) {
        CompletableFuture<MultiSubjectTagBatchJudgeResponse> future = PigeonCallbackUtils.setPigeonCallback(MultiSubjectTagBatchJudgeResponse.class);
        subjectTagJudgeService.batchJudgeMultiSubjectTag(request);
        future.exceptionally(e -> {
            log.error("queryTagIdByProductId error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return null;
        });
        return future;
    }

    @Override
    public CompletableFuture<List> queryTagNameByTagIdAsync(List<Long> tagIds) {
        CompletableFuture<List> future = PigeonCallbackUtils.setPigeonCallback(List.class);
        dealMetaTagQueryServiceFuture.queryByTagIds(tagIds);
        future.exceptionally(e -> {
            log.error("queryTagNameByTagIdAsync error, request={}", JsonCodec.encodeWithUTF8(tagIds), e);
            return null;
        });
        return future;
    }

    @Override
    public List<MetaTagDTO> queryTagNameByTagIdSync(List<Long> tagIds) {
        try {
            return dealMetaTagQueryServiceSync.queryByTagIds(tagIds);
        } catch (Exception e) {
            log.error("queryTagNameByTagIdSync error, request={}", JsonCodec.encodeWithUTF8(tagIds), e);
            return Lists.newArrayList();
        }
    }

    @Override
    public CompletableFuture<ReserveQueryResponse> queryReserveProduct(ReserveQueryRequest request) {
        CompletableFuture<ReserveQueryResponse> future = PigeonCallbackUtils.setPigeonCallback(ReserveQueryResponse.class);
        reserveThemeQueryService.query(request);
        future.exceptionally(e -> {
            log.error("queryReserveProduct error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return null;
        });
        return future;
    }

    @Override
    public CompletableFuture<AccessResponseDTO> prepareAccessIn(AccessRequestDTO request) {
        CompletableFuture<AccessResponseDTO> future = PigeonCallbackUtils.setPigeonCallback(AccessResponseDTO.class);
        cscAccessInServiceFuture.accessIn(request);
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "prepareAccessIn")
                    .message(String.format("prepareAccessIn error, request : %s", JsonCodec.encodeWithUTF8(request))));
            return null;
        }).thenApply(result -> {
            if (Objects.isNull(result)) {
                return null;
            }
            return result;
        });
    }

    @Override
    public CompletableFuture<ClientEntryDTO> preOnlineConsultUrl(ClientEntryReqDTO request) {
        CompletableFuture<ClientEntryDTO> future = PigeonCallbackUtils.setPigeonCallback(ClientEntryDTO.class);
        clientEntryService.getClientEntry(request);
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "preOnlineConsultUrl")
                    .message(String.format("preOnlineConsultUrl error, request : %s", JsonCodec.encodeWithUTF8(request))));
            return null;
        }).thenApply(result -> {
            if (Objects.isNull(result)) {
                return null;
            }
            return result;
        });
    }

    @Override
    public List<DealGroupDTO> getProductBaseInfo(List<Long> dealGroupIds, boolean isMt) {
        final QueryByDealGroupIdRequest queryCenterRequest = QueryByDealGroupIdRequestBuilder
                .builder()
                .dealGroupIds(new HashSet<>(dealGroupIds), IdTypeEnum.BIZ_PRODUCT)
                .displayShop(DealGroupDisplayShopBuilder.builder().all())
                .dealGroupId(DealGroupIdBuilder.builder().bizProductId())
                .build();
        List<DealGroupDTO> response = null;
        try {
            QueryDealGroupListResponse queryDealGroupListResponse = dealGroupQueryServiceAtom.queryByDealGroupIds(queryCenterRequest);
            return new ArrayList<>(Optional.of(queryDealGroupListResponse)
                    .map(QueryDealGroupListResponse::getData)
                    .map(QueryDealGroupListResult::getList)
                    .orElse(new ArrayList<>()));
        } catch (TException e) {
            log.error("查询中心查询失败,request={}, isMt={}", dealGroupIds, isMt, e);
        }
        return response;
    }


    @Override
    public List<DealGroupDTO> getProductBaseInfoByUnifiedId(List<Long> unifiedProductId) {
        final QueryByDealGroupIdRequest queryCenterRequest = QueryByDealGroupIdRequestBuilder
                .builder()
                .dealGroupIds(new HashSet<>(unifiedProductId), IdTypeEnum.MT)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .build();
        List<DealGroupDTO> response = null;
        try {
            QueryDealGroupListResponse queryDealGroupListResponse = dealGroupQueryServiceAtom.queryByDealGroupIds(queryCenterRequest);
            return new ArrayList<>(Optional.of(queryDealGroupListResponse)
                    .map(QueryDealGroupListResponse::getData)
                    .map(QueryDealGroupListResult::getList)
                    .orElse(new ArrayList<>()));
        } catch (TException e) {
            log.error("统一ID查询中心查询失败,request:{}", unifiedProductId, e);
        }
        return response;
    }

    @Override
    public CompletableFuture<Map<Long, List<DisplayTagDto>>> queryShopTags(FindSceneDisplayTagRequest request) {
        CompletableFuture<com.sankuai.athena.biz.Response> future = PigeonCallbackUtils.setPigeonCallback(com.sankuai.athena.biz.Response.class);
        poiTagDisplayRPCService.findSceneDisplayTagOfDp(request);
        try {
            return future.thenApply(result -> {
                if (Objects.isNull(result) || !result.isSuccess()) {
                    return null;
                }
                return (Map<Long, List<DisplayTagDto>>) result.getData();
            });
        } catch (Exception e) {
            log.error("queryShopTags error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<List<ObjectGuaranteeTagDTO>> batchQueryGuaranteeTag(SessionContextDTO sessionContext, BatchQueryGuaranteeTagRequest request) {
        try {
            guaranteeQueryService.batchQueryGuaranteeTag(sessionContext, request);
            CompletableFuture<com.sankuai.nib.price.operation.api.common.response.Response<List<ObjectGuaranteeTagDTO>>> future = ThriftAsyncUtils.getThriftFuture();
            return future.thenApply(result -> {
                if (Objects.isNull(result) || result.isNotSuccess()) {
                    return null;
                }
                return result.getData();
            });
        } catch (Exception e) {
            log.error("batchQueryGuaranteeTag error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<Map<Long, Long>> batchPlatformProductId(BizProductIdConvertRequest request) {
        try {
            idService.convertBizProductIdsToProductIds(request);
            CompletableFuture<BizProductIdConvertResponse> future = ThriftAsyncUtils.getThriftFuture();
            return future.thenApply(result -> {
                        if (Objects.isNull(result) || !result.isSuccess()) {
                            return Maps.newHashMap();
                        }
                        return result.getBizProductIdConvertResult();
                    });
        } catch (Exception e) {
            log.error("batchPlatformProductId error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }
}
