package com.sankuai.dzshoppingguide.product.detail.application.builder.review;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.review.MtReserveReviewFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.review.MtReserveReviewReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.CommonModuleStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.review.vo.MtReserveReview;
import org.apache.commons.collections.CollectionUtils;

/**
 * @Author: litengfei04
 * @Date: 2025/2/5 17:45
 */
@Builder(
        moduleKey = ModuleKeyConstants.MT_RESERVE_REVIEW_MODULE_V1,
        startFetcher = CommonModuleStarter.class,
        dependentFetchers = {MtReserveReviewFetcher.class}
)
public class MtReserveReviewModuleBuilder extends BaseBuilder<MtReserveReview> {
    @Override
    public MtReserveReview doBuild() {
        // 点评侧直接返回
        if (this.request.getClientTypeEnum().isDpClientType()) {
            return null;
        }
        MtReserveReview mtReserveReview = new MtReserveReview();
        MtReserveReviewReturnValue mtReserveReviewReturnValue = getDependencyResult(MtReserveReviewFetcher.class);
        if (mtReserveReviewReturnValue == null || mtReserveReviewReturnValue.getData() == null || CollectionUtils.isEmpty(mtReserveReviewReturnValue.getData().getContentThemeDTO())) {
            return null;
        }
        mtReserveReview.setContent(JSON.toJSONString(mtReserveReviewReturnValue.getData()));
        return mtReserveReview;
    }
}
