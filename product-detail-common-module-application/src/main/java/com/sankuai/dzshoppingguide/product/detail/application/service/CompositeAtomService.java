package com.sankuai.dzshoppingguide.product.detail.application.service;

import com.dianping.csc.center.engine.access.dto.AccessRequestDTO;
import com.dianping.csc.center.engine.access.dto.AccessResponseDTO;
import com.dianping.deal.tag.dto.MetaTagDTO;
import com.dianping.deal.tag.dto.MultiSubjectTagBatchJudgeRequest;
import com.dianping.deal.tag.dto.MultiSubjectTagBatchJudgeResponse;
import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import com.sankuai.dzcard.navigation.api.dto.QueryCardInfoDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryDiscountCardReq;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.mdp.dzrank.scenes.api.request.RankLabelDataRequest;
import com.sankuai.mdp.dzrank.scenes.api.response.dto.RankingResult;
import com.sankuai.meituan.shangou.standardquery.thrift.command.ListSgBrandByIdsCommand;
import com.sankuai.meituan.shangou.standardquery.thrift.domain.BrandVo;
import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import com.sankuai.zdc.tag.apply.dto.FindSceneDisplayTagRequest;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-02-13
 * @desc
 */
public interface CompositeAtomService {
    /**
     * 查询品牌信息
     * @param command
     * @return
     */
    CompletableFuture<BrandVo> queryBrandInfoByBrandId(ListSgBrandByIdsCommand command);

    /**
     * 查询折扣卡信息
     * @param request
     * @return
     */
    CompletableFuture<QueryCardInfoDTO> queryDiscountCardInfo(QueryDiscountCardReq request);

    /**
     * 查询团单是否支持在线预约
     * @param dpDealGroupId
     * @return
     */
    CompletableFuture<Boolean> queryDealGroupOnlineReserve(long dpDealGroupId);

    /**
     * 查询点评团单评价数
     * @param dpDealGroupId
     * @return
     */
    CompletableFuture<List<ReviewStarDistributionDTO>> queryDpReview(long dpDealGroupId);

    /**
     * 查询美团团单评价数
     * @param mtDealGroupId
     * @return
     */
    CompletableFuture<ReviewCount> queryMtReview(long mtDealGroupId);

    /**
     * 团购榜单信息查询
     * @param request
     * @return
     */
    CompletableFuture<RankingResult> queryProductRankTag(RankLabelDataRequest request);

    /**
     * 查询企业微信信息
     * @param request
     * @return
     */
    // CompletableFuture<CorpWxFlowMaterialDTO> queryFlowEntryWxMaterial(FlowEntryWxMaterialRequest request);

    /**
     * 查询预订信息
     * @param request 预订信息查询入参
     * @return 预订信息查询结果
     */
    CompletableFuture<ReserveQueryResponse> queryReserveProduct(ReserveQueryRequest request);

    /**
     * 查询商品基础信息
     * @param dealGroupIds
     * @param isMt
     * @return
     */
    List<DealGroupDTO> getProductBaseInfo(List<Long> dealGroupIds, boolean isMt);

    /**
     * 根据统一ID查询商品基础信息
     * @param unifiedProductId
     * @return
     */
    List<DealGroupDTO> getProductBaseInfoByUnifiedId(List<Long> unifiedProductId);

    /**
     * 根据预订商品id查询对应的映射的团单三级分类id
     * @param request
     * @return
     */
    CompletableFuture<MultiSubjectTagBatchJudgeResponse> queryTagIdByProductId(MultiSubjectTagBatchJudgeRequest request);

    /**
     * 根据查询的团单三级分类id获取对应的三级分类名称
     * @param tagIds
     * @return
     */
    CompletableFuture<List> queryTagNameByTagIdAsync(List<Long> tagIds);

    /**
     * 商品三级分类信息查询同步接口
     * @param tagIds
     * @return
     */
    List<MetaTagDTO> queryTagNameByTagIdSync(List<Long> tagIds);

    /**
     * 查询门店标签
     * @param request 门店标签查询入参
     * @return 门店标签查询结果
     */
    CompletableFuture<Map<Long, List<DisplayTagDto>>> queryShopTags(FindSceneDisplayTagRequest request);

    /**
     * 批量查询保障标签
     * @param sessionContext 用户信息
     * @param request 团单id
     * @return 保障标签
     */
    CompletableFuture<List<ObjectGuaranteeTagDTO>> batchQueryGuaranteeTag(SessionContextDTO sessionContext, BatchQueryGuaranteeTagRequest request);

    /**
     * 太平洋智能客服
     * @param request
     * @return
     */
    CompletableFuture<AccessResponseDTO> prepareAccessIn(AccessRequestDTO request);

    /**
     * 获取在线咨询的URL
     * @param request
     * @return
     */
    CompletableFuture<ClientEntryDTO> preOnlineConsultUrl(ClientEntryReqDTO request);

    /**
     * 批量查询平台商品id
     * @param request 点评团单ID
     * @return 平台商品id
     */
    CompletableFuture<Map<Long, Long>> batchPlatformProductId(BizProductIdConvertRequest request);
}
