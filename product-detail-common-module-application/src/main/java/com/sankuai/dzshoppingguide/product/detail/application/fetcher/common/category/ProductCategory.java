package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;

import lombok.Getter;

/**
 * @Author: guangyujie
 * @Date: 2025/2/11 19:45
 */
@Getter
public class ProductCategory extends FetcherReturnValueDTO {

    private final int productFirstCategoryId;

    private final int productSecondCategoryId;

    /**
     * 不一定是三级分类id,准确来说应该是末级分类id
     */
    private final int productThirdCategoryId;

    public ProductCategory(final int productFirstCategoryId,
                           final int productSecondCategoryId,
                           final int productThirdCategoryId) {
        this.productFirstCategoryId = productFirstCategoryId;
        this.productSecondCategoryId = productSecondCategoryId;
        this.productThirdCategoryId = productThirdCategoryId;
    }
}
