package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ProductBestShop;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ProductBestShopFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.product.related.shop.ProductRelatedShop;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.product.related.shop.ProductRelatedShopFetcher;
import com.sankuai.dzshoppingguide.product.detail.domain.idmapper.MapperCacheWrapper;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/12 11:11
 */
@Fetcher(
        previousLayerDependencies = {
                ProductRelatedShopFetcher.class,
                ProductBestShopFetcher.class
        }
)
@Slf4j
public class ShopIdMapperFetcher extends NormalFetcherContext<ShopIdMapper> {


    public final static String FAST_BEST_SHOP = "FAST_BEST_SHOP";

    @Autowired
    private MapperCacheWrapper mapperCacheWrapper;

    private long dpShopId;
    private long mtShopId;

    @Override
    protected CompletableFuture<ShopIdMapper> doFetch() {
        //准备参数
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        ProductBestShop bestShopInfo = getDependencyResult(ProductBestShopFetcher.class);
        ProductRelatedShop productRelatedShop = getDependencyResult(ProductRelatedShopFetcher.class);

        // 如果不存在的话进行后面的查询的流程,并且需要进行对应的赋值
        List<Long> dpRelatedShopIds = Optional.ofNullable(productRelatedShop)
                .map(ProductRelatedShop::getDpDisplayShopIds)
                .orElse(Lists.newArrayList());
        List<Long> mtRelatedShopIds = Optional.ofNullable(productRelatedShop)
                .map(ProductRelatedShop::getMtDisplayShopIds)
                .orElse(Lists.newArrayList());

        if (request.getPoiId() > 0) {//如果poi存在，则校验合法性
            dpShopId = isMt ? mapperCacheWrapper.fetchDpShopId(request.getPoiId()) : request.getPoiId();
            // 有来源门店，但是来源门店和团单不绑定，则用bestShopInfo进行兜底，若bestShopInfo也没数据，则用查询中心兜底
            if(CollectionUtils.isNotEmpty(dpRelatedShopIds) && !dpRelatedShopIds.contains(dpShopId)) {
                dpShopId = Optional.ofNullable(bestShopInfo).map(ProductBestShop::getDpShopId).orElse(dpRelatedShopIds.get(0));
            }
            mtShopId = mapperCacheWrapper.fetchMtShopId(dpShopId);
        } else {//如果poi不存在
            if(request.getProductTypeEnum() == ProductTypeEnum.RESERVE) {
                //针对预订直接取适用门店第一个，因为预订只有一个适用门店
                dpShopId = CollectionUtils.isEmpty(dpRelatedShopIds) ? 0 : dpRelatedShopIds.get(0);
                mtShopId = mapperCacheWrapper.fetchMtShopId(dpShopId);
            }
            if(request.getProductTypeEnum() == ProductTypeEnum.DEAL) {
                //如果是团购，则取bestShop的计算逻辑
                dpShopId = Optional.ofNullable(bestShopInfo).map(ProductBestShop::getDpShopId).orElse(0L);
                if (dpShopId == 0 || CollectionUtils.isNotEmpty(dpRelatedShopIds) && !dpRelatedShopIds.contains(dpShopId)) {
                    dpShopId = CollectionUtils.isEmpty(dpRelatedShopIds) ? 0 : dpRelatedShopIds.get(0);
                }
                //并且直接做id转换
                mtShopId = mapperCacheWrapper.fetchMtShopId(dpShopId);
            }
        }
        //检查并兜底
        checkBestShopResultAndSetDefaultValue(dpRelatedShopIds);
        if (request.getPoiId() <= 0) {
            // 修改入参的poiId，防止后续有人用错
            request.setPoiId(isMt ? mtShopId : dpShopId);
        }
        return CompletableFuture.completedFuture(new ShopIdMapper(dpShopId, mtShopId));
    }

    private void checkBestShopResultAndSetDefaultValue(List<Long> relatedShops) {
        if (dpShopId <= 0 && mtShopId <= 0) {
            //如果点评和美团门店Id都<=0，则走兜底取适用门店第一个
            Cat.logEvent(FAST_BEST_SHOP, "all_shopId_result_empty");
            if ( CollectionUtils.isNotEmpty(relatedShops)) {
                dpShopId = relatedShops.get(0);
            }
            if (dpShopId <= 0) {
                //如果适用门店也是空则报错
                Cat.logEvent(FAST_BEST_SHOP, "related_shopIds_empty");
            } else {
                long mtPoiId = mapperCacheWrapper.fetchMtShopId(dpShopId);
                if (mtPoiId <= 0) {
                    Cat.logEvent(FAST_BEST_SHOP, "related_mtShopId_is_null");
                }
                mtShopId = mtPoiId;
            }
        } else if (dpShopId > 0 && mtShopId <= 0) {
            //如果有点评门店Id，但缺失美团门店Id（一般不可能出现）
            Cat.logEvent(FAST_BEST_SHOP, "mt_shopId_result_empty");
            long mtPoiId = mapperCacheWrapper.fetchMtShopId(dpShopId);
            if (mtPoiId <= 0) {
                Cat.logEvent(FAST_BEST_SHOP, "related_mtShopId_is_null");
                log.error("mtShopId is null,dpShopId:{}", dpShopId, new ShopIdMapperException("mtShopId is null,dpShopId:" + dpShopId));
            }
            mtShopId = mtPoiId;
        } else if ( dpShopId <= 0 ) {
            //如果有美团门店Id，但缺失点评门店Id（一般不可能出现）
            Cat.logEvent(FAST_BEST_SHOP, "dp_shopId_result_empty");
            long dpPoiId = mapperCacheWrapper.fetchDpShopId(mtShopId);
            if (dpPoiId <= 0) {
                Cat.logEvent(FAST_BEST_SHOP, "related_dpShopId_is_null");
                log.error("dpShopId is null,mtShopId:{}", mtShopId, new ShopIdMapperException("dpShopId is null,mtShopId:" + mtShopId));
            }
            dpShopId = dpPoiId;
        }
    }
}
