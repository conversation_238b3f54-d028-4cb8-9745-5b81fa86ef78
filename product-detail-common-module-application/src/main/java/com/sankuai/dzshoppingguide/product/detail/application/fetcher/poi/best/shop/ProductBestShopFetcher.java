package com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop;

import com.dianping.general.unified.search.api.common.dto.ExtendSearchOption;
import com.dianping.general.unified.search.api.common.dto.SearchConditionUnit;
import com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService;
import com.dianping.general.unified.search.api.productshopsearch.dto.LocationInfoDTO;
import com.dianping.general.unified.search.api.productshopsearch.dto.ProductShopSearchDTO;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.*;
import com.dianping.general.unified.search.api.productshopsearch.dto.unit.CollapseUnit;
import com.dianping.general.unified.search.api.productshopsearch.dto.unit.RetrievalUnit;
import com.dianping.general.unified.search.api.productshopsearch.dto.unit.SortUnit;
import com.dianping.general.unified.search.api.productshopsearch.enums.*;
import com.dianping.general.unified.search.api.productshopsearch.request.GeneralProductShopSearchRequest;
import com.dianping.general.unified.search.api.productshopsearch.response.GeneralProductShopSearchResponse;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.gpscityid.GpsCityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.gpscityid.GpsCityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:18
 * 大家勿直接使用本Fetcher，请依赖ShopIdMapperFetcher
 */
@Fetcher(
        previousLayerDependencies = {
                CityIdMapperFetcher.class,
                GpsCityIdMapperFetcher.class,
                DealGroupIdMapperFetcher.class
        }
)
@Slf4j
public class ProductBestShopFetcher extends NormalFetcherContext<ProductBestShop> {

    @MdpPigeonClient(url = "com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private GeneralProductShopSearchService generalProductShopSearchService;


    int dpCityId;
    int dpGpsCityId;
    long dpDealGroupId;

    @Override
    protected CompletableFuture<ProductBestShop> doFetch() {
        try {
            if (request.getProductType() != ProductTypeEnum.DEAL.getCode()) {
                return CompletableFuture.completedFuture(null);
            }

            CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
            GpsCityIdMapper gpsCityIdMapper = getDependencyResult(GpsCityIdMapperFetcher.class);
            DealGroupIdMapper dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
            dpCityId = Optional.ofNullable(cityIdMapper).map(CityIdMapper::getDpCityId).orElse(0);
            dpGpsCityId = Optional.ofNullable(gpsCityIdMapper).map(GpsCityIdMapper::getDpGpsCityId).orElse(0);
            dpDealGroupId = Optional.ofNullable(dealGroupIdMapper).map(DealGroupIdMapper::getDpDealGroupId).orElse(0L);

            return searchBestShop(buildNewQueryRequest()).thenApply(res -> {
                if (res == null || !res.isSuccess() || CollectionUtils.isEmpty(res.getResult())) {
                    return null;
                }
                List<ProductShopSearchDTO> result = res.getResult();
                Long dpBestShopId = result.stream().filter(Objects::nonNull).map(ProductShopSearchDTO::getShopId).findFirst().orElse(0L);
                ProductBestShop productBestShop = new ProductBestShop();
                productBestShop.setDpShopId(dpBestShopId);
                productBestShop.setTotalCount(res.getTotalHits());
                return productBestShop;
            });
        } catch (Exception e) {
            log.error("ProductBestShopFetcher error,request:{}", JsonCodec.encodeWithUTF8(request),e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private CompletableFuture<GeneralProductShopSearchResponse> searchBestShop(GeneralProductShopSearchRequest request) {
        CompletableFuture<GeneralProductShopSearchResponse> future = PigeonCallbackUtils.setPigeonCallback(GeneralProductShopSearchResponse.class);
        generalProductShopSearchService.searchProductShops(request);
        future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "ProductBestShopFetcher")
                    .putTag("method", "searchBestShop")
                    .message(String.format("searchBestShop error, request : %s, error message:%s", JsonCodec.encodeWithUTF8(request),e)));
            return null;
        });
        return future;
    }


    private GeneralProductShopSearchRequest buildNewQueryRequest() {
        GeneralProductShopSearchRequest bestShopRequest = new GeneralProductShopSearchRequest();
        bestShopRequest.setIdPlatform(ProductShopSearchIdTypeEnum.DP_BP);
        BaseSearchOption baseSearchOption = new BaseSearchOption();
        baseSearchOption.setProductBizTypes(Lists.newArrayList(ProductBizTypeEnum.DEALGROUP));

        baseSearchOption.setProductIds(Lists.newArrayList(dpDealGroupId));

        bestShopRequest.setBaseSearchOption(baseSearchOption);

        // 团购可被门店展示和核销
        baseSearchOption.setShopCanDisplay(true);
        baseSearchOption.setShopCanVerify(true);

        SortOption sortOption = new SortOption();
        SortUnit sortUnit = new SortUnit();
        sortUnit.setSortField(ProductShopSortFieldEnum.SHOP_DISTANCE.getCode());
        sortUnit.setSortOrder(ProductShopSortOrderEnum.ASC);
        sortOption.setSortUnits(Lists.newArrayList(sortUnit));
        bestShopRequest.setSortOption(sortOption);

        CollapseOption collapseOption = new CollapseOption();
        CollapseUnit collapseUnit = new CollapseUnit();
        collapseUnit.setCollapseField(ProductShopCollapseFieldEnum.STANDARD_SPU_ID.getCode());
        collapseUnit.setCollapseType(ProductShopCollapseTypeEnum.SORTED_FIELD_FIRST_RECORD);
        collapseOption.setCollapseUnits(Lists.newArrayList(collapseUnit));
        bestShopRequest.setCollapseOption(collapseOption);

        PageOption pageOption = new PageOption();
        pageOption.setPageNo(1);
        pageOption.setPageSize(100);
        bestShopRequest.setPageOption(pageOption);

        ExtendSearchOption extendSearchOption = new ExtendSearchOption();
        List<SearchConditionUnit> andConditions = Lists.newArrayList();
        // 定位城市ID
        if (dpGpsCityId > 0) {
            SearchConditionUnit positionCityId = new SearchConditionUnit();
            positionCityId.setConditionField(ProductShopSearchFieldEnum.POSITION_CITY_ID.getName());
            positionCityId.setValues(Lists.newArrayList(String.valueOf(dpGpsCityId)));
            andConditions.add(positionCityId);
        }


        // 首页城市ID
        if (dpCityId > 0) {
            SearchConditionUnit homeCityId = new SearchConditionUnit();
            homeCityId.setConditionField(ProductShopSearchFieldEnum.HOME_PAGE_CITY_ID.getName());
            homeCityId.setValues(Lists.newArrayList(String.valueOf(dpCityId)));
            andConditions.add(homeCityId);
        }

        extendSearchOption.setAndConditions(andConditions);
        bestShopRequest.setExtendSearchOption(extendSearchOption);

        RetrievalOption retrievalOption = new RetrievalOption();
        RetrievalUnit retrievalUnit = new RetrievalUnit();
        retrievalUnit.setRetrievalExtendField(ProductShopRetrievalExtendFieldEnum.SHOP_DISTANCE_IN_METER.getCode());
        retrievalOption.setRetrievalUnits(Lists.newArrayList(retrievalUnit));
        bestShopRequest.setRetrievalOption(retrievalOption);

        LocationInfoDTO locationInfoDTO = new LocationInfoDTO();
        locationInfoDTO.setLat(BigDecimal.valueOf(request.getUserLat()));
        locationInfoDTO.setLng(BigDecimal.valueOf(request.getUserLng()));
        bestShopRequest.setLocationInfo(locationInfoDTO);

        return bestShopRequest;
    }
}
