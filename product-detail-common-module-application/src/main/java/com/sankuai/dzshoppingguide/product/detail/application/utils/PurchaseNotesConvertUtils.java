package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNote;
import com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO;
import com.sankuai.general.product.query.center.client.enums.PurchaseNoteSceneEnum;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class PurchaseNotesConvertUtils {


    /**
     * 根据枚举获取对应type的购买须知
     *
     * @param data
     * @param type
     * @return
     */
    public static PurchaseNoteDTO getPurchaseByEnum(ProductPurchaseNote data, PurchaseNoteSceneEnum type) {
        if (data == null || CollectionUtils.isEmpty(data.getPurchaseNotes()) ) {
            return null;
        }
        List<PurchaseNoteDTO> purchaseNotes = data.getPurchaseNotes();
        for (PurchaseNoteDTO purchaseNote : purchaseNotes) {
            if (purchaseNote.getSceneEnum() == type) {
                return purchaseNote;
            }
        }
        return null;
    }

    /**
     * 构建购买须知模块
     * @param data
     * @param type
     */
    public static PurchaseNoteDTO buildInstruction(ProductPurchaseNote data, PurchaseNoteSceneEnum type) {
        return getPurchaseByEnum(data, type);
    }

    /**
     * 构建须知条文案信息
     * @param data
     * @param type
     * @param reminderInfo
     */
    public static void buildReminderInfo(ProductPurchaseNote data, PurchaseNoteSceneEnum type, List<String> reminderInfo) {
        PurchaseNoteDTO purchaseNotes = getPurchaseByEnum(data, type);
        if (purchaseNotes != null) {
            processPurchaseNote(purchaseNotes, reminderInfo);
        }
    }

    /**
     * 拼装须知信息
     *
     * @param purchaseNote
     * @param reminderInfo
     */
    public static void processPurchaseNote(PurchaseNoteDTO purchaseNote, List<String> reminderInfo) {
        List<StandardDisplayModuleDTO> modules = purchaseNote.getModules();
        if (modules == null || modules.isEmpty()) {
            return;
        }
        modules.stream().flatMap(module -> module.getItems().stream())
                .filter(item -> item != null && item.getItemValues() != null)
                .map(item -> item.getItemValues().stream()
                        .filter(Objects::nonNull)
                        .map(StandardDisplayValueDTO::getValue)
                        .collect(Collectors.joining()))
                .forEach(reminderInfo::add);
    }
}
