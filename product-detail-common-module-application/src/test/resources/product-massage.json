{"code": 0, "message": "成功", "data": {"list": [{"dpDealGroupId": 1042034504, "mtDealGroupId": 1042034504, "basic": {"categoryId": 304, "title": "浴资票|全量测试团单", "brandName": "304分类数据池补充业务测试用门店", "titleDesc": "仅售110元，价值111元浴资票|全量测试团单！", "beginSaleDate": "2025-05-28 19:24:10", "endSaleDate": "2026-05-30 19:07:51", "status": 1, "saleChannel": 0, "salePlatform": 3, "sourceId": 102, "tradeType": 3, "platformCategoryId": 80304, "addTime": "2025-05-28 19:24:13", "updateTime": "2025-06-03 11:23:09"}, "image": {"defaultPicPath": "https://p0.inf.test.sankuai.com/dpmerchantpic/8e969db60ee8d08766b79fcca9235063171436.jpg", "allPicPaths": "https://p0.inf.test.sankuai.com/dpmerchantpic/8e969db60ee8d08766b79fcca9235063171436.jpg", "videoPath": null, "videoCoverPath": null, "videoSize": null, "extendVideos": null, "allVideos": null}, "category": {"categoryId": 304, "serviceType": "浴资票", "serviceTypeId": 181, "platformCategoryId": 80304}, "bgBu": null, "serviceProject": {"title": "团购详情", "salePrice": "110.0", "marketPrice": "111.0", "mustGroups": [{"groups": [{"skuId": 0, "categoryId": 895, "name": "浴资票全部可享", "amount": 1, "marketPrice": null, "status": 10, "attrs": [{"metaAttrId": 537, "attrName": "content", "chnName": "浴资票补充说明", "attrValue": "浴资票全部可享补充说明浴资票补充说明", "rawAttrValue": "浴资票全部可享补充说明浴资票补充说明", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 355829, "attrName": "ApplicableDuration", "chnName": "适用时长(小时)", "attrValue": "24小时", "rawAttrValue": "24", "unit": "小时", "valueType": 500, "sequence": 0}, {"metaAttrId": 2730, "attrName": "skuCateId", "chnName": "项目分类", "attrValue": "895", "rawAttrValue": "895", "unit": null, "valueType": 402, "sequence": 0}, {"metaAttrId": 10048055, "attrName": "AvailableTimePeriod3", "chnName": "可用时段", "attrValue": "营业时间内全部可用", "rawAttrValue": "营业时间内全部可用", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10048063, "attrName": "AdultPopulation2", "chnName": "成人", "attrValue": "1", "rawAttrValue": "1", "unit": null, "valueType": 401, "sequence": 0}, {"metaAttrId": 10048064, "attrName": "ChildCount2", "chnName": "儿童", "attrValue": "1", "rawAttrValue": "1", "unit": null, "valueType": 401, "sequence": 0}]}, {"skuId": 0, "categoryId": 2201279, "name": "附赠服务", "amount": 1, "marketPrice": null, "status": 10, "attrs": [{"metaAttrId": 537, "attrName": "content", "chnName": "亮点描述", "attrValue": "门票附赠服务亮点描述", "rawAttrValue": "门票附赠服务亮点描述", "unit": null, "valueType": 500, "sequence": 0}]}]}], "optionGroups": [{"optionalCount": 1, "groups": [{"skuId": 0, "categoryId": 2105655, "name": "自助餐1", "amount": 2, "marketPrice": null, "status": 10, "attrs": [{"metaAttrId": 2730, "attrName": "skuCateId", "chnName": "项目分类", "attrValue": "2105655", "rawAttrValue": "2105655", "unit": null, "valueType": 402, "sequence": 0}, {"metaAttrId": 10084523, "attrName": "Selfservicediningcontent", "chnName": "自助餐食内容", "attrValue": "海鲜、中式炒菜、粉面主食、养生汤粥、小吃甜品、水果", "rawAttrValue": "海鲜、中式炒菜、粉面主食、养生汤粥、小吃甜品、水果", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10084524, "attrName": "Selfservicediningduration", "chnName": "自助用餐时长", "attrValue": "90分钟", "rawAttrValue": "90分钟", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 537, "attrName": "content", "chnName": "餐食亮点描述", "attrValue": "自助餐1餐食亮点描述", "rawAttrValue": "自助餐1餐食亮点描述", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10164647, "attrName": "selfservicedininghours", "chnName": "自助餐供应时间", "attrValue": "[{\"timeframe1\":\"1\",\"selfservicerestaurantclosingtime\":\"08:00\",\"selfservicerestaurantstarttime\":\"07:00\"},{\"timeframe1\":\"2\",\"selfservicerestaurantclosingtime\":\"20:00\",\"selfservicerestaurantstarttime\":\"19:00\"}]", "rawAttrValue": "[{\"timeframe1\":\"1\",\"selfservicerestaurantclosingtime\":\"08:00\",\"selfservicerestaurantstarttime\":\"07:00\"},{\"timeframe1\":\"2\",\"selfservicerestaurantclosingtime\":\"20:00\",\"selfservicerestaurantstarttime\":\"19:00\"}]", "unit": null, "valueType": 300, "sequence": 0}]}, {"skuId": 0, "categoryId": 2105655, "name": "自助餐2", "amount": 3, "marketPrice": null, "status": 10, "attrs": [{"metaAttrId": 2730, "attrName": "skuCateId", "chnName": "项目分类", "attrValue": "2105655", "rawAttrValue": "2105655", "unit": null, "valueType": 402, "sequence": 0}, {"metaAttrId": 10084523, "attrName": "Selfservicediningcontent", "chnName": "自助餐食内容", "attrValue": "海鲜、中式炒菜、粉面主食、养生汤粥、小吃甜品、水果", "rawAttrValue": "海鲜、中式炒菜、粉面主食、养生汤粥、小吃甜品、水果", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10084524, "attrName": "Selfservicediningduration", "chnName": "自助用餐时长", "attrValue": "60分钟", "rawAttrValue": "60分钟", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 537, "attrName": "content", "chnName": "餐食亮点描述", "attrValue": "自助餐2餐食亮点描述", "rawAttrValue": "自助餐2餐食亮点描述", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10164647, "attrName": "selfservicedininghours", "chnName": "自助餐供应时间", "attrValue": "[{\"timeframe1\":\"1\",\"selfservicerestaurantclosingtime\":\"14:00\",\"selfservicerestaurantstarttime\":\"12:00\"}]", "rawAttrValue": "[{\"timeframe1\":\"1\",\"selfservicerestaurantclosingtime\":\"14:00\",\"selfservicerestaurantstarttime\":\"12:00\"}]", "unit": null, "valueType": 300, "sequence": 0}]}]}, {"optionalCount": 1, "groups": [{"skuId": 0, "categoryId": 895, "name": "浴资票1", "amount": 1, "marketPrice": null, "status": 10, "attrs": [{"metaAttrId": 537, "attrName": "content", "chnName": "浴资票补充说明", "attrValue": "浴资票1补充说明浴资票补充说明浴资票补充说明", "rawAttrValue": "浴资票1补充说明浴资票补充说明浴资票补充说明", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 355829, "attrName": "ApplicableDuration", "chnName": "适用时长(小时)", "attrValue": "6小时", "rawAttrValue": "6", "unit": "小时", "valueType": 500, "sequence": 0}, {"metaAttrId": 2730, "attrName": "skuCateId", "chnName": "项目分类", "attrValue": "895", "rawAttrValue": "895", "unit": null, "valueType": 402, "sequence": 0}, {"metaAttrId": 10048055, "attrName": "AvailableTimePeriod3", "chnName": "可用时段", "attrValue": "部分时间可用", "rawAttrValue": "部分时间可用", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10048056, "attrName": "AvailableTimePeriodName", "chnName": "可用时段名称", "attrValue": "工作日", "rawAttrValue": "工作日", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10048057, "attrName": "TimeRange3", "chnName": "时间范围", "attrValue": "1、2、3、5、6、7", "rawAttrValue": "1、2、3、5、6、7", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10048063, "attrName": "AdultPopulation2", "chnName": "成人", "attrValue": "2", "rawAttrValue": "2", "unit": null, "valueType": 401, "sequence": 0}, {"metaAttrId": 10048064, "attrName": "ChildCount2", "chnName": "儿童", "attrValue": "0", "rawAttrValue": "0", "unit": null, "valueType": 401, "sequence": 0}]}, {"skuId": 0, "categoryId": 895, "name": "浴资票2", "amount": 1, "marketPrice": null, "status": 10, "attrs": [{"metaAttrId": 537, "attrName": "content", "chnName": "浴资票补充说明", "attrValue": "浴资票2补充说明浴资票补充说明", "rawAttrValue": "浴资票2补充说明浴资票补充说明", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 355829, "attrName": "ApplicableDuration", "chnName": "适用时长(小时)", "attrValue": "6小时", "rawAttrValue": "6", "unit": "小时", "valueType": 500, "sequence": 0}, {"metaAttrId": 2730, "attrName": "skuCateId", "chnName": "项目分类", "attrValue": "895", "rawAttrValue": "895", "unit": null, "valueType": 402, "sequence": 0}, {"metaAttrId": 10048055, "attrName": "AvailableTimePeriod3", "chnName": "可用时段", "attrValue": "部分时间可用", "rawAttrValue": "部分时间可用", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10048056, "attrName": "AvailableTimePeriodName", "chnName": "可用时段名称", "attrValue": "工作日", "rawAttrValue": "工作日", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10048057, "attrName": "TimeRange3", "chnName": "时间范围", "attrValue": "1、3、5、7", "rawAttrValue": "1、3、5、7", "unit": null, "valueType": 500, "sequence": 0}, {"metaAttrId": 10048063, "attrName": "AdultPopulation2", "chnName": "成人", "attrValue": "0", "rawAttrValue": "0", "unit": null, "valueType": 401, "sequence": 0}, {"metaAttrId": 10048064, "attrName": "ChildCount2", "chnName": "儿童", "attrValue": "2", "rawAttrValue": "2", "unit": null, "valueType": 401, "sequence": 0}, {"metaAttrId": 10519941, "attrName": "ChildInstructions", "chnName": "儿童判断标准", "attrValue": "判断标准判断标准判断标准", "rawAttrValue": "判断标准判断标准判断标准", "unit": null, "valueType": 500, "sequence": 0}]}]}], "structType": "uniform-structure-table"}, "channel": {"channelId": 3, "channelEn": "joy", "channelCn": "休闲娱乐", "channelGroupId": 2, "channelGroupEn": "general", "channelGroupCn": "到店综合"}, "attrs": [{"name": "bathing_pool", "value": ["儿童池", "桑拿浴", "温泉浴", "按摩浴", "火龙浴", "碳酸浴", "牛奶浴", "电气浴", "岩盘浴"], "source": 0, "cnName": null, "type": null}, {"name": "RestArea", "value": ["休息大厅", "懒人沙发区", "格子洞", "窑洞", "环形休息区", "榻榻米", "太空休息舱"], "source": 0, "cnName": null, "type": null}, {"name": "disposable_materials", "value": ["一次性浴巾", "一次性浴服", "一次性剃须刀", "一次性牙刷", "一次性牙膏", "一次性内裤", "一次性胸垫", "隐形眼镜盒", "浴巾(高温消毒)", "浴服(高温消毒)"], "source": 0, "cnName": null, "type": null}, {"name": "free_bathing_supplies", "value": ["提供免费洗浴用品"], "source": 0, "cnName": null, "type": null}, {"name": "free_facilities", "value": ["提供免费设施"], "source": 0, "cnName": null, "type": null}, {"name": "sauna", "value": ["湿蒸", "干蒸", "盐蒸", "蒙古包", "玉石房", "冰雪房", "黄土房", "氧气房", "溶洞汗蒸"], "source": 0, "cnName": null, "type": null}, {"name": "leisure", "value": ["棋牌室", "PS/电竞", "游乐区", "水上乐园", "影院设备", "健身房", "图书借阅", "桌游室"], "source": 0, "cnName": null, "type": null}, {"name": "bathing_supplies", "value": ["漱口水", "洗发水", "沐浴露", "卸妆水", "洗面奶", "吹风机", "护肤品", "戴森吹风机", "卡诗洗发水", "香奈儿洗面奶", "兰蔻护肤品", "娇韵诗护肤品", "雅漾护肤品", "大牌洗护用品"], "source": 0, "cnName": null, "type": null}], "rule": {"buyRule": {"maxPerUser": 0, "minPerUser": 1}, "useRule": {"receiptEffectiveDate": {"receiptDateType": 1, "receiptValidDays": 90, "receiptBeginDate": "2025-06-04 19:40:03", "receiptEndDate": "2025-09-02 23:59:59", "showText": "购买后90天内有效"}, "availableDate": {"availableType": 1, "specifiedDurationDateList": [{"availableDateRangeDTOS": [{"from": "2025-05-28 00:00:00", "to": "2025-06-01 00:00:00"}], "availableTimeRangePerDay": [{"from": "1970-01-01 00:00:00", "to": "1970-01-01 23:00:00"}]}], "cycleAvailableDateList": null}, "disableDate": null}, "bookingRule": null, "refundRule": {"supportRefundType": 1, "supportOverdueAutoRefund": true}}, "displayShopInfo": {"dpDisplayShopIds": [608271256], "mtDisplayShopIds": [608271256]}, "verifyShopInfo": null, "tags": [{"id": 100090859, "tagName": "含浴资或搓澡"}, {"id": 100053227, "tagName": "通用"}, {"id": 100054245, "tagName": "含浴资票"}, {"id": 100054242, "tagName": "成人"}, {"id": 10014354, "tagName": "1"}, {"id": 10014401, "tagName": "测试"}, {"id": 100208256, "tagName": "测试"}, {"id": 100200351, "tagName": "含自助餐"}, {"id": 100229628, "tagName": "测试测试"}, {"id": 10064338, "tagName": "批量团泛1"}, {"id": 10058438, "tagName": "批量团泛1"}, {"id": 100312080, "tagName": "推拿按摩"}, {"id": 100298349, "tagName": "肩颈按摩"}, {"id": 10062440, "tagName": "批量团泛1+2rules"}, {"id": 100436024, "tagName": "洗浴中心单人成人通用浴资票"}, {"id": 100443010, "tagName": "洗浴中心亲子通用浴资票"}, {"id": 100381092, "tagName": "浴室澡堂单人浴资票"}, {"id": 100090843, "tagName": "重复2"}, {"id": 100090842, "tagName": "重复"}, {"id": 100090840, "tagName": "批量新增示例V1"}], "customer": null, "regions": [{"dpCityId": 1, "mtCityId": 10}, {"dpCityId": 10, "mtCityId": 40}], "notice": null, "notice2b": null, "deals": [{"dealId": 464991391, "basic": {"title": "浴资票|全量测试团单", "originTitle": "浴资票|全量测试团单", "thirdPartyId": null, "status": 1, "thirdPartyDealId": null}, "price": null, "stock": null, "attrs": null, "dealTimeStockDTO": null, "dealTimeStockPlanDTO": null, "rule": null, "image": null, "displayShop": null, "dealDelivery": null, "shopStocks": null, "bizDealId": 464991391, "weeklyPricePlan": null, "dateTimePrice": null, "periodPrice": null, "deductPlan": null, "dealIdInt": 464991391, "bizDealIdInt": 464991391}], "price": {"salePrice": "110.00", "marketPrice": "111.00", "version": 5191940146, "status": null, "prePayPrice": null, "finalPayPrice": null}, "stock": null, "detail": {"dealGroupPics": "https://p0.inf.test.sankuai.com/dpmerchantpic/8e969db60ee8d08766b79fcca9235063171436.jpg", "images": ["https://qcloud.dpfile.com/pc/t8TlJaXQN4VEivfU5gpndxGE7SaTi87YRePfq4g4k6eNrM9QDL81k3s_hM6FxBgFDkGHbckSnjHySsp3uDLV9w.jpg"], "info": null, "importantPoint": "", "specialPoint": null, "productInfo": null, "editorInfo": null, "memberInfo": null, "shopInfo": null, "editorTeam": null, "summary": null, "templateDetailDTOs": [{"title": "产品介绍", "content": "<div>图文详情11111111</div>\n<p class=\"listitem\"></p>\n<p class=\"explain\"></p>\n<div class=\"img\"><img src=\"https://p0.meituan.net/dpmerchantpic/21fae7018771d72e1ece2068d5271f7668592.jpg%40640w_1024h_1e_1l%7Cwatermark%3D1%26%26r%3D1%26p%3D9%26x%3D2%26y%3D2%26relative%3D1%26o%3D20\" /></div>\n\n", "type": 5, "blockId": 0, "mixedContents": null, "imageContents": null}, {"title": "团购详情", "content": "        <div class=\"detail-tit\"></div>\n        <div>\n            <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" class=\"detail-table\">\n                <thead>\n                <tr>\n                    <th width=\"50%\">名称</th>\n                    <th width=\"25%\">数量</th>\n                    <th width=\"25%\">单价</th>\n                </tr>\n                </thead>\n                <tbody>\n                            <tr>\n                                <td>浴资票全部可享</td>\n                                    <td class=\"tc\">1</td>\n                            </tr>\n                            <tr>\n                                <td>附赠服务</td>\n                                    <td class=\"tc\">1</td>\n                            </tr>\n                            <tr>\n                                <th colspan=\"3\">以下2选1</th>\n                            </tr>\n                            <tr>\n                                <td>自助餐1</td>\n                                    <td class=\"tc\">2</td>\n                            </tr>\n                            <tr>\n                                <td>自助餐2</td>\n                                    <td class=\"tc\">3</td>\n                            </tr>\n                            <tr>\n                                <th colspan=\"3\">以下2选1</th>\n                            </tr>\n                            <tr>\n                                <td>浴资票1</td>\n                                    <td class=\"tc\">1</td>\n                            </tr>\n                            <tr>\n                                <td>浴资票2</td>\n                                    <td class=\"tc\">1</td>\n                            </tr>\n                    <tr class=\"total\">\n                        <td></td>\n                        <td class=\"tc\">总价<br><strong>团购价</strong></td>\n                        <td class=\"tc\">111元<br><strong>110元</strong>\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n<div>补充信息1111111111</div>\n\n", "type": 1, "blockId": 0, "mixedContents": null, "imageContents": null}, {"title": "购买须知", "content": "<div class=\"detail-box\">\n    <div class=\"purchase-notes\">\n\t\t        <dl>\n            <dt>有效期</dt>\n            <dd>\n                <p class=\"listitem\">\n    购买后90天内有效\n        </p>\n            </dd>\n        </dl>\n        <dl>\n            <dt>使用时间</dt>\n            <dd>\n                <p class=\"listitem\">2025-5-28至2025-6-1 00:00-23:00可用</p>\n            </dd>\n        </dl>\n        <dl>\n            <dt>验证方式</dt>\n            <dd>\n                <p class=\"listitem\">入场前验证团购券</p>\n            </dd>\n        </dl>\n        <dl>\n            <dt>预约信息</dt>\n            <dd>\n                <p class=\"listitem\">无需预约，如遇消费高峰时段您可能需要排队</p>\n            </dd>\n        </dl>\n        <dl>\n            <dt>适用人数</dt>\n            <dd>\n                <p class=\"listitem\">每张团购券不限使用人数</p>\n            </dd>\n        </dl>\n        <dl>\n            <dt>规则提醒</dt>\n            <dd>\n                <p class=\"listitem\">需您当日一次性体验完毕所有项目</p>\n                <p class=\"listitem\">不再与其他优惠同享</p>\n            </dd>\n        </dl>\n        <dl>\n            <dt>温馨提示</dt>\n            <dd>\n                <p class=\"listitem\">如需团购券发票，请您在消费时向商户咨询</p>\n                <p class=\"listitem\">为了保障您的权益，建议使用美团、点评网线上支付。若使用其他支付方式导致纠纷，美团、点评网不承担任何责任，感谢您的理解和支持！</p>\n            </dd>\n        </dl>\n    </div>\n</div>\n", "type": 2, "blockId": 0, "mixedContents": null, "imageContents": null}]}, "spu": null, "standardServiceProject": {"mustGroups": [{"serviceProjectItems": [{"serviceProjectName": "浴资票全部可享", "standardAttribute": {"attrs": [{"attrName": "AvailableTimePeriod3", "attrCnName": "可用时段", "attrValues": [{"type": 0, "simpleValues": ["营业时间内全部可用"], "complexValues": null}]}, {"attrName": "skuCateId", "attrCnName": "项目分类", "attrValues": [{"type": 0, "simpleValues": ["895"], "complexValues": null}]}, {"attrName": "ApplicableDuration", "attrCnName": "适用时长(小时)", "attrValues": [{"type": 0, "simpleValues": ["24"], "complexValues": null}]}, {"attrName": "ChildCount2", "attrCnName": "儿童", "attrValues": [{"type": 0, "simpleValues": ["1"], "complexValues": null}]}, {"attrName": "AdultPopulation2", "attrCnName": "成人", "attrValues": [{"type": 0, "simpleValues": ["1"], "complexValues": null}]}, {"attrName": "content", "attrCnName": "浴资票补充说明", "attrValues": [{"type": 0, "simpleValues": ["浴资票全部可享补充说明浴资票补充说明"], "complexValues": null}]}], "cpvObjectId": 20413636, "cpvObjectVersion": 54}, "marketPrice": null, "amount": null, "referSubjectId": null, "referSubjectType": null, "isMain": null, "source": null, "resources": null, "chooseFewConfigDTOS": null, "subServiceProjectItems": null}, {"serviceProjectName": "附赠服务", "standardAttribute": {"attrs": [{"attrName": "content", "attrCnName": "亮点描述", "attrValues": [{"type": 0, "simpleValues": ["门票附赠服务亮点描述"], "complexValues": null}]}], "cpvObjectId": 24493794, "cpvObjectVersion": 23}, "marketPrice": null, "amount": null, "referSubjectId": null, "referSubjectType": null, "isMain": null, "source": null, "resources": null, "chooseFewConfigDTOS": null, "subServiceProjectItems": null}], "optionalCount": 0, "optionalRepeatable": null}], "optionalGroups": [{"serviceProjectItems": [{"serviceProjectName": "自助餐1", "standardAttribute": {"attrs": [{"attrName": "skuCateId", "attrCnName": "项目分类", "attrValues": [{"type": 0, "simpleValues": ["2105655"], "complexValues": null}]}, {"attrName": "Selfservicediningcontent", "attrCnName": "自助餐食内容", "attrValues": [{"type": 0, "simpleValues": ["[\"海鲜\",\"中式炒菜\",\"粉面主食\",\"养生汤粥\",\"小吃甜品\",\"水果\"]"], "complexValues": null}]}, {"attrName": "selfservicedininghours", "attrCnName": "自助餐供应时间", "attrValues": [{"type": 1, "simpleValues": null, "complexValues": "[{\"attrs\":[{\"attrName\":\"timeframe1\",\"attrCnName\":\"时段\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"1.0\"]}]},{\"attrName\":\"selfservicerestaurantclosingtime\",\"attrCnName\":\"自助餐结束时间\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"08:00\"]}]},{\"attrName\":\"selfservicerestaurantstarttime\",\"attrCnName\":\"自助餐开始时间\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"07:00\"]}]}],\"cpvObjectId\":10618454},{\"attrs\":[{\"attrName\":\"timeframe1\",\"attrCnName\":\"时段\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"2.0\"]}]},{\"attrName\":\"selfservicerestaurantclosingtime\",\"attrCnName\":\"自助餐结束时间\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"20:00\"]}]},{\"attrName\":\"selfservicerestaurantstarttime\",\"attrCnName\":\"自助餐开始时间\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"19:00\"]}]}],\"cpvObjectId\":10618454}]"}]}, {"attrName": "Selfservicediningduration", "attrCnName": "自助用餐时长", "attrValues": [{"type": 0, "simpleValues": ["90分钟"], "complexValues": null}]}, {"attrName": "content", "attrCnName": "餐食亮点描述", "attrValues": [{"type": 0, "simpleValues": ["自助餐1餐食亮点描述"], "complexValues": null}]}], "cpvObjectId": 20417997, "cpvObjectVersion": 42}, "marketPrice": null, "amount": 2, "referSubjectId": null, "referSubjectType": null, "isMain": null, "source": null, "resources": null, "chooseFewConfigDTOS": null, "subServiceProjectItems": null}, {"serviceProjectName": "自助餐2", "standardAttribute": {"attrs": [{"attrName": "skuCateId", "attrCnName": "项目分类", "attrValues": [{"type": 0, "simpleValues": ["2105655"], "complexValues": null}]}, {"attrName": "Selfservicediningcontent", "attrCnName": "自助餐食内容", "attrValues": [{"type": 0, "simpleValues": ["[\"海鲜\",\"中式炒菜\",\"粉面主食\",\"养生汤粥\",\"小吃甜品\",\"水果\"]"], "complexValues": null}]}, {"attrName": "selfservicedininghours", "attrCnName": "自助餐供应时间", "attrValues": [{"type": 1, "simpleValues": null, "complexValues": "[{\"attrs\":[{\"attrName\":\"timeframe1\",\"attrCnName\":\"时段\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"1.0\"]}]},{\"attrName\":\"selfservicerestaurantclosingtime\",\"attrCnName\":\"自助餐结束时间\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"14:00\"]}]},{\"attrName\":\"selfservicerestaurantstarttime\",\"attrCnName\":\"自助餐开始时间\",\"attrValues\":[{\"type\":0,\"simpleValues\":[\"12:00\"]}]}],\"cpvObjectId\":10618454}]"}]}, {"attrName": "Selfservicediningduration", "attrCnName": "自助用餐时长", "attrValues": [{"type": 0, "simpleValues": ["60分钟"], "complexValues": null}]}, {"attrName": "content", "attrCnName": "餐食亮点描述", "attrValues": [{"type": 0, "simpleValues": ["自助餐2餐食亮点描述"], "complexValues": null}]}], "cpvObjectId": 20417997, "cpvObjectVersion": 42}, "marketPrice": null, "amount": 3, "referSubjectId": null, "referSubjectType": null, "isMain": null, "source": null, "resources": null, "chooseFewConfigDTOS": null, "subServiceProjectItems": null}], "optionalCount": 1, "optionalRepeatable": null}, {"serviceProjectItems": [{"serviceProjectName": "浴资票1", "standardAttribute": {"attrs": [{"attrName": "AvailableTimePeriod3", "attrCnName": "可用时段", "attrValues": [{"type": 0, "simpleValues": ["部分时间可用"], "complexValues": null}]}, {"attrName": "skuCateId", "attrCnName": "项目分类", "attrValues": [{"type": 0, "simpleValues": ["895"], "complexValues": null}]}, {"attrName": "TimeRange3", "attrCnName": "时间范围", "attrValues": [{"type": 0, "simpleValues": ["[\"1\",\"2\",\"3\",\"5\",\"6\",\"7\"]"], "complexValues": null}]}, {"attrName": "ApplicableDuration", "attrCnName": "适用时长(小时)", "attrValues": [{"type": 0, "simpleValues": ["6"], "complexValues": null}]}, {"attrName": "AvailableTimePeriodName", "attrCnName": "可用时段名称", "attrValues": [{"type": 0, "simpleValues": ["工作日"], "complexValues": null}]}, {"attrName": "ChildCount2", "attrCnName": "儿童", "attrValues": [{"type": 0, "simpleValues": ["0"], "complexValues": null}]}, {"attrName": "AdultPopulation2", "attrCnName": "成人", "attrValues": [{"type": 0, "simpleValues": ["2"], "complexValues": null}]}, {"attrName": "content", "attrCnName": "浴资票补充说明", "attrValues": [{"type": 0, "simpleValues": ["浴资票1补充说明浴资票补充说明浴资票补充说明"], "complexValues": null}]}], "cpvObjectId": 20413636, "cpvObjectVersion": 54}, "marketPrice": null, "amount": null, "referSubjectId": null, "referSubjectType": null, "isMain": null, "source": null, "resources": null, "chooseFewConfigDTOS": null, "subServiceProjectItems": null}, {"serviceProjectName": "浴资票2", "standardAttribute": {"attrs": [{"attrName": "AvailableTimePeriod3", "attrCnName": "可用时段", "attrValues": [{"type": 0, "simpleValues": ["部分时间可用"], "complexValues": null}]}, {"attrName": "skuCateId", "attrCnName": "项目分类", "attrValues": [{"type": 0, "simpleValues": ["895"], "complexValues": null}]}, {"attrName": "TimeRange3", "attrCnName": "时间范围", "attrValues": [{"type": 0, "simpleValues": ["[\"1\",\"3\",\"5\",\"7\"]"], "complexValues": null}]}, {"attrName": "ChildInstructions", "attrCnName": "儿童判断标准", "attrValues": [{"type": 0, "simpleValues": ["判断标准判断标准判断标准"], "complexValues": null}]}, {"attrName": "ApplicableDuration", "attrCnName": "适用时长(小时)", "attrValues": [{"type": 0, "simpleValues": ["6"], "complexValues": null}]}, {"attrName": "AvailableTimePeriodName", "attrCnName": "可用时段名称", "attrValues": [{"type": 0, "simpleValues": ["工作日"], "complexValues": null}]}, {"attrName": "ChildCount2", "attrCnName": "儿童", "attrValues": [{"type": 0, "simpleValues": ["2"], "complexValues": null}]}, {"attrName": "AdultPopulation2", "attrCnName": "成人", "attrValues": [{"type": 0, "simpleValues": ["0"], "complexValues": null}]}, {"attrName": "content", "attrCnName": "浴资票补充说明", "attrValues": [{"type": 0, "simpleValues": ["浴资票2补充说明浴资票补充说明"], "complexValues": null}]}], "cpvObjectId": 20413636, "cpvObjectVersion": 54}, "marketPrice": null, "amount": null, "referSubjectId": null, "referSubjectType": null, "isMain": null, "source": null, "resources": null, "chooseFewConfigDTOS": null, "subServiceProjectItems": null}], "optionalCount": 1, "optionalRepeatable": null}]}, "extendImage": null, "combines": null, "saleChannelAggregation": {"allSupport": true, "supportChannels": [], "notSupportChannels": []}, "purchaseNote": null, "bizProductId": null, "resourceInfos": null, "thirdPartyInfo": null, "metaObjectInfo": {"objectId": 20408612, "objectVersion": 63}, "spus": null, "purchaseNotes": null, "originDetails": {"headImage": "{\"version\":1,\"headline\":\"头图\",\"content\":[{\"type\":\"imageList\",\"data\":[{\"title\":\"\",\"desc\":\"\",\"path\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/8e969db60ee8d08766b79fcca9235063171436.jpg\"}]}]}", "detailInfo": "{\"version\":1,\"headline\":\"团购详情\",\"content\":[{\"type\":\"uniform-structure-table\",\"data\":{\"totalPrice\":111.0,\"salePrice\":110.0,\"groups\":[{\"optionalCount\":1,\"units\":[{\"skuCateId\":2105655,\"projectName\":\"自助餐1\",\"amount\":2,\"attrValues\":{\"skuCateId\":\"2105655\",\"Selfservicediningcontent\":\"海鲜、中式炒菜、粉面主食、养生汤粥、小吃甜品、水果\",\"selfservicedininghours\":\"[{\\\"timeframe1\\\":\\\"1\\\",\\\"selfservicerestaurantclosingtime\\\":\\\"08:00\\\",\\\"selfservicerestaurantstarttime\\\":\\\"07:00\\\"},{\\\"timeframe1\\\":\\\"2\\\",\\\"selfservicerestaurantclosingtime\\\":\\\"20:00\\\",\\\"selfservicerestaurantstarttime\\\":\\\"19:00\\\"}]\",\"Selfservicediningduration\":\"90分钟\",\"content\":\"自助餐1餐食亮点描述\"}},{\"skuCateId\":2105655,\"projectName\":\"自助餐2\",\"amount\":3,\"attrValues\":{\"skuCateId\":\"2105655\",\"Selfservicediningcontent\":\"海鲜、中式炒菜、粉面主食、养生汤粥、小吃甜品、水果\",\"selfservicedininghours\":\"[{\\\"timeframe1\\\":\\\"1\\\",\\\"selfservicerestaurantclosingtime\\\":\\\"14:00\\\",\\\"selfservicerestaurantstarttime\\\":\\\"12:00\\\"}]\",\"Selfservicediningduration\":\"60分钟\",\"content\":\"自助餐2餐食亮点描述\"}}]},{\"optionalCount\":1,\"units\":[{\"skuCateId\":895,\"projectName\":\"浴资票1\",\"amount\":1,\"attrValues\":{\"AvailableTimePeriod3\":\"部分时间可用\",\"skuCateId\":\"895\",\"TimeRange3\":\"1、2、3、5、6、7\",\"ApplicableDuration\":\"6\",\"AvailableTimePeriodName\":\"工作日\",\"ChildCount2\":\"0\",\"AdultPopulation2\":\"2\",\"content\":\"浴资票1补充说明浴资票补充说明浴资票补充说明\"}},{\"skuCateId\":895,\"projectName\":\"浴资票2\",\"amount\":1,\"attrValues\":{\"AvailableTimePeriod3\":\"部分时间可用\",\"skuCateId\":\"895\",\"TimeRange3\":\"1、3、5、7\",\"ChildInstructions\":\"判断标准判断标准判断标准\",\"ApplicableDuration\":\"6\",\"AvailableTimePeriodName\":\"工作日\",\"ChildCount2\":\"2\",\"AdultPopulation2\":\"0\",\"content\":\"浴资票2补充说明浴资票补充说明\"}}]},{\"optionalCount\":0,\"units\":[{\"skuCateId\":895,\"projectName\":\"浴资票全部可享\",\"amount\":1,\"attrValues\":{\"AvailableTimePeriod3\":\"营业时间内全部可用\",\"skuCateId\":\"895\",\"ApplicableDuration\":\"24\",\"ChildCount2\":\"1\",\"AdultPopulation2\":\"1\",\"content\":\"浴资票全部可享补充说明浴资票补充说明\"}},{\"skuCateId\":2201279,\"projectName\":\"附赠服务\",\"amount\":1,\"attrValues\":{\"skuCateId\":\"2201279\",\"content\":\"门票附赠服务亮点描述\"}}]}]}},{\"type\":\"serviceItem-structure-table\",\"data\":{\"groups\":[{\"optionalCount\":1,\"units\":[{\"serviceItemName\":\"自助餐1\",\"amount\":2,\"serviceItemValue\":{\"objectId\":20417997,\"objectVersion\":42,\"objectValues\":{\"skuCateId\":2105655,\"Selfservicediningcontent\":[\"海鲜\",\"中式炒菜\",\"粉面主食\",\"养生汤粥\",\"小吃甜品\",\"水果\"],\"selfservicedininghours\":[{\"timeframe1\":1,\"selfservicerestaurantclosingtime\":\"08:00\",\"selfservicerestaurantstarttime\":\"07:00\"},{\"timeframe1\":2,\"selfservicerestaurantclosingtime\":\"20:00\",\"selfservicerestaurantstarttime\":\"19:00\"}],\"Selfservicediningduration\":\"90分钟\",\"content\":\"自助餐1餐食亮点描述\"}}},{\"serviceItemName\":\"自助餐2\",\"amount\":3,\"serviceItemValue\":{\"objectId\":20417997,\"objectVersion\":42,\"objectValues\":{\"skuCateId\":2105655,\"Selfservicediningcontent\":[\"海鲜\",\"中式炒菜\",\"粉面主食\",\"养生汤粥\",\"小吃甜品\",\"水果\"],\"selfservicedininghours\":[{\"timeframe1\":1,\"selfservicerestaurantclosingtime\":\"14:00\",\"selfservicerestaurantstarttime\":\"12:00\"}],\"Selfservicediningduration\":\"60分钟\",\"content\":\"自助餐2餐食亮点描述\"}}}]},{\"optionalCount\":1,\"units\":[{\"serviceItemName\":\"浴资票1\",\"serviceItemValue\":{\"objectId\":20413636,\"objectVersion\":54,\"objectValues\":{\"AvailableTimePeriod3\":\"部分时间可用\",\"skuCateId\":895,\"TimeRange3\":[\"1\",\"2\",\"3\",\"5\",\"6\",\"7\"],\"ApplicableDuration\":\"6\",\"AvailableTimePeriodName\":\"工作日\",\"ChildCount2\":\"0\",\"AdultPopulation2\":2,\"content\":\"浴资票1补充说明浴资票补充说明浴资票补充说明\"}}},{\"serviceItemName\":\"浴资票2\",\"serviceItemValue\":{\"objectId\":20413636,\"objectVersion\":54,\"objectValues\":{\"AvailableTimePeriod3\":\"部分时间可用\",\"skuCateId\":895,\"TimeRange3\":[\"1\",\"3\",\"5\",\"7\"],\"ChildInstructions\":\"判断标准判断标准判断标准\",\"ApplicableDuration\":\"6\",\"AvailableTimePeriodName\":\"工作日\",\"ChildCount2\":2,\"AdultPopulation2\":0,\"content\":\"浴资票2补充说明浴资票补充说明\"}}}]},{\"optionalCount\":0,\"units\":[{\"serviceItemName\":\"浴资票全部可享\",\"serviceItemValue\":{\"objectId\":20413636,\"objectVersion\":54,\"objectValues\":{\"AvailableTimePeriod3\":\"营业时间内全部可用\",\"skuCateId\":895,\"ApplicableDuration\":\"24\",\"ChildCount2\":1,\"AdultPopulation2\":1,\"content\":\"浴资票全部可享补充说明浴资票补充说明\"}}},{\"serviceItemName\":\"附赠服务\",\"serviceItemValue\":{\"objectId\":24493794,\"objectVersion\":23,\"objectValues\":{\"skuCateId\":2201279,\"content\":\"门票附赠服务亮点描述\"}}}]}],\"totalPrice\":111.0}},{\"type\":\"richtext\",\"data\":\"补充信息1111111111\"}]}", "productIntro": "{\"version\":1,\"headline\":\"产品介绍\",\"content\":[{\"type\":\"richtext\",\"data\":\"图文详情11111111\"},{\"type\":\"imageList\",\"data\":[{\"title\":\"\",\"desc\":\"\",\"path\":\"http://p0.meituan.net/dpmerchantpic/21fae7018771d72e1ece2068d5271f7668592.jpg\"}]}]}"}, "shopBookInfos": null, "defaultSelectDTO": null, "dpDealGroupIdInt": 1042034504, "mtDealGroupIdInt": 1042034504}], "filteredDealGroups": []}, "exceptionDealGroupField": null, "exceptionDealField": null, "exceptionSpuField": null}