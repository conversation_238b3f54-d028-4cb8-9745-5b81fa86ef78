package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MtLiveMiniAppReminderInfoBuilderTest {

    @Spy
    private TestMtLiveMiniAppReminderInfoBuilder builder;

    // 创建一个测试用的子类来重写getBaseReminderInfo方法
    private static class TestMtLiveMiniAppReminderInfoBuilder extends MtLiveMiniAppReminderInfoBuilder {

        private ProductDetailReminderVO baseReminderInfo;

        public void setBaseReminderInfo(ProductDetailReminderVO baseReminderInfo) {
            this.baseReminderInfo = baseReminderInfo;
        }

        @Override
        public ProductDetailReminderVO getBaseReminderInfo() {
            return baseReminderInfo;
        }
    }

    /**
     * Test when getBaseReminderInfo returns null
     */
    @Test
    public void testPreBuildWhenBaseReminderInfoIsNull() throws Throwable {
        // arrange
        builder.setBaseReminderInfo(null);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNull(result);
        verify(builder).getBaseReminderInfo();
    }

    /**
     * Test when getBaseReminderInfo returns object with empty contents
     */
    @Test
    public void testPreBuildWhenContentsIsEmpty() throws Throwable {
        // arrange
        ProductDetailReminderVO mockReminder = new ProductDetailReminderVO();
        mockReminder.setContents(Collections.emptyList());
        builder.setBaseReminderInfo(mockReminder);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNull(result);
        verify(builder).getBaseReminderInfo();
    }

    /**
     * Test when getBaseReminderInfo returns valid contents and default items are added
     */
    @Test
    public void testPreBuildWhenContentsExist() throws Throwable {
        // arrange
        ProductDetailReminderVO mockReminder = new ProductDetailReminderVO();
        GuaranteeInstructionsContentVO existingContent = new GuaranteeInstructionsContentVO("Existing content");
        mockReminder.setContents(Lists.newArrayList(existingContent));
        builder.setBaseReminderInfo(mockReminder);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNotNull(result);
        List<GuaranteeInstructionsContentVO> contents = result.getContents();
        assertNotNull(contents);
        assertEquals(3, contents.size());
        // Verify content texts
        List<String> contentTexts = contents.stream().map(GuaranteeInstructionsContentVO::getText).collect(java.util.stream.Collectors.toList());
        assertTrue(contentTexts.contains("7天无理由退款"));
        assertTrue(contentTexts.contains("过期自动退"));
        assertTrue(contentTexts.contains("Existing content"));
        verify(builder).getBaseReminderInfo();
    }

    /**
     * Test when base reminder info has multiple contents
     */
    @Test
    public void testPreBuildWithMultipleContents() throws Throwable {
        // arrange
        ProductDetailReminderVO mockReminder = new ProductDetailReminderVO();
        List<GuaranteeInstructionsContentVO> existingContents = Lists.newArrayList(new GuaranteeInstructionsContentVO("Content 1"), new GuaranteeInstructionsContentVO("Content 2"));
        mockReminder.setContents(existingContents);
        builder.setBaseReminderInfo(mockReminder);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNotNull(result);
        List<GuaranteeInstructionsContentVO> contents = result.getContents();
        assertNotNull(contents);
        assertEquals(4, contents.size());
        // Verify content texts
        List<String> contentTexts = contents.stream().map(GuaranteeInstructionsContentVO::getText).collect(java.util.stream.Collectors.toList());
        assertTrue(contentTexts.contains("7天无理由退款"));
        assertTrue(contentTexts.contains("过期自动退"));
        assertTrue(contentTexts.contains("Content 1"));
        assertTrue(contentTexts.contains("Content 2"));
        verify(builder).getBaseReminderInfo();
    }
}
