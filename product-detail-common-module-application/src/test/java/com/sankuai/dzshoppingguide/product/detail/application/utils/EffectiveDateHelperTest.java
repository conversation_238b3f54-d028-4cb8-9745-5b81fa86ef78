package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class EffectiveDateHelperTest {

    /**
     * Test when ProductBaseInfo is null
     */
    @Test
    public void testGetEffectiveDateWhenBaseInfoIsNull() {
        // arrange
        ProductBaseInfo baseInfo = null;
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when rule is null in ProductBaseInfo
     */
    @Test
    public void testGetEffectiveDateWhenRuleIsNull() {
        // arrange
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        when(baseInfo.getRule()).thenReturn(null);
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when useRule is null in DealGroupRuleDTO
     */
    @Test
    public void testGetEffectiveDateWhenUseRuleIsNull() {
        // arrange
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = Mockito.mock(DealGroupRuleDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(null);
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when receiptEffectiveDate is null in DealGroupUseRuleDTO
     */
    @Test
    public void testGetEffectiveDateWhenReceiptEffectiveDateIsNull() {
        // arrange
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = Mockito.mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = Mockito.mock(DealGroupUseRuleDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(null);
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when receiptDateType is null
     */
    @Test
    public void testGetEffectiveDateWhenReceiptDateTypeIsNull() {
        // arrange
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = Mockito.mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = Mockito.mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptEffectiveDate = Mockito.mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptEffectiveDate);
        when(receiptEffectiveDate.getReceiptDateType()).thenReturn(null);
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test valid date range (receiptDateType = 0)
     */
    @Test
    public void testGetEffectiveDateWhenValidDateRange() {
        // arrange
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = Mockito.mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = Mockito.mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptEffectiveDate = Mockito.mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptEffectiveDate);
        when(receiptEffectiveDate.getReceiptDateType()).thenReturn(0);
        when(receiptEffectiveDate.getReceiptBeginDate()).thenReturn("2023-01-01 00:00:00");
        when(receiptEffectiveDate.getReceiptEndDate()).thenReturn("2023-12-31 23:59:59");
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("2023-01-01至2023-12-31有效", result);
    }

    /**
     * Test invalid date range (one date is null)
     */
    @Test
    public void testGetEffectiveDateWhenInvalidDateRange() {
        // arrange
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = Mockito.mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = Mockito.mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptEffectiveDate = Mockito.mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptEffectiveDate);
        when(receiptEffectiveDate.getReceiptDateType()).thenReturn(0);
        when(receiptEffectiveDate.getReceiptBeginDate()).thenReturn("2023-01-01 00:00:00");
        when(receiptEffectiveDate.getReceiptEndDate()).thenReturn(null);
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test valid days after purchase (receiptDateType = 1)
     */
    @Test
    public void testGetEffectiveDateWhenValidDaysAfterPurchase() {
        // arrange
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = Mockito.mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = Mockito.mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptEffectiveDate = Mockito.mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptEffectiveDate);
        when(receiptEffectiveDate.getReceiptDateType()).thenReturn(1);
        when(receiptEffectiveDate.getReceiptValidDays()).thenReturn(30);
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("购买后30天内有效", result);
    }

    /**
     * Test invalid days after purchase (receiptValidDays is null)
     */
    @Test
    public void testGetEffectiveDateWhenInvalidDaysAfterPurchase() {
        // arrange
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = Mockito.mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = Mockito.mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptEffectiveDate = Mockito.mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptEffectiveDate);
        when(receiptEffectiveDate.getReceiptDateType()).thenReturn(1);
        when(receiptEffectiveDate.getReceiptValidDays()).thenReturn(null);
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test unknown receiptDateType
     */
    @Test
    public void testGetEffectiveDateWhenUnknownReceiptDateType() {
        // arrange
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = Mockito.mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = Mockito.mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptEffectiveDate = Mockito.mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptEffectiveDate);
        // Unknown type
        when(receiptEffectiveDate.getReceiptDateType()).thenReturn(2);
        // act
        String result = EffectiveDateHelper.getEffectiveDate(baseInfo);
        // assert
        assertEquals("", result);
    }
}
