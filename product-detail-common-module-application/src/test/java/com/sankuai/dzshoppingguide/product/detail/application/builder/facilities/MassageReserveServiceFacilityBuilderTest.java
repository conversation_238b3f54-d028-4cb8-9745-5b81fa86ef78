package com.sankuai.dzshoppingguide.product.detail.application.builder.facilities;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.config.AttrConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.config.Configs;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.FacilitiesVO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;

@ExtendWith(MockitoExtension.class)
public class MassageReserveServiceFacilityBuilderTest {

    @Mock
    private ProductAttr productAttr;

    private Configs originalConfigs;

    /**
     * Test when configs contains multiple valid attributes
     */
    @Test
    public void testBuildFacilities_WithMultipleValidAttributes() {
        // arrange
        MassageReserveServiceFacilityBuilder builder = new MassageReserveServiceFacilityBuilder();
        List<FacilitiesVO> detailServiceFacilities = new ArrayList<>();
        AttrConfig upperAttr = new AttrConfig();
        upperAttr.setDisplayName("Upper Title");
        AttrConfig config1 = new AttrConfig();
        config1.setAttrName("attr1");
        config1.setDisplayName("Display1");
        AttrConfig config2 = new AttrConfig();
        config2.setAttrName("attr2");
        config2.setDisplayName("Display2");
        Configs configs = new Configs();
        configs.setUpperAttr(upperAttr);
        configs.setConfigs(Arrays.asList(config1, config2));
        when(productAttr.getSkuAttrFirstValue("attr1")).thenReturn("value1");
        when(productAttr.getSkuAttrFirstValue("attr2")).thenReturn("value2");
        // act
        builder.buildFacilities(productAttr, detailServiceFacilities, configs);
        // assert
        assertEquals(5, detailServiceFacilities.size());
        assertEquals(1, detailServiceFacilities.get(0).getType());
        assertEquals("Upper Title", detailServiceFacilities.get(0).getTitle());
        assertEquals(2, detailServiceFacilities.get(1).getType());
        assertEquals("Display1", detailServiceFacilities.get(1).getTitle());
        assertEquals(3, detailServiceFacilities.get(2).getType());
        assertTrue(detailServiceFacilities.get(2).getContents().contains("value1"));
        assertEquals(2, detailServiceFacilities.get(3).getType());
        assertEquals("Display2", detailServiceFacilities.get(3).getTitle());
        assertEquals(3, detailServiceFacilities.get(4).getType());
        assertTrue(detailServiceFacilities.get(4).getContents().contains("value2"));
    }

    /**
     * Test when some attributes have empty values
     */
    @Test
    public void testBuildFacilities_WithSomeEmptyAttributes() {
        // arrange
        MassageReserveServiceFacilityBuilder builder = new MassageReserveServiceFacilityBuilder();
        List<FacilitiesVO> detailServiceFacilities = new ArrayList<>();
        AttrConfig upperAttr = new AttrConfig();
        upperAttr.setDisplayName("Upper Title");
        AttrConfig config1 = new AttrConfig();
        config1.setAttrName("attr1");
        config1.setDisplayName("Display1");
        AttrConfig config2 = new AttrConfig();
        config2.setAttrName("attr2");
        config2.setDisplayName("Display2");
        Configs configs = new Configs();
        configs.setUpperAttr(upperAttr);
        configs.setConfigs(Arrays.asList(config1, config2));
        when(productAttr.getSkuAttrFirstValue("attr1")).thenReturn("value1");
        when(productAttr.getSkuAttrFirstValue("attr2")).thenReturn("");
        // act
        builder.buildFacilities(productAttr, detailServiceFacilities, configs);
        // assert
        assertEquals(3, detailServiceFacilities.size());
        assertEquals(1, detailServiceFacilities.get(0).getType());
        assertEquals("Upper Title", detailServiceFacilities.get(0).getTitle());
        assertEquals(2, detailServiceFacilities.get(1).getType());
        assertEquals("Display1", detailServiceFacilities.get(1).getTitle());
        assertEquals(3, detailServiceFacilities.get(2).getType());
        assertTrue(detailServiceFacilities.get(2).getContents().contains("value1"));
    }

    /**
     * Test when configs list is empty
     */
    @Test
    public void testBuildFacilities_WithEmptyConfigs() {
        // arrange
        MassageReserveServiceFacilityBuilder builder = new MassageReserveServiceFacilityBuilder();
        List<FacilitiesVO> detailServiceFacilities = new ArrayList<>();
        AttrConfig upperAttr = new AttrConfig();
        upperAttr.setDisplayName("Upper Title");
        Configs configs = new Configs();
        configs.setUpperAttr(upperAttr);
        configs.setConfigs(new ArrayList<>());
        // act
        builder.buildFacilities(productAttr, detailServiceFacilities, configs);
        // assert
        assertEquals(1, detailServiceFacilities.size());
        assertEquals(1, detailServiceFacilities.get(0).getType());
        assertEquals("Upper Title", detailServiceFacilities.get(0).getTitle());
    }

    /**
     * Test when all attribute values are null
     */
    @Test
    public void testBuildFacilities_WithAllNullValues() {
        // arrange
        MassageReserveServiceFacilityBuilder builder = new MassageReserveServiceFacilityBuilder();
        List<FacilitiesVO> detailServiceFacilities = new ArrayList<>();
        AttrConfig upperAttr = new AttrConfig();
        upperAttr.setDisplayName("Upper Title");
        AttrConfig config1 = new AttrConfig();
        config1.setAttrName("attr1");
        config1.setDisplayName("Display1");
        Configs configs = new Configs();
        configs.setUpperAttr(upperAttr);
        configs.setConfigs(Arrays.asList(config1));
        when(productAttr.getSkuAttrFirstValue("attr1")).thenReturn(null);
        // act
        builder.buildFacilities(productAttr, detailServiceFacilities, configs);
        // assert
        assertEquals(1, detailServiceFacilities.size());
        assertEquals(1, detailServiceFacilities.get(0).getType());
        assertEquals("Upper Title", detailServiceFacilities.get(0).getTitle());
    }

    @Test
    void testBuildFacilityTitle_WithNullDisplayName_ShouldHandleNullTitle() throws Throwable {
        // arrange
        MassageReserveServiceFacilityBuilder builder = new MassageReserveServiceFacilityBuilder();
        AttrConfig config = mock(AttrConfig.class);
        when(config.getDisplayName()).thenReturn(null);
        List<FacilitiesVO> detailServiceFacilities = new ArrayList<>();
        // act
        builder.buildFacilityTitle(config, detailServiceFacilities);
        // assert
        assertEquals(1, detailServiceFacilities.size());
        FacilitiesVO vo = detailServiceFacilities.get(0);
        assertEquals(ViewComponentTypeEnum.FACILITY_TYPE_1.getType(), vo.getType());
        assertNull(vo.getTitle());
    }

    @Test
    void testBuildFacilityTitle_WithNullConfig_ShouldThrowException() throws Throwable {
        // arrange
        MassageReserveServiceFacilityBuilder builder = new MassageReserveServiceFacilityBuilder();
        List<FacilitiesVO> detailServiceFacilities = new ArrayList<>();
        // act & assert
        assertThrows(NullPointerException.class, () -> builder.buildFacilityTitle(null, detailServiceFacilities));
    }

    @Test
    void testBuildFacilityTitle_WithNullList_ShouldThrowException() throws Throwable {
        // arrange
        MassageReserveServiceFacilityBuilder builder = new MassageReserveServiceFacilityBuilder();
        AttrConfig config = mock(AttrConfig.class);
        when(config.getDisplayName()).thenReturn("测试标题");
        // act & assert
        assertThrows(NullPointerException.class, () -> builder.buildFacilityTitle(config, null));
    }

    @Test
    void testBuildFacilityTitle_VerifyBuilderUsage() throws Throwable {
        // arrange
        MassageReserveServiceFacilityBuilder builder = new MassageReserveServiceFacilityBuilder();
        AttrConfig config = mock(AttrConfig.class);
        when(config.getDisplayName()).thenReturn("测试标题");
        List<FacilitiesVO> detailServiceFacilities = new ArrayList<>();
        // act
        builder.buildFacilityTitle(config, detailServiceFacilities);
        // assert
        assertEquals(1, detailServiceFacilities.size());
        FacilitiesVO vo = detailServiceFacilities.get(0);
        assertAll(() -> assertEquals(ViewComponentTypeEnum.FACILITY_TYPE_1.getType(), vo.getType()), () -> assertEquals("测试标题", vo.getTitle()), () -> assertNull(vo.getIcon()), () -> assertNull(vo.getContents()), () -> assertNull(vo.getContentsWithIcon()));
    }

    @BeforeEach
    void setUp() {
        // Save original configs for restoration
        originalConfigs = MassageReserveServiceFacilityBuilder.configs;
    }

    @AfterEach
    void tearDown() {
        // Restore original configs
        MassageReserveServiceFacilityBuilder.configs = originalConfigs;
    }

    @Test
    void testParse_WithValidCompleteJson_ShouldSetConfigsCorrectly() throws Throwable {
        // arrange
        String validJson = "{\"upperAttr\":{\"attrId\":1,\"displayName\":\"Test Name\"}," + "\"configs\":[{\"attrId\":2,\"displayName\":\"Config 1\"}]}";
        // act
        MassageReserveServiceFacilityBuilder.parse(validJson);
        // assert
        assertNotNull(MassageReserveServiceFacilityBuilder.configs);
        assertNotNull(MassageReserveServiceFacilityBuilder.configs.getUpperAttr());
        assertEquals(1, MassageReserveServiceFacilityBuilder.configs.getUpperAttr().getAttrId());
        assertEquals("Test Name", MassageReserveServiceFacilityBuilder.configs.getUpperAttr().getDisplayName());
        assertEquals(1, MassageReserveServiceFacilityBuilder.configs.getConfigs().size());
        assertEquals(2, MassageReserveServiceFacilityBuilder.configs.getConfigs().get(0).getAttrId());
        assertEquals("Config 1", MassageReserveServiceFacilityBuilder.configs.getConfigs().get(0).getDisplayName());
    }

    @Test
    void testParse_WithEmptyString_ShouldThrowException() throws Throwable {
        // arrange
        String emptyJson = "";
        // act
        MassageReserveServiceFacilityBuilder.parse(emptyJson);
        // assert
        assertNull(MassageReserveServiceFacilityBuilder.configs);
    }

    @Test
    void testParse_WithNullInput_ShouldThrowException() throws Throwable {
        // arrange
        String nullJson = null;
        // act
        MassageReserveServiceFacilityBuilder.parse(nullJson);
        // assert
        assertNull(MassageReserveServiceFacilityBuilder.configs);
    }

    @Test
    void testParse_WithInvalidJson_ShouldThrowException() throws Throwable {
        // arrange
        String invalidJson = "{invalid: json}";
        // act & assert
        assertThrows(com.alibaba.fastjson.JSONException.class, () -> MassageReserveServiceFacilityBuilder.parse(invalidJson));
    }

    @Test
    void testParse_WithOnlyUpperAttr_ShouldSetOnlyUpperAttr() throws Throwable {
        // arrange
        String partialJson = "{\"upperAttr\":{\"attrId\":3,\"displayName\":\"Partial\"}}";
        // act
        MassageReserveServiceFacilityBuilder.parse(partialJson);
        // assert
        assertNotNull(MassageReserveServiceFacilityBuilder.configs);
        assertNotNull(MassageReserveServiceFacilityBuilder.configs.getUpperAttr());
        assertEquals(3, MassageReserveServiceFacilityBuilder.configs.getUpperAttr().getAttrId());
        assertEquals("Partial", MassageReserveServiceFacilityBuilder.configs.getUpperAttr().getDisplayName());
        assertNull(MassageReserveServiceFacilityBuilder.configs.getConfigs());
    }

    @Test
    void testParse_WithOnlyConfigs_ShouldSetOnlyConfigs() throws Throwable {
        // arrange
        String partialJson = "{\"configs\":[{\"attrId\":4,\"displayName\":\"Config Only\"}]}";
        // act
        MassageReserveServiceFacilityBuilder.parse(partialJson);
        // assert
        assertNotNull(MassageReserveServiceFacilityBuilder.configs);
        assertNull(MassageReserveServiceFacilityBuilder.configs.getUpperAttr());
        assertNotNull(MassageReserveServiceFacilityBuilder.configs.getConfigs());
        assertEquals(1, MassageReserveServiceFacilityBuilder.configs.getConfigs().size());
        assertEquals(4, MassageReserveServiceFacilityBuilder.configs.getConfigs().get(0).getAttrId());
        assertEquals("Config Only", MassageReserveServiceFacilityBuilder.configs.getConfigs().get(0).getDisplayName());
    }

    @Test
    void testParse_WithUnknownFields_ShouldIgnoreThem() throws Throwable {
        // arrange
        String jsonWithUnknownFields = "{\"unknownField\":\"value\",\"upperAttr\":{\"attrId\":5}}";
        // act
        MassageReserveServiceFacilityBuilder.parse(jsonWithUnknownFields);
        // assert
        assertNotNull(MassageReserveServiceFacilityBuilder.configs);
        assertNotNull(MassageReserveServiceFacilityBuilder.configs.getUpperAttr());
        assertEquals(5, MassageReserveServiceFacilityBuilder.configs.getUpperAttr().getAttrId());
        assertNull(MassageReserveServiceFacilityBuilder.configs.getConfigs());
    }

    @Test
    void testParse_WithEmptyConfigsArray_ShouldSetEmptyList() throws Throwable {
        // arrange
        String jsonWithEmptyArray = "{\"configs\":[]}";
        // act
        MassageReserveServiceFacilityBuilder.parse(jsonWithEmptyArray);
        // assert
        assertNotNull(MassageReserveServiceFacilityBuilder.configs);
        assertNull(MassageReserveServiceFacilityBuilder.configs.getUpperAttr());
        assertNotNull(MassageReserveServiceFacilityBuilder.configs.getConfigs());
        assertTrue(MassageReserveServiceFacilityBuilder.configs.getConfigs().isEmpty());
    }
}
