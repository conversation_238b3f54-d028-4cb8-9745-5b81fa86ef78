package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProductAttrGetSkuAttrFirstValue1Test {

    private Map<String, AttrDTO> testAttrMap;

    private ProductAttr productAttr;

    private AttrDTO mockAttrDTO;

    @BeforeEach
    void setUp() {
        testAttrMap = new HashMap<>();
        mockAttrDTO = Mockito.mock(AttrDTO.class);
        productAttr = new ProductAttr(testAttrMap);
    }

    /**
     * 测试当skuAttr为null时，返回null
     */
    @Test
    void testGetSkuAttrFirstValue_WhenSkuAttrIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        ProductAttr nullAttrProduct = new ProductAttr(null);
        // act
        String result = nullAttrProduct.getSkuAttrFirstValue("anyAttr");
        // assert
        assertNull(result);
    }

    /**
     * 测试当attrName不存在时，返回null
     */
    @Test
    void testGetSkuAttrFirstValue_WhenAttrNotExist_ShouldReturnNull() throws Throwable {
        // arrange
        String nonExistAttr = "nonExistAttr";
        // act
        String result = productAttr.getSkuAttrFirstValue(nonExistAttr);
        // assert
        assertNull(result);
    }

    /**
     * 测试当attrName为null时，返回null
     */
    @Test
    void testGetSkuAttrFirstValue_WhenAttrNameIsNull_ShouldReturnNull() throws Throwable {
        // act
        String result = productAttr.getSkuAttrFirstValue(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试当attrName为空字符串时，返回null
     */
    @Test
    void testGetSkuAttrFirstValue_WhenAttrNameIsEmpty_ShouldReturnNull() throws Throwable {
        // act
        String result = productAttr.getSkuAttrFirstValue("");
        // assert
        assertNull(result);
    }

    /**
     * 测试当attrDTO存在但value为空时，返回null
     */
    @Test
    void testGetSkuAttrFirstValue_WhenValueIsEmpty_ShouldReturnNull() throws Throwable {
        // arrange
        String attrName = "testAttr";
        when(mockAttrDTO.getValue()).thenReturn(Collections.emptyList());
        testAttrMap.put(attrName, mockAttrDTO);
        // act
        String result = productAttr.getSkuAttrFirstValue(attrName);
        // assert
        assertNull(result);
        Mockito.verify(mockAttrDTO).getValue();
    }

    /**
     * 测试当attrDTO存在且value有单个元素时，返回该元素
     */
    @Test
    void testGetSkuAttrFirstValue_WhenValueHasSingleElement_ShouldReturnFirstElement() throws Throwable {
        // arrange
        String attrName = "testAttr";
        String expectedValue = "value1";
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList(expectedValue));
        testAttrMap.put(attrName, mockAttrDTO);
        // act
        String result = productAttr.getSkuAttrFirstValue(attrName);
        // assert
        assertEquals(expectedValue, result);
        Mockito.verify(mockAttrDTO).getValue();
    }

    /**
     * 测试当attrDTO存在且value有多个元素时，返回第一个元素
     */
    @Test
    void testGetSkuAttrFirstValue_WhenValueHasMultipleElements_ShouldReturnFirstElement() throws Throwable {
        // arrange
        String attrName = "testAttr";
        String expectedValue = "value1";
        when(mockAttrDTO.getValue()).thenReturn(Arrays.asList(expectedValue, "value2", "value3"));
        testAttrMap.put(attrName, mockAttrDTO);
        // act
        String result = productAttr.getSkuAttrFirstValue(attrName);
        // assert
        assertEquals(expectedValue, result);
        Mockito.verify(mockAttrDTO).getValue();
    }

    /**
     * 测试当attrDTO存在但value为null时，返回null
     */
    @Test
    void testGetSkuAttrFirstValue_WhenValueIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        String attrName = "testAttr";
        when(mockAttrDTO.getValue()).thenReturn(null);
        testAttrMap.put(attrName, mockAttrDTO);
        // act
        String result = productAttr.getSkuAttrFirstValue(attrName);
        // assert
        assertNull(result);
        Mockito.verify(mockAttrDTO).getValue();
    }

    @Test
    void testGetSkuAttrValueJson_WhenSkuAttrIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        ProductAttr productAttr = new ProductAttr(null);
        // act
        String result = productAttr.getSkuAttrValueJson("anyAttr");
        // assert
        assertNull(result);
    }

    @Test
    void testGetSkuAttrValueJson_WhenAttrNameNotExist_ShouldReturnNull() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("otherAttr", mock(AttrDTO.class));
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getSkuAttrValueJson("nonExistAttr");
        // assert
        assertNull(result);
    }

    @Test
    void testGetSkuAttrValueJson_WhenAttrValueIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getValue()).thenReturn(null);
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getSkuAttrValueJson("testAttr");
        // assert
        assertNull(result);
        verify(attrDTO, times(1)).getValue();
    }

    @Test
    void testGetSkuAttrValueJson_WhenAttrValueIsEmpty_ShouldReturnNull() throws Throwable {
        // arrange
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getValue()).thenReturn(Collections.emptyList());
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getSkuAttrValueJson("testAttr");
        // assert
        assertNull(result);
        verify(attrDTO, times(1)).getValue();
    }

    @Test
    void testGetSkuAttrValueJson_WhenAttrValueHasData_ShouldReturnJsonString() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("value1", "value2");
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getValue()).thenReturn(values);
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getSkuAttrValueJson("testAttr");
        // assert
        assertNotNull(result);
        assertEquals(JSON.toJSONString(values), result);
        verify(attrDTO, times(1)).getValue();
    }

    @Test
    void testGetSkuAttrValueJson_WhenJsonConversionFails_ShouldReturnNull() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("value1", "value2");
        AttrDTO attrDTO = mock(AttrDTO.class);
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        // 使用 spy 来部分模拟 ProductAttr
        ProductAttr productAttr = spy(new ProductAttr(skuAttr));
        // 模拟 JSON.toJSONString 抛出异常
        doThrow(new RuntimeException("JSON conversion error")).when(productAttr).getSkuAttrValueJson("testAttr");
        // act & assert
        assertThrows(RuntimeException.class, () -> productAttr.getSkuAttrValueJson("testAttr"));
    }
}
