package com.sankuai.dzshoppingguide.product.detail.application.builder.discountcard;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.shop.info.dto.PeriodStockM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card.reserve.shop.info.dto.ProductItemM;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import java.util.Collections;
import java.util.List;
import org.assertj.core.internal.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DiscountCardModuleBuilderTest {

    /**
     * Test when isTechStockShop is true, should return 15
     */
    @Test
    void testGetStockGranularity_TechStockShop_Returns15() throws Throwable {
        // arrange
        boolean isTechStockShop = true;
        ProductItemM productItemM = new ProductItemM();
        // act
        int result = new DiscountCardModuleBuilder().getStockGranularity(isTechStockShop, productItemM);
        // assert
        assertEquals(15, result);
    }

    /**
     * Test when config is null, should return 30
     */
    @Test
    void testGetStockGranularity_ConfigNull_Returns30() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(LionConfigUtils::getMiddleReserveConfig).thenReturn(null);
            // act
            int result = new DiscountCardModuleBuilder().getStockGranularity(isTechStockShop, productItemM);
            // assert
            assertEquals(30, result);
        }
    }

    /**
     * Test when stockGranularitySwitch is false, should return 30
     */
    @Test
    void testGetStockGranularity_SwitchOff_Returns30() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        DiscountCardModuleBuilder.MidReserveConfig config = new DiscountCardModuleBuilder.MidReserveConfig();
        config.setStockGranularitySwitch(false);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(LionConfigUtils::getMiddleReserveConfig).thenReturn(config);
            // act
            int result = new DiscountCardModuleBuilder().getStockGranularity(isTechStockShop, productItemM);
            // assert
            assertEquals(30, result);
        }
    }

    /**
     * Test when productItemM is null, should return 30
     */
    @Test
    void testGetStockGranularity_ProductItemNull_Returns30() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        DiscountCardModuleBuilder.MidReserveConfig config = new DiscountCardModuleBuilder.MidReserveConfig();
        config.setStockGranularitySwitch(true);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(LionConfigUtils::getMiddleReserveConfig).thenReturn(config);
            // act
            int result = new DiscountCardModuleBuilder().getStockGranularity(isTechStockShop, null);
            // assert
            assertEquals(30, result);
        }
    }

    /**
     * Test when bookPeriodStocks is empty, should return 30
     */
    @Test
    void testGetStockGranularity_EmptyPeriodStocks_Returns30() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        productItemM.setBookPeriodStocks(Collections.<PeriodStockM>emptyList());
        DiscountCardModuleBuilder.MidReserveConfig config = new DiscountCardModuleBuilder.MidReserveConfig();
        config.setStockGranularitySwitch(true);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(LionConfigUtils::getMiddleReserveConfig).thenReturn(config);
            // act
            int result = new DiscountCardModuleBuilder().getStockGranularity(isTechStockShop, productItemM);
            // assert
            assertEquals(30, result);
        }
    }

    /**
     * Test when periodStockM is null, should return 30
     */
    @Test
    void testGetStockGranularity_NullPeriodStock_Returns30() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        List<PeriodStockM> listWithNull = Collections.singletonList(null);
        productItemM.setBookPeriodStocks(listWithNull);
        DiscountCardModuleBuilder.MidReserveConfig config = new DiscountCardModuleBuilder.MidReserveConfig();
        config.setStockGranularitySwitch(true);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(LionConfigUtils::getMiddleReserveConfig).thenReturn(config);
            // act
            int result = new DiscountCardModuleBuilder().getStockGranularity(isTechStockShop, productItemM);
            // assert
            assertEquals(30, result);
        }
    }

    /**
     * Test when actual granularity is 30, should return 30
     */
    @Test
    void testGetStockGranularity_Granularity30_Returns30() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        PeriodStockM periodStockM = new PeriodStockM();
        periodStockM.setStockGranularity(30);
        List<PeriodStockM> stockList = Collections.singletonList(periodStockM);
        productItemM.setBookPeriodStocks(stockList);
        DiscountCardModuleBuilder.MidReserveConfig config = new DiscountCardModuleBuilder.MidReserveConfig();
        config.setStockGranularitySwitch(true);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(LionConfigUtils::getMiddleReserveConfig).thenReturn(config);
            // act
            int result = new DiscountCardModuleBuilder().getStockGranularity(isTechStockShop, productItemM);
            // assert
            assertEquals(30, result);
        }
    }

    /**
     * Test when actual granularity is 15, should return 30 (since we can't mock private method)
     */
    @Test
    void testGetStockGranularity_Granularity15_Returns30() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        PeriodStockM periodStockM = new PeriodStockM();
        periodStockM.setStockGranularity(15);
        List<PeriodStockM> stockList = Collections.singletonList(periodStockM);
        productItemM.setBookPeriodStocks(stockList);
        DiscountCardModuleBuilder.MidReserveConfig config = new DiscountCardModuleBuilder.MidReserveConfig();
        config.setStockGranularitySwitch(true);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(LionConfigUtils::getMiddleReserveConfig).thenReturn(config);
            // act
            int result = new DiscountCardModuleBuilder().getStockGranularity(isTechStockShop, productItemM);
            // assert
            assertEquals(30, result);
        }
    }

    /**
     * Test when actual granularity is 0, should return 30
     */
    @Test
    void testGetStockGranularity_Granularity0_Returns30() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        PeriodStockM periodStockM = new PeriodStockM();
        periodStockM.setStockGranularity(0);
        List<PeriodStockM> stockList = Collections.singletonList(periodStockM);
        productItemM.setBookPeriodStocks(stockList);
        DiscountCardModuleBuilder.MidReserveConfig config = new DiscountCardModuleBuilder.MidReserveConfig();
        config.setStockGranularitySwitch(true);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(LionConfigUtils::getMiddleReserveConfig).thenReturn(config);
            // act
            int result = new DiscountCardModuleBuilder().getStockGranularity(isTechStockShop, productItemM);
            // assert
            assertEquals(30, result);
        }
    }
}
