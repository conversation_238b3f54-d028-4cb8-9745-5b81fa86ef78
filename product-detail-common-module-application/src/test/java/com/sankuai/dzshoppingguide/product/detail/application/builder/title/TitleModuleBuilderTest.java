package com.sankuai.dzshoppingguide.product.detail.application.builder.title;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TitleModuleBuilderTest {

    /**
     * 测试当dealCategoryId等于MALL_DEAL_CATEGORY_ID(712)时返回true
     */
    @Test
    void testIsMallDealCategory_WhenCategoryIdEqualsMallDealCategoryId_ReturnsTrue() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        Integer dealCategoryId = 712;
        // act
        boolean result = builder.isMallDealCategory(dealCategoryId);
        // assert
        assertTrue(result, "当dealCategoryId等于712时应返回true");
    }

    /**
     * 测试当dealCategoryId不等于MALL_DEAL_CATEGORY_ID(712)时返回false
     */
    @Test
    void testIsMallDealCategory_WhenCategoryIdNotEqualsMallDealCategoryId_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        Integer dealCategoryId = 123;
        // act
        boolean result = builder.isMallDealCategory(dealCategoryId);
        // assert
        assertFalse(result, "当dealCategoryId不等于712时应返回false");
    }

    /**
     * 测试当dealCategoryId为null时返回false
     */
    @Test
    void testIsMallDealCategory_WhenCategoryIdIsNull_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        Integer dealCategoryId = null;
        // act
        boolean result = builder.isMallDealCategory(dealCategoryId);
        // assert
        assertFalse(result, "当dealCategoryId为null时应返回false");
    }

    /**
     * 测试当dealCategoryId为Integer.MAX_VALUE时返回false
     */
    @Test
    void testIsMallDealCategory_WhenCategoryIdIsMaxValue_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        Integer dealCategoryId = Integer.MAX_VALUE;
        // act
        boolean result = builder.isMallDealCategory(dealCategoryId);
        // assert
        assertFalse(result, "当dealCategoryId为Integer.MAX_VALUE时应返回false");
    }

    /**
     * 测试当dealCategoryId为Integer.MIN_VALUE时返回false
     */
    @Test
    void testIsMallDealCategory_WhenCategoryIdIsMinValue_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        Integer dealCategoryId = Integer.MIN_VALUE;
        // act
        boolean result = builder.isMallDealCategory(dealCategoryId);
        // assert
        assertFalse(result, "当dealCategoryId为Integer.MIN_VALUE时应返回false");
    }

    @Test
    void testIsFromMallFoodPoiVoucher_NullPageSourceAndNullCategoryId_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        // act
        boolean result = builder.isFromMallFoodPoiVoucher(null, null);
        // assert
        assertFalse(result);
    }

    @Test
    void testIsFromMallFoodPoiVoucher_EmptyPageSourceAndNullCategoryId_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        // act
        boolean result = builder.isFromMallFoodPoiVoucher("", null);
        // assert
        assertFalse(result);
    }

    @Test
    void testIsFromMallFoodPoiVoucher_NullPageSourceAndValidCategoryId_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        // act
        boolean result = builder.isFromMallFoodPoiVoucher(null, 712);
        // assert
        assertFalse(result);
    }

    @Test
    void testIsFromMallFoodPoiVoucher_WrongPageSourceAndCorrectCategoryId_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        // act
        boolean result = builder.isFromMallFoodPoiVoucher("wrongSource", 712);
        // assert
        assertFalse(result);
    }

    @Test
    void testIsFromMallFoodPoiVoucher_CorrectPageSourceAndWrongCategoryId_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        // act
        boolean result = builder.isFromMallFoodPoiVoucher("mallFoodPoiShelf", 123);
        // assert
        assertFalse(result);
    }

    @Test
    void testIsFromMallFoodPoiVoucher_CorrectPageSourceAndCorrectCategoryId_ReturnsTrue() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        // act
        boolean result = builder.isFromMallFoodPoiVoucher("mallFoodPoiShelf", 712);
        // assert
        assertTrue(result);
    }

    @Test
    void testIsFromMallFoodPoiVoucher_CaseMismatchPageSourceAndCorrectCategoryId_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        // act
        boolean result = builder.isFromMallFoodPoiVoucher("MallFoodPoiShelf", 712);
        // assert
        assertFalse(result);
    }

    @Test
    void testIsFromMallFoodPoiVoucher_CorrectPageSourceAndMaxValueCategoryId_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        // act
        boolean result = builder.isFromMallFoodPoiVoucher("mallFoodPoiShelf", Integer.MAX_VALUE);
        // assert
        assertFalse(result);
    }

    @Test
    void testIsFromMallFoodPoiVoucher_CorrectPageSourceAndMinValueCategoryId_ReturnsFalse() {
        // arrange
        TitleModuleBuilder builder = new TitleModuleBuilder();
        // act
        boolean result = builder.isFromMallFoodPoiVoucher("mallFoodPoiShelf", Integer.MIN_VALUE);
        // assert
        assertFalse(result);
    }
}
