package com.sankuai.dzshoppingguide.product.detail.application.mq.listener;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.meituan.mpproduct.general.model.dzMessage.DzProductChangeDTO;
import com.meituan.mpproduct.general.model.dzMessage.DzProductChangeMsg;
import com.sankuai.dzshoppingguide.product.detail.application.mq.acl.CategoryCacheService;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * Test class for ProductChangeConsumer
 */
public class ProductChangeConsumerTest {

    @Mock
    private CategoryCacheService categoryCacheService;

    @Mock
    private CompositeAtomService compositeAtomService;

    @InjectMocks
    private ProductChangeConsumer productChangeConsumer;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test case: Message is null
     * Expected: Should return CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessage_WhenMessageIsNull() throws Throwable {
        // arrange
        MafkaMessage message = null;
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case: Message body is empty
     * Expected: Should return CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessage_WhenMessageBodyIsEmpty() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("");
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case: Message body is null
     * Expected: Should return CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessage_WhenMessageBodyIsNull() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(null);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case: Product change message is null
     * Expected: Should return CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessage_WhenProductChangeMessageIsNull() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{}");
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case: Product change list is empty
     * Expected: Should return CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessage_WhenProductChangeListIsEmpty() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        String jsonMessage = "{\"productChangeDTOList\":[]}";
        when(message.getBody()).thenReturn(jsonMessage);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case: Product change list first item is null
     * Expected: Should return CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessage_WhenProductChangeListFirstItemIsNull() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        String jsonMessage = "{\"productChangeDTOList\":[null]}";
        when(message.getBody()).thenReturn(jsonMessage);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case: Unified product ID is null
     * Expected: Should return CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessage_WhenUnifiedProductIdIsNull() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        String jsonMessage = "{\"productChangeDTOList\":[{\"platformProductId\":null}]}";
        when(message.getBody()).thenReturn(jsonMessage);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case: Unified product ID is zero
     * Expected: Should return CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessage_WhenUnifiedProductIdIsZero() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        String jsonMessage = "{\"productChangeDTOList\":[{\"platformProductId\":0}]}";
        when(message.getBody()).thenReturn(jsonMessage);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test case: Product base info query returns empty
     * Expected: Should return RECONSUME_LATER
     */
    @Test
    public void testRecvMessage_WhenProductBaseInfoIsEmpty() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        String jsonMessage = "{\"productChangeDTOList\":[{\"platformProductId\":123}]}";
        when(message.getBody()).thenReturn(jsonMessage);
        when(compositeAtomService.getProductBaseInfoByUnifiedId(anyList())).thenReturn(Collections.emptyList());
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
        verify(compositeAtomService).getProductBaseInfoByUnifiedId(eq(Lists.newArrayList(123L)));
    }

    /**
     * Test case: Product base info first item is null
     * Expected: Should return RECONSUME_LATER
     */
    @Test
    public void testRecvMessage_WhenProductBaseInfoFirstItemIsNull() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        String jsonMessage = "{\"productChangeDTOList\":[{\"platformProductId\":123}]}";
        when(message.getBody()).thenReturn(jsonMessage);
        when(compositeAtomService.getProductBaseInfoByUnifiedId(anyList())).thenReturn(Collections.singletonList(null));
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
    }

    /**
     * Test case: Biz product ID is null, should clean DP and MT cache
     * Expected: Should return CONSUME_SUCCESS and clean both caches
     */
    @Test
    public void testRecvMessage_WhenBizProductIdIsNull() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        String jsonMessage = "{\"productChangeDTOList\":[{\"platformProductId\":123}]}";
        when(message.getBody()).thenReturn(jsonMessage);
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setBizProductId(null);
        dealGroup.setDpDealGroupId(456L);
        dealGroup.setMtDealGroupId(789L);
        when(compositeAtomService.getProductBaseInfoByUnifiedId(anyList())).thenReturn(Collections.singletonList(dealGroup));
        when(categoryCacheService.clean(any(IdTypeEnum.class), anyLong())).thenReturn(true);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(categoryCacheService).clean(IdTypeEnum.DP, 456L);
        verify(categoryCacheService).clean(IdTypeEnum.MT, 789L);
        verify(categoryCacheService, never()).clean(eq(IdTypeEnum.BIZ_PRODUCT), anyLong());
    }

    /**
     * Test case: Valid biz product ID, should clean BIZ_PRODUCT cache
     * Expected: Should return CONSUME_SUCCESS and clean BIZ_PRODUCT cache
     */
    @Test
    public void testRecvMessage_WhenBizProductIdIsValid() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        String jsonMessage = "{\"productChangeDTOList\":[{\"platformProductId\":123}]}";
        when(message.getBody()).thenReturn(jsonMessage);
        DealGroupDTO dealGroup = new DealGroupDTO();
        dealGroup.setBizProductId(456L);
        when(compositeAtomService.getProductBaseInfoByUnifiedId(anyList())).thenReturn(Collections.singletonList(dealGroup));
        when(categoryCacheService.clean(any(IdTypeEnum.class), anyLong())).thenReturn(true);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(categoryCacheService).clean(IdTypeEnum.BIZ_PRODUCT, 456L);
        verify(categoryCacheService, never()).clean(eq(IdTypeEnum.DP), anyLong());
        verify(categoryCacheService, never()).clean(eq(IdTypeEnum.MT), anyLong());
    }

    /**
     * Test case: General exception occurs
     * Expected: Should return RECONSUME_LATER
     */
    @Test
    public void testRecvMessage_WhenGeneralExceptionOccurs() throws Throwable {
        // arrange
        MafkaMessage message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"productChangeDTOList\":[{\"platformProductId\":123}]}");
        when(compositeAtomService.getProductBaseInfoByUnifiedId(anyList())).thenThrow(new RuntimeException("Test exception"));
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = productChangeConsumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
    }
}
