package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class MoxibustionStrategyImplTest {

    private MoxibustionStrategyImpl strategy;

    private List<ServiceProjectAttrDTO> mockAttrs;

    @BeforeEach
    void setUp() {
        strategy = new MoxibustionStrategyImpl();
        mockAttrs = new ArrayList<>();
    }

    private ServiceProjectAttrDTO createMockAttr(String attrName, String attrValue) {
        ServiceProjectAttrDTO mock = Mockito.mock(ServiceProjectAttrDTO.class);
        when(mock.getAttrName()).thenReturn(attrName);
        when(mock.getAttrValue()).thenReturn(attrValue);
        return mock;
    }

    /**
     * 测试当其他工具包含排烟设备时，应返回排烟设备（最高优先级）
     */
    @Test
    public void testGetToolValue_WhenContainsSmokeTool_ShouldReturnSmokeTool() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("unclassifiedTools", "排烟设备、其他工具"));
        mockAttrs.add(createMockAttr("moxibustionTool", "艾灸盒"));
        mockAttrs.add(createMockAttr("disposableMaterial", "生姜"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("排烟设备", result);
    }

    /**
     * 测试当不包含排烟设备但有艾灸工具时，应返回艾灸工具（第二优先级）
     */
    @Test
    public void testGetToolValue_WhenHasMoxibustionTool_ShouldReturnMoxibustionTool() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("unclassifiedTools", "其他工具"));
        mockAttrs.add(createMockAttr("moxibustionTool", "艾灸盒"));
        mockAttrs.add(createMockAttr("disposableMaterial", "生姜"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("艾灸盒", result);
    }

    /**
     * 测试当不包含前两者但有艾灸材料时，应返回艾灸材料（第三优先级）
     */
    @Test
    public void testGetToolValue_WhenHasMoxibustionMaterial_ShouldReturnMoxibustionMaterial() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("moxibustionMaterial", "艾条"));
        mockAttrs.add(createMockAttr("disposableMaterial", "生姜、盐"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("艾条", result);
    }

    /**
     * 测试当只有一次性材料时，应按优先级返回最高优先级的材料
     */
    @Test
    public void testGetToolValue_WhenOnlyDisposableMaterial_ShouldReturnHighestPriority() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("disposableMaterial", "一次性拖鞋、生姜、盐"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("生姜", result);
    }

    /**
     * 测试当输入为空列表时，应返回null
     */
    @Test
    public void testGetToolValue_WhenEmptyInput_ShouldReturnNull() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> emptyList = new ArrayList<>();
        // act
        String result = strategy.getToolValue(emptyList);
        // assert
        assertNull(result);
    }

    /**
     * 测试混合工具的优先级排序
     */
    @Test
    public void testGetToolValue_WhenMixedToolsWithPriorityItems_ShouldReturnHighestPriority() throws Throwable {
        // arrange
        // 只在disposableMaterial中添加电动按摩床
        mockAttrs.add(createMockAttr("disposableMaterial", "电动按摩床、一次性拖鞋"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("电动按摩床", result);
    }

    /**
     * 测试当所有属性都为空字符串时的情况
     */
    @Test
    public void testGetToolValue_WhenAllEmptyStrings_ShouldReturnNull() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("unclassifiedTools", ""));
        mockAttrs.add(createMockAttr("moxibustionTool", ""));
        mockAttrs.add(createMockAttr("moxibustionMaterial", ""));
        mockAttrs.add(createMockAttr("disposableMaterial", ""));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertNull(result);
    }
}
