package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.google.common.collect.Lists;
import org.junit.Ignore;
import org.junit.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * <AUTHOR>
 * @date 2025/5/8 15:55
 */
public class AvailableTimeHelperTest {
    @Test
    @Ignore
    public void test() {
        int todayWeekDay = getTodayWeekDay();
        System.out.println("todayWeekDay = " + todayWeekDay);

        int i = UnavailableReminderInfoUtils.dayOfWeek();
        System.out.println("i = " + i);
    }
    
    @Test
    public void testHasThreeConsecutiveNumbers() {
        // 测试用例1: 有3个连续数字
        List<Integer> numbers1 = Lists.newArrayList(1, 2, 3, 5);
        boolean result1 = hasThreeConsecutiveNumbers(numbers1);
        System.out.println("测试用例1: " + numbers1 + " 是否有3个连续数字: " + result1);
        assert result1;

        // 测试用例2: 没有3个连续数字
        List<Integer> numbers2 = Lists.newArrayList(1, 3, 5, 7);
        boolean result2 = hasThreeConsecutiveNumbers(numbers2);
        System.out.println("测试用例2: " + numbers2 + " 是否有3个连续数字: " + result2);
        assert !result2;

        // 测试用例3: 有4个连续数字
        List<Integer> numbers3 = Lists.newArrayList(1, 2, 3, 4);
        boolean result3 = hasThreeConsecutiveNumbers(numbers3);
        System.out.println("测试用例3: " + numbers3 + " 是否有3个连续数字: " + result3);
        assert result3;

        // 测试用例4: 数字不连续
        List<Integer> numbers4 = Lists.newArrayList(1, 2, 4, 5);
        boolean result4 = hasThreeConsecutiveNumbers(numbers4);
        System.out.println("测试用例4: " + numbers4 + " 是否有3个连续数字: " + result4);
        assert !result4;

        // 测试用例5: 数字重复
        List<Integer> numbers5 = Lists.newArrayList(1, 1, 2, 3);
        boolean result5 = hasThreeConsecutiveNumbers(numbers5);
        System.out.println("测试用例5: " + numbers5 + " 是否有3个连续数字: " + result5);
        assert result5;
    }

    @Test
    public void testGetWeekDayStr() {
        // 测试连续天数的情况
        assertConsecutiveDays("周一至周三", Arrays.asList(1, 2, 3));
        assertConsecutiveDays("周二至周六", Arrays.asList(2, 3, 4, 5, 6));
        assertConsecutiveDays("周一至周三、周五、周六", Arrays.asList(1, 2, 3, 5, 6));
        assertConsecutiveDays("周一、周二、周四至周六", Arrays.asList(1, 2, 4, 5, 6));
        assertConsecutiveDays("周一、周三", Arrays.asList(1, 3));
        assertConsecutiveDays("周日", Arrays.asList(7));
        assertConsecutiveDays("周一至周三、周五至周日", Arrays.asList(1, 2, 3, 5, 6, 7));

        // 测试非连续天数的情况
        assertNonConsecutiveDays("周一、周二、周三", Arrays.asList(3, 1, 2));
        assertNonConsecutiveDays("周五", Arrays.asList(5));
        assertNonConsecutiveDays("周一、周三、周五、周日", Arrays.asList(1, 3, 5, 7));
        assertNonConsecutiveDays("周一、周二、周三、周四、周五、周六、周日", Arrays.asList(1, 2, 3, 4, 5, 6, 7));
//        assertNonConsecutiveDays("", Collections.emptyList());
    }

    private void assertConsecutiveDays(String expected, List<Integer> input) {
        String actual = buildConsecutiveDaysDoc(input);
        System.out.printf("连续天数测试 - 输入: %s, 期望: %s, 实际: %s%n", input, expected, actual);
        assertEquals(expected, actual);
    }

    private void assertNonConsecutiveDays(String expected, List<Integer> input) {
        String actual = buildNonConsecutiveDaysDoc(input);
        System.out.printf("非连续天数测试 - 输入: %s, 期望: %s, 实际: %s%n", input, expected, actual);
        assertEquals(expected, actual);
    }

}