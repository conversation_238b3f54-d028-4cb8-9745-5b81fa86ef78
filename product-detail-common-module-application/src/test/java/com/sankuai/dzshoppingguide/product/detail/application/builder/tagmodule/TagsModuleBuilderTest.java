package com.sankuai.dzshoppingguide.product.detail.application.builder.tagmodule;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.ugc.review.remote.dto.ReviewCount;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.TagsEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.tag.vo.ReviewTagVO;
import java.util.HashMap;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;
import com.dianping.reviewremote.remote.dto.ReviewStarDistributionDTO;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class TagsModuleBuilderTest {

    private TagsModuleBuilder tagsModuleBuilder;

    @Mock
    private ProductDetailPageRequest request;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        tagsModuleBuilder = new TagsModuleBuilder();
        ReflectionTestUtils.setField(tagsModuleBuilder, "request", request);
    }

    /**
     * Test case for null ReviewCount input
     */
    @Test
    public void testBuildMtGoodReview_NullInput() throws Throwable {
        // arrange
        ReviewCount reviewCount = null;
        // act
        Optional<ReviewTagVO> result = tagsModuleBuilder.buildMtGoodReview(reviewCount);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test case for zero reviews
     */
    @Test
    public void testBuildMtGoodReview_ZeroReviews() throws Throwable {
        // arrange
        ReviewCount reviewCount = new ReviewCount();
        reviewCount.setAll(0);
        HashMap<Integer, Integer> stars = new HashMap<>();
        stars.put(50, 0);
        stars.put(45, 0);
        stars.put(40, 0);
        reviewCount.setStars(stars);
        // act
        Optional<ReviewTagVO> result = tagsModuleBuilder.buildMtGoodReview(reviewCount);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test case for valid reviews with good ratings
     */
    @Test
    public void testBuildMtGoodReview_ValidReviewsWithGoodRatings() throws Throwable {
        // arrange
        ReviewCount reviewCount = new ReviewCount();
        reviewCount.setAll(100);
        HashMap<Integer, Integer> stars = new HashMap<>();
        stars.put(50, 40);
        stars.put(45, 30);
        stars.put(40, 20);
        stars.put(35, 5);
        stars.put(30, 5);
        reviewCount.setStars(stars);
        reviewCount.setAvgRate(45);
        when(request.getProductId()).thenReturn(12345L);
        // MT_APP = 200
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(200));
        // act
        Optional<ReviewTagVO> result = tagsModuleBuilder.buildMtGoodReview(reviewCount);
        // assert
        assertTrue(result.isPresent());
        ReviewTagVO reviewTagVO = result.get();
        assertEquals(TagsEnum.REVIEW.getCode(), reviewTagVO.getType());
        assertEquals("90%", reviewTagVO.getGoodReviewRatio());
        assertEquals("4.5", reviewTagVO.getReviewScore());
        assertTrue(reviewTagVO.isPrefixIcon());
        assertTrue(reviewTagVO.isSuffixIcon());
    }

    /**
     * Test case for reviews with no good ratings
     */
    @Test
    public void testBuildMtGoodReview_NoGoodRatings() throws Throwable {
        // arrange
        ReviewCount reviewCount = new ReviewCount();
        reviewCount.setAll(100);
        HashMap<Integer, Integer> stars = new HashMap<>();
        stars.put(50, 0);
        stars.put(45, 0);
        stars.put(40, 0);
        stars.put(35, 50);
        stars.put(30, 50);
        reviewCount.setStars(stars);
        when(request.getProductId()).thenReturn(12345L);
        // MT_APP = 200
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(200));
        // act
        Optional<ReviewTagVO> result = tagsModuleBuilder.buildMtGoodReview(reviewCount);
        // assert
        assertTrue(result.isPresent());
        ReviewTagVO reviewTagVO = result.get();
        assertEquals("0%", reviewTagVO.getGoodReviewRatio());
        // Changed from assertFalse to assertTrue
        assertTrue(reviewTagVO.isPrefixIcon());
        assertNotNull(reviewTagVO.getContents());
        assertTrue(reviewTagVO.getContents().size() > 0);
    }

    /**
     * Test case for reviews with average rating
     */
    @Test
    public void testBuildMtGoodReview_WithAverageRating() throws Throwable {
        // arrange
        ReviewCount reviewCount = new ReviewCount();
        reviewCount.setAll(100);
        HashMap<Integer, Integer> stars = new HashMap<>();
        stars.put(50, 20);
        stars.put(45, 20);
        stars.put(40, 20);
        stars.put(35, 20);
        stars.put(30, 20);
        reviewCount.setStars(stars);
        reviewCount.setAvgRate(42);
        when(request.getProductId()).thenReturn(12345L);
        // DP_APP = 100
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(100));
        // act
        Optional<ReviewTagVO> result = tagsModuleBuilder.buildMtGoodReview(reviewCount);
        // assert
        assertTrue(result.isPresent());
        ReviewTagVO reviewTagVO = result.get();
        assertEquals("4.2", reviewTagVO.getReviewScore());
        assertEquals("60%", reviewTagVO.getGoodReviewRatio());
        assertTrue(reviewTagVO.isPrefixIcon());
        assertNotNull(reviewTagVO.getContents());
        assertTrue(reviewTagVO.getContents().size() > 0);
    }

    /**
     * Test case for different client types
     */
    @Test
    public void testBuildMtGoodReview_DifferentClientTypes() throws Throwable {
        // arrange
        ReviewCount reviewCount = new ReviewCount();
        reviewCount.setAll(100);
        HashMap<Integer, Integer> stars = new HashMap<>();
        stars.put(50, 30);
        stars.put(45, 0);
        stars.put(40, 0);
        stars.put(35, 35);
        stars.put(30, 35);
        reviewCount.setStars(stars);
        when(request.getProductId()).thenReturn(12345L);
        // Test DP_APP client type
        // DP_APP = 100
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(100));
        Optional<ReviewTagVO> dpResult = tagsModuleBuilder.buildMtGoodReview(reviewCount);
        assertTrue(dpResult.isPresent());
        assertTrue(dpResult.get().getLink().startsWith("dianping://"));
        // Test MT_APP client type
        // MT_APP = 200
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(200));
        Optional<ReviewTagVO> mtResult = tagsModuleBuilder.buildMtGoodReview(reviewCount);
        assertTrue(mtResult.isPresent());
        assertTrue(mtResult.get().getLink().startsWith("imeituan://"));
    }

    @Test
    public void testBuildDpGoodReview_NullInput() throws Throwable {
        // arrange
        TagsModuleBuilder builder = new TagsModuleBuilder();
        // act
        Optional<ReviewTagVO> result = builder.buildDpGoodReview(null);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildDpGoodReview_ZeroReviews() throws Throwable {
        // arrange
        TagsModuleBuilder builder = new TagsModuleBuilder();
        ReviewStarDistributionDTO reviewStar = new ReviewStarDistributionDTO(123L);
        reviewStar.setReviewCount(0);
        // act
        Optional<ReviewTagVO> result = builder.buildDpGoodReview(reviewStar);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildDpGoodReview_ZeroTotalCount() throws Throwable {
        // arrange
        TagsModuleBuilder builder = new TagsModuleBuilder();
        ReviewStarDistributionDTO reviewStar = new ReviewStarDistributionDTO(123L);
        reviewStar.setReviewCount(0);
        reviewStar.setStar5Count(10);
        // act
        Optional<ReviewTagVO> result = builder.buildDpGoodReview(reviewStar);
        // assert
        assertFalse(result.isPresent());
    }
}
