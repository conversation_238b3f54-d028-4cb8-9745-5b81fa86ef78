package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.PriceProtectionTagDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class GuaranteeUtilsTest {

    /**
     * 测试当输入参数为null时返回false
     */
    @Test
    @DisplayName("Should return false when input is null")
    public void testCheckPriceProtectionValid_NullInput() throws Throwable {
        // arrange - no arrangement needed for null input
        // act
        boolean result = GuaranteeUtils.checkPriceProtectionValid(null);
        // assert
        assertFalse(result, "Should return false for null input");
    }

    /**
     * 测试当价格保护标签为null时返回false
     */
    @Test
    @DisplayName("Should return false when price protection tag is null")
    public void testCheckPriceProtectionValid_NullPriceProtectionTag() throws Throwable {
        // arrange
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        when(mockInput.getPriceProtectionTag()).thenReturn(null);
        // act
        boolean result = GuaranteeUtils.checkPriceProtectionValid(mockInput);
        // assert
        assertFalse(result, "Should return false when price protection tag is null");
        verify(mockInput).getPriceProtectionTag();
    }

    /**
     * 测试当价格保护标签有效但valid为false时返回false
     */
    @Test
    @DisplayName("Should return false when price protection tag is not valid")
    public void testCheckPriceProtectionValid_InvalidTag() throws Throwable {
        // arrange
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        PriceProtectionTagDTO mockTag = mock(PriceProtectionTagDTO.class);
        when(mockInput.getPriceProtectionTag()).thenReturn(mockTag);
        when(mockTag.getValid()).thenReturn(false);
        // act
        boolean result = GuaranteeUtils.checkPriceProtectionValid(mockInput);
        // assert
        assertFalse(result, "Should return false when tag is not valid");
        verify(mockInput).getPriceProtectionTag();
        // 修改为期望调用2次
        verify(mockTag, times(2)).getValid();
    }

    /**
     * 测试当价格保护标签有效但valid为null时返回false
     */
    @Test
    @DisplayName("Should return false when valid field is null")
    public void testCheckPriceProtectionValid_NullValidField() throws Throwable {
        // arrange
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        PriceProtectionTagDTO mockTag = mock(PriceProtectionTagDTO.class);
        when(mockInput.getPriceProtectionTag()).thenReturn(mockTag);
        when(mockTag.getValid()).thenReturn(null);
        // act
        boolean result = GuaranteeUtils.checkPriceProtectionValid(mockInput);
        // assert
        assertFalse(result, "Should return false when valid field is null");
        verify(mockInput).getPriceProtectionTag();
        verify(mockTag).getValid();
    }

    /**
     * 测试当价格保护标签有效但validityDays为null时返回false
     */
    @Test
    @DisplayName("Should return false when validity days is null")
    public void testCheckPriceProtectionValid_NullValidityDays() throws Throwable {
        // arrange
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        PriceProtectionTagDTO mockTag = mock(PriceProtectionTagDTO.class);
        when(mockInput.getPriceProtectionTag()).thenReturn(mockTag);
        when(mockTag.getValid()).thenReturn(true);
        when(mockTag.getValidityDays()).thenReturn(null);
        // act
        boolean result = GuaranteeUtils.checkPriceProtectionValid(mockInput);
        // assert
        assertFalse(result, "Should return false when validity days is null");
        verify(mockInput).getPriceProtectionTag();
        // 修改为期望调用2次
        verify(mockTag, times(2)).getValid();
        verify(mockTag).getValidityDays();
    }

    /**
     * 测试当所有条件都满足时返回true
     */
    @Test
    @DisplayName("Should return true when all conditions are met")
    public void testCheckPriceProtectionValid_AllConditionsMet() throws Throwable {
        // arrange
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        PriceProtectionTagDTO mockTag = mock(PriceProtectionTagDTO.class);
        when(mockInput.getPriceProtectionTag()).thenReturn(mockTag);
        when(mockTag.getValid()).thenReturn(true);
        when(mockTag.getValidityDays()).thenReturn(7);
        // act
        boolean result = GuaranteeUtils.checkPriceProtectionValid(mockInput);
        // assert
        assertTrue(result, "Should return true when all conditions are met");
        verify(mockInput).getPriceProtectionTag();
        // 修改为期望调用2次
        verify(mockTag, times(2)).getValid();
        verify(mockTag).getValidityDays();
    }
}
