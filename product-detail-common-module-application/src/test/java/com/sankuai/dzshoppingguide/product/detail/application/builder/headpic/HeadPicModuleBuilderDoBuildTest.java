// package com.sankuai.dzshoppingguide.product.detail.application.builder.headpic;
//
// import static org.junit.jupiter.api.Assertions.*;
// import static org.mockito.Mockito.*;
// import com.sankuai.dz.api.module.arrange.framework.builder.metadata.definition.extend.CommonBuilderDefinition;
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
// import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
// import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.ProductIdDTO;
// import com.sankuai.dzshoppingguide.product.detail.spi.dtos.HeadPicModuleVO;
// import com.sankuai.dzshoppingguide.product.detail.spi.enums.ContentType;
// import com.sankuai.dzshoppingguide.product.detail.spi.headpic.vo.HeadPicModulesVO;
// import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
// import com.sankuai.general.product.query.center.client.dto.video.DealGroupVideoDTO;
// import java.util.Collections;
// import java.util.Map;
// import java.util.Optional;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
// import org.mockito.junit.*;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// @ExtendWith(MockitoExtension.class)
// public class HeadPicModuleBuilderDoBuildTest {
//
//     @Mock
//     private ProductDetailPageRequest request;
//
//     @Mock
//     private ProductBaseInfo baseInfo;
//
//     @Mock
//     private ProductCategory productCategory;
//
//     @Mock
//     private DealGroupImageDTO imageDTO;
//
//     private TestableHeadPicModuleBuilder builder;
//
//     private static class TestableHeadPicModuleBuilder extends HeadPicModuleBuilder {
//
//         private ProductBaseInfo mockBaseInfo;
//
//         private ProductCategory mockCategory;
//
//         private ProductDetailPageRequest mockRequest;
//
//         public void setMockResults(ProductDetailPageRequest request, ProductBaseInfo baseInfo, ProductCategory category) {
//             this.mockRequest = request;
//             this.mockBaseInfo = baseInfo;
//             this.mockCategory = category;
//         }
//
//         @Override
//         public void extraInitBuilder(ProductDetailPageRequest request, Map<String, BaseFetcherContext> fetcherContextMap, CommonBuilderDefinition builderDefinition) {
//             this.request = request;
//         }
//
//         @Override
//         protected <T extends FetcherReturnValueDTO> T getDependencyResult(Class<? extends BaseFetcherContext> dependencyFetcherClass) {
//             if (dependencyFetcherClass.equals(ProductBaseInfoFetcher.class)) {
//                 return (T) mockBaseInfo;
//             } else if (dependencyFetcherClass.equals(ProductCategoryFetcher.class)) {
//                 return (T) mockCategory;
//             }
//             return null;
//         }
//     }
//
//     @BeforeEach
//     void setUp() {
//         builder = new TestableHeadPicModuleBuilder();
//         builder.setMockResults(request, null, null);
//         builder.extraInitBuilder(request, Collections.emptyMap(), null);
//     }
//
//     /**
//      * Tests when all dependencies are null
//      */
//     @Test
//     public void testDoBuildAllDependenciesNull() throws Throwable {
//         // arrange
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
//         builder.setMockResults(request, null, null);
//         // act
//         HeadPicModulesVO result = builder.doBuild();
//         // assert
//         assertNull(result);
//     }
//
//     /**
//      * Tests when baseInfo is null
//      */
//     @Test
//     public void testDoBuildBaseInfoNull() throws Throwable {
//         // arrange
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
//         builder.setMockResults(request, null, productCategory);
//         // act
//         HeadPicModulesVO result = builder.doBuild();
//         // assert
//         assertNull(result);
//     }
//
//     /**
//      * Tests when imageDTO is null
//      */
//     @Test
//     public void testDoBuildImageNull() throws Throwable {
//         // arrange
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
//         when(baseInfo.getImage()).thenReturn(null);
//         builder.setMockResults(request, baseInfo, productCategory);
//         // act
//         HeadPicModulesVO result = builder.doBuild();
//         // assert
//         assertNull(result);
//     }
//
//     /**
//      * Tests when product category is null
//      */
//     @Test
//     public void testDoBuildProductCategoryNull() throws Throwable {
//         // arrange
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
//         when(baseInfo.getImage()).thenReturn(imageDTO);
//         builder.setMockResults(request, baseInfo, null);
//         // act
//         HeadPicModulesVO result = builder.doBuild();
//         // assert
//         assertNotNull(result);
//         assertNotNull(result.getHeadPicModules());
//         assertTrue(result.getHeadPicModules().isEmpty());
//     }
//
//     /**
//      * Tests when no videos or pictures exist
//      */
//     @Test
//     public void testDoBuildNoVideosNoPictures() throws Throwable {
//         // arrange
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
//         when(baseInfo.getImage()).thenReturn(imageDTO);
//         when(productCategory.getProductSecondCategoryId()).thenReturn(123);
//         when(imageDTO.getAllVideos()).thenReturn(Collections.emptyList());
//         when(imageDTO.getDefaultPicPath()).thenReturn(null);
//         when(imageDTO.getAllPicPaths()).thenReturn(null);
//         builder.setMockResults(request, baseInfo, productCategory);
//         // act
//         HeadPicModulesVO result = builder.doBuild();
//         // assert
//         assertNotNull(result);
//         assertNotNull(result.getHeadPicModules());
//         assertTrue(result.getHeadPicModules().isEmpty());
//     }
//
//     /**
//      * Tests when videos exist but no pictures
//      */
//     @Test
//     public void testDoBuildWithVideosNoPictures() throws Throwable {
//         // arrange
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
//         when(baseInfo.getImage()).thenReturn(imageDTO);
//         when(productCategory.getProductSecondCategoryId()).thenReturn(123);
//         DealGroupVideoDTO videoDTO = mock(DealGroupVideoDTO.class);
//         when(videoDTO.getVideoPath()).thenReturn("videoPath");
//         when(videoDTO.getVideoCoverPath()).thenReturn("coverPath");
//         when(videoDTO.getVideoSize()).thenReturn(1024L);
//         when(imageDTO.getAllVideos()).thenReturn(Collections.singletonList(videoDTO));
//         when(imageDTO.getDefaultPicPath()).thenReturn(null);
//         when(imageDTO.getAllPicPaths()).thenReturn(null);
//         builder.setMockResults(request, baseInfo, productCategory);
//         // act
//         HeadPicModulesVO result = builder.doBuild();
//         // assert
//         assertNotNull(result);
//         assertNotNull(result.getHeadPicModules());
//         assertEquals(1, result.getHeadPicModules().size());
//         HeadPicModuleVO module = result.getHeadPicModules().get(0);
//         assertEquals(ContentType.VIDEO.getType(), module.getType());
//         assertEquals("coverPath", module.getContent());
//         assertEquals("videoPath", module.getVideoUrl());
//         assertEquals("当前Wi-Fi环境确定播放？预计花费流量1.00M", module.getDesc());
//     }
// }
