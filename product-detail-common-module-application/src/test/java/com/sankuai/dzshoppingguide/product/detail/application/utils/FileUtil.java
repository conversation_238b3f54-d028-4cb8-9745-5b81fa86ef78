package com.sankuai.dzshoppingguide.product.detail.application.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class FileUtil {

    public static String file2str(String inputFile) {
        try {
            URL resource = Thread.currentThread().getContextClassLoader().getResource(inputFile);
            if (resource == null) {
                throw new RuntimeException("null resource");
            }
            FileInputStream outputStream = new FileInputStream(resource.getFile());
            BufferedReader in = new BufferedReader(new InputStreamReader(outputStream, StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            String str;
            while ((str = in.readLine()) != null) {
                sb.append(str);
            }
            in.close();
            return sb.toString();
        } catch (Exception e) {
            return null;
        }
    }

    public static File getFile(String fileName) {
        URL resource = Thread.currentThread().getContextClassLoader().getResource(fileName);
        File groovyFile = new File(resource.getFile());
        return groovyFile;
    }
}
