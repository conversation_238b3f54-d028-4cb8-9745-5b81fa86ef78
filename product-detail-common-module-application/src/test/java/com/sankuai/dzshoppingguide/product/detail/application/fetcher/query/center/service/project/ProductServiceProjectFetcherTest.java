package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.sankuai.general.product.query.center.client.builder.model.ServiceProjectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProductServiceProjectFetcherTest {

    @Mock
    private QueryByDealGroupIdRequestBuilder requestBuilder;

    /**
     * Tests happy path where all values are present in the chain
     */
    @Test
    public void testMapResultAllValuesPresent() throws Throwable {
        // arrange
        ProductServiceProjectFetcher fetcher = new ProductServiceProjectFetcher();
        DealGroupServiceProjectDTO serviceProjectDTO = new DealGroupServiceProjectDTO();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setServiceProject(serviceProjectDTO);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductServiceProject> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertEquals(serviceProjectDTO, result.getReturnValue().getServiceProject());
    }

    /**
     * Tests when aggregateResult is null
     */
    @Test
    public void testMapResultNullAggregateResult() throws Throwable {
        // arrange
        ProductServiceProjectFetcher fetcher = new ProductServiceProjectFetcher();
        // act
        FetcherResponse<ProductServiceProject> result = fetcher.mapResult(null);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue().getServiceProject());
    }

    /**
     * Tests when returnValue in FetcherResponse is null
     */
    @Test
    public void testMapResultNullReturnValue() throws Throwable {
        // arrange
        ProductServiceProjectFetcher fetcher = new ProductServiceProjectFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(null);
        // act
        FetcherResponse<ProductServiceProject> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue().getServiceProject());
    }

    /**
     * Tests when DealGroupDTO in QueryCenterAggregateReturnValue is null
     */
    @Test
    public void testMapResultNullDealGroupDTO() throws Throwable {
        // arrange
        ProductServiceProjectFetcher fetcher = new ProductServiceProjectFetcher();
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(null);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductServiceProject> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue().getServiceProject());
    }

    /**
     * Tests when ServiceProject in DealGroupDTO is null
     */
    @Test
    public void testMapResultNullServiceProject() throws Throwable {
        // arrange
        ProductServiceProjectFetcher fetcher = new ProductServiceProjectFetcher();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setServiceProject(null);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductServiceProject> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue().getServiceProject());
    }

    @Test
    public void testFulfillRequestNormalCase() throws Throwable {
        // arrange
        ProductServiceProjectFetcher fetcher = new ProductServiceProjectFetcher();
        when(requestBuilder.serviceProject(any(ServiceProjectBuilder.class))).thenReturn(requestBuilder);
        // act
        fetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder).serviceProject(any(ServiceProjectBuilder.class));
    }

    @Test
    public void testFulfillRequestVerifiesAllFieldsRequested() throws Throwable {
        // arrange
        ProductServiceProjectFetcher fetcher = new ProductServiceProjectFetcher();
        when(requestBuilder.serviceProject(any(ServiceProjectBuilder.class))).thenReturn(requestBuilder);
        // act
        fetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder).serviceProject(any(ServiceProjectBuilder.class));
    }

    @Test
    public void testFulfillRequestWithNullBuilder() throws Throwable {
        // arrange
        ProductServiceProjectFetcher fetcher = new ProductServiceProjectFetcher();
        // act & assert
        assertThrows(NullPointerException.class, () -> fetcher.fulfillRequest(null));
    }
}
