package com.sankuai.dzshoppingguide.product.detail.application.builder.headpic;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.HeadPicModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ImagesSize;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.SpritePicModule;
import com.sankuai.general.product.query.center.client.dto.video.ExtendVideoDTO;
import com.sankuai.general.product.query.center.client.dto.video.SpriteImageDTO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.ProductIdDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ContentType;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ImageScaleEnum;
import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
import java.lang.reflect.Field;
import java.util.List;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
class HeadPicModuleBuilderTest {

    @InjectMocks
    private HeadPicModuleBuilder builder;

    private HeadPicModuleVO video;

    @Mock
    private ProductBaseInfo baseInfo;

    @Mock
    private DealGroupImageDTO imageDTO;

    @BeforeEach
    void setUp() {
        video = new HeadPicModuleVO(1, "content");
    }

    /**
     * Test when ExtendVideoDTO is null
     * Should not modify video object
     */
    @Test
    public void testBuildSpritePicByExtendVideoDto_NullExtendVideoDto() throws Throwable {
        // act
        builder.buildSpritePicByExtendVideoDto(video, null);
        // assert
        assertNull(video.getSpritePic());
    }

    /**
     * Test when spriteImageList is null
     * Should not modify video object
     */
    @Test
    public void testBuildSpritePicByExtendVideoDto_NullSpriteImageList() throws Throwable {
        // arrange
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        extendVideoDTO.setSpriteImageList(null);
        // act
        builder.buildSpritePicByExtendVideoDto(video, extendVideoDTO);
        // assert
        assertNull(video.getSpritePic());
    }

    /**
     * Test when spriteImageList is empty
     * Should not modify video object
     */
    @Test
    public void testBuildSpritePicByExtendVideoDto_EmptySpriteImageList() throws Throwable {
        // arrange
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        extendVideoDTO.setSpriteImageList(Collections.emptyList());
        // act
        builder.buildSpritePicByExtendVideoDto(video, extendVideoDTO);
        // assert
        assertNull(video.getSpritePic());
    }

    /**
     * Test when first spriteImage is null
     * Should not modify video object
     */
    @Test
    public void testBuildSpritePicByExtendVideoDto_FirstSpriteImageNull() throws Throwable {
        // arrange
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        extendVideoDTO.setSpriteImageList(Collections.singletonList(null));
        // act
        builder.buildSpritePicByExtendVideoDto(video, extendVideoDTO);
        // assert
        assertNull(video.getSpritePic());
    }

    /**
     * Test valid ExtendVideoDTO with valid SpriteImageDTO
     * Should set SpritePicModule with correct values
     */
    @Test
    public void testBuildSpritePicByExtendVideoDto_ValidInput() throws Throwable {
        // arrange
        SpriteImageDTO spriteImage = new SpriteImageDTO();
        spriteImage.setPath("test/path");
        spriteImage.setWidth(100);
        spriteImage.setHeight(200);
        spriteImage.setSubImageCount(50);
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        extendVideoDTO.setSpriteImageList(Collections.singletonList(spriteImage));
        // act
        builder.buildSpritePicByExtendVideoDto(video, extendVideoDTO);
        // assert
        SpritePicModule spritePic = video.getSpritePic();
        assertNotNull(spritePic);
        assertEquals("test/path", spritePic.getSpritePicUrl());
        assertEquals(50, spritePic.getTotalCount());
        assertEquals(10, spritePic.getRow());
        assertEquals(10, spritePic.getColumn());
        ImagesSize cellSize = spritePic.getSpriteCellSize();
        assertNotNull(cellSize);
        assertEquals(100, cellSize.getWidth());
        assertEquals(200, cellSize.getHeight());
        ImagesSize allSize = spritePic.getAllSpriteImageSize();
        assertNotNull(allSize);
        assertEquals(1000, allSize.getWidth());
        assertEquals(2000, allSize.getHeight());
    }

    /**
     * Test when SpriteImageDTO has null width/height
     * Both spriteCellSize and allSpriteImageSize should be null
     */
    @Test
    public void testBuildSpritePicByExtendVideoDto_NullDimensions() throws Throwable {
        // arrange
        SpriteImageDTO spriteImage = new SpriteImageDTO();
        spriteImage.setPath("test/path");
        // Use 0 instead of null
        spriteImage.setWidth(0);
        // Use 0 instead of null
        spriteImage.setHeight(0);
        spriteImage.setSubImageCount(50);
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        extendVideoDTO.setSpriteImageList(Collections.singletonList(spriteImage));
        // act
        builder.buildSpritePicByExtendVideoDto(video, extendVideoDTO);
        // assert
        SpritePicModule spritePic = video.getSpritePic();
        assertNotNull(spritePic);
        assertEquals("test/path", spritePic.getSpritePicUrl());
        assertEquals(50, spritePic.getTotalCount());
        assertEquals(10, spritePic.getRow());
        assertEquals(10, spritePic.getColumn());
        ImagesSize cellSize = spritePic.getSpriteCellSize();
        assertNotNull(cellSize);
        assertEquals(0, cellSize.getWidth());
        assertEquals(0, cellSize.getHeight());
        ImagesSize allSize = spritePic.getAllSpriteImageSize();
        assertNotNull(allSize);
        assertEquals(0, allSize.getWidth());
        assertEquals(0, allSize.getHeight());
    }

    /**
     * Test when multiple sprite images exist
     * Should only use first one
     */
    @Test
    public void testBuildSpritePicByExtendVideoDto_MultipleSpriteImages() throws Throwable {
        // arrange
        SpriteImageDTO firstImage = new SpriteImageDTO();
        firstImage.setPath("first/path");
        firstImage.setWidth(100);
        firstImage.setHeight(100);
        firstImage.setSubImageCount(10);
        SpriteImageDTO secondImage = new SpriteImageDTO();
        secondImage.setPath("second/path");
        secondImage.setWidth(200);
        secondImage.setHeight(200);
        secondImage.setSubImageCount(20);
        ExtendVideoDTO extendVideoDTO = new ExtendVideoDTO();
        extendVideoDTO.setSpriteImageList(Arrays.asList(firstImage, secondImage));
        // act
        builder.buildSpritePicByExtendVideoDto(video, extendVideoDTO);
        // assert
        SpritePicModule spritePic = video.getSpritePic();
        assertNotNull(spritePic);
        assertEquals("first/path", spritePic.getSpritePicUrl());
        assertEquals(10, spritePic.getTotalCount());
        ImagesSize cellSize = spritePic.getSpriteCellSize();
        assertNotNull(cellSize);
        assertEquals(100, cellSize.getWidth());
        assertEquals(100, cellSize.getHeight());
    }

    private void setPrivateField(String fieldName, Object value) throws Exception {
        Field field = HeadPicModuleBuilder.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(builder, value);
        field.setAccessible(false);
    }

    @Test
    public void testAssemblePicturesWhenBaseInfoIsNull() throws Throwable {
        // arrange
        setPrivateField("baseInfo", null);
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testAssemblePicturesWhenImageIsNull() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        when(baseInfo.getImage()).thenReturn(null);
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testAssemblePicturesWhenBothPathsAreEmpty() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        when(baseInfo.getImage()).thenReturn(imageDTO);
        when(imageDTO.getDefaultPicPath()).thenReturn("");
        when(imageDTO.getAllPicPaths()).thenReturn("");
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testAssemblePicturesWhenBothPathsAreNull() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        when(baseInfo.getImage()).thenReturn(imageDTO);
        when(imageDTO.getDefaultPicPath()).thenReturn(null);
        when(imageDTO.getAllPicPaths()).thenReturn(null);
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testAssemblePicturesWhenOnlyDefaultPicPathExists() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        when(baseInfo.getImage()).thenReturn(imageDTO);
        when(imageDTO.getDefaultPicPath()).thenReturn("default.jpg");
        when(imageDTO.getAllPicPaths()).thenReturn("");
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ContentType.PIC.getType(), result.get(0).getType());
        assertNotNull(result.get(0).getContent());
        assertEquals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale(), result.get(0).getScale());
    }

    @Test
    public void testAssemblePicturesWhenOnlyAllPicPathsExists() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        when(baseInfo.getImage()).thenReturn(imageDTO);
        when(imageDTO.getDefaultPicPath()).thenReturn("");
        when(imageDTO.getAllPicPaths()).thenReturn("pic1.jpg|pic2.jpg");
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        result.forEach(pic -> {
            assertEquals(ContentType.PIC.getType(), pic.getType());
            assertNotNull(pic.getContent());
            assertEquals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale(), pic.getScale());
        });
    }

    @Test
    public void testAssemblePicturesWhenBothPathsExistWithOverlap() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        when(baseInfo.getImage()).thenReturn(imageDTO);
        when(imageDTO.getDefaultPicPath()).thenReturn("default.jpg");
        when(imageDTO.getAllPicPaths()).thenReturn("default.jpg|pic2.jpg");
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        result.forEach(pic -> {
            assertEquals(ContentType.PIC.getType(), pic.getType());
            assertNotNull(pic.getContent());
            assertEquals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale(), pic.getScale());
        });
    }

    @Test
    public void testAssemblePicturesWithMtPlatform() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        setPrivateField("isMt", true);
        when(baseInfo.getImage()).thenReturn(imageDTO);
        when(imageDTO.getDefaultPicPath()).thenReturn("default.jpg");
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ContentType.PIC.getType(), result.get(0).getType());
        assertNotNull(result.get(0).getContent());
        assertEquals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale(), result.get(0).getScale());
    }

    @Test
    public void testAssemblePicturesWithMultipleImages() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        when(baseInfo.getImage()).thenReturn(imageDTO);
        when(imageDTO.getDefaultPicPath()).thenReturn("default.jpg");
        when(imageDTO.getAllPicPaths()).thenReturn("pic1.jpg|pic2.jpg|pic3.jpg|pic4.jpg");
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertEquals(5, result.size());
        result.forEach(pic -> {
            assertEquals(ContentType.PIC.getType(), pic.getType());
            assertNotNull(pic.getContent());
            assertEquals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale(), pic.getScale());
        });
    }

    @Test
    public void testAssemblePicturesWithCustomCategoryId() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        setPrivateField("secondCategoryId", 123);
        when(baseInfo.getImage()).thenReturn(imageDTO);
        when(imageDTO.getDefaultPicPath()).thenReturn("default.jpg");
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ContentType.PIC.getType(), result.get(0).getType());
        assertNotNull(result.get(0).getContent());
        assertEquals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale(), result.get(0).getScale());
    }

    @Test
    public void testAssemblePicturesWithEmptyPathsContainingSeparators() throws Throwable {
        // arrange
        setPrivateField("baseInfo", baseInfo);
        when(baseInfo.getImage()).thenReturn(imageDTO);
        when(imageDTO.getDefaultPicPath()).thenReturn("|||");
        when(imageDTO.getAllPicPaths()).thenReturn("|||");
        // act
        List<HeadPicModuleVO> result = builder.assemblePictures();
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ContentType.PIC.getType(), result.get(0).getType());
        assertEquals(ImageScaleEnum.SIXTEEN_TO_NINE.getScale(), result.get(0).getScale());
    }
}
