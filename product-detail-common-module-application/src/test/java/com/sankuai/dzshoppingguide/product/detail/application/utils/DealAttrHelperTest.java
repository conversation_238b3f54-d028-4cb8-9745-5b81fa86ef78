package com.sankuai.dzshoppingguide.product.detail.application.utils;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @create 2025/2/27 19:42
 */

public class DealAttrHelperTest {


    @Test
    public void testJoinListByDelimiterList() {
        List<String> values = new ArrayList<>();
        values.add("1");
        values.add("2");
        String delimiter = "、";
        String result = DealAttrHelper.joinListByDelimiter(values, delimiter);
        Assert.assertTrue("1、2".equals(result));
    }

    @Test
    public void testJoinListByDelimiterSet() {
        Set<String> values = new HashSet<>();
        values.add("1");
        values.add("2");
        String delimiter = "、";
        String result = DealAttrHelper.joinListByDelimiter(values, delimiter);
        Assert.assertTrue("1、2".equals(result));
    }
}
