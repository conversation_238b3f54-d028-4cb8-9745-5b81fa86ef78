package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;

import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UnavailableReminderInfoUtilsValidAvailableDateRangeTest {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 测试输入列表为空的情况
     */
    @Test
    void testValidAvailableDateRange_EmptyList_ReturnsFalse() throws Throwable {
        // arrange
        List<DateRangeDTO> emptyList = Collections.emptyList();
        // act
        boolean result = UnavailableReminderInfoUtils.validAvailableDateRange(emptyList);
        // assert
        assertFalse(result);
    }

    /**
     * 测试输入列表为null的情况
     */
    @Test
    void testValidAvailableDateRange_NullList_ReturnsFalse() throws Throwable {
        // arrange & act
        boolean result = UnavailableReminderInfoUtils.validAvailableDateRange(null);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当前日期在有效范围内的情况
     */
    @Test
    void testValidAvailableDateRange_CurrentDateInRange_ReturnsTrue() throws Throwable {
        // arrange
        LocalDate today = LocalDate.now();
        DateRangeDTO range = new DateRangeDTO();
        range.setFrom(today.minusDays(1).format(DATE_FORMATTER));
        range.setTo(today.plusDays(1).format(DATE_FORMATTER));
        List<DateRangeDTO> ranges = Collections.singletonList(range);
        // act
        boolean result = UnavailableReminderInfoUtils.validAvailableDateRange(ranges);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当前日期不在有效范围内的情况
     */
    @Test
    void testValidAvailableDateRange_CurrentDateNotInRange_ReturnsFalse() throws Throwable {
        // arrange
        LocalDate today = LocalDate.now();
        DateRangeDTO range = new DateRangeDTO();
        range.setFrom(today.plusDays(1).format(DATE_FORMATTER));
        range.setTo(today.plusDays(2).format(DATE_FORMATTER));
        List<DateRangeDTO> ranges = Collections.singletonList(range);
        // act
        boolean result = UnavailableReminderInfoUtils.validAvailableDateRange(ranges);
        // assert
        assertFalse(result);
    }

    /**
     * 测试日期格式不正确的情况
     */
    @Test
    void testValidAvailableDateRange_InvalidDateFormat_ReturnsFalse() throws Throwable {
        // arrange
        DateRangeDTO range = new DateRangeDTO();
        range.setFrom("invalid-date");
        range.setTo(LocalDate.now().format(DATE_FORMATTER));
        List<DateRangeDTO> ranges = Collections.singletonList(range);
        // act
        boolean result = UnavailableReminderInfoUtils.validAvailableDateRange(ranges);
        // assert
        assertFalse(result);
    }

    /**
     * 测试多个日期范围的情况(部分有效部分无效)
     */
    @Test
    void testValidAvailableDateRange_MultipleRangesWithOneValid_ReturnsTrue() throws Throwable {
        // arrange
        LocalDate today = LocalDate.now();
        DateRangeDTO validRange = new DateRangeDTO();
        validRange.setFrom(today.minusDays(1).format(DATE_FORMATTER));
        validRange.setTo(today.plusDays(1).format(DATE_FORMATTER));
        DateRangeDTO invalidRange = new DateRangeDTO();
        invalidRange.setFrom(today.plusDays(2).format(DATE_FORMATTER));
        invalidRange.setTo(today.plusDays(3).format(DATE_FORMATTER));
        List<DateRangeDTO> ranges = Arrays.asList(invalidRange, validRange);
        // act
        boolean result = UnavailableReminderInfoUtils.validAvailableDateRange(ranges);
        // assert
        assertTrue(result);
    }

    /**
     * 测试所有日期范围都无效的情况
     */
    @Test
    void testValidAvailableDateRange_AllRangesInvalid_ReturnsFalse() throws Throwable {
        // arrange
        LocalDate today = LocalDate.now();
        DateRangeDTO range1 = new DateRangeDTO();
        range1.setFrom(today.plusDays(1).format(DATE_FORMATTER));
        range1.setTo(today.plusDays(2).format(DATE_FORMATTER));
        DateRangeDTO range2 = new DateRangeDTO();
        range2.setFrom(today.plusDays(3).format(DATE_FORMATTER));
        range2.setTo(today.plusDays(4).format(DATE_FORMATTER));
        List<DateRangeDTO> ranges = Arrays.asList(range1, range2);
        // act
        boolean result = UnavailableReminderInfoUtils.validAvailableDateRange(ranges);
        // assert
        assertFalse(result);
    }
}
