package com.sankuai.dzshoppingguide.product.detail.application.builder.inventory;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import com.dianping.vc.sdk.codec.JsonCodec;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

/**
 * Unit tests for InventoryDetailBuilder.getAddress method
 */
@ExtendWith(MockitoExtension.class)
public class InventoryDetailBuilderTest {

    @InjectMocks
    private InventoryDetailBuilder inventoryDetailBuilder;

    /**
     * Test when input list is null
     */
    @Test
    public void testGetAddress_WhenInputNull() throws Throwable {
        // arrange
        List<String> values = null;
        // act
        String result = inventoryDetailBuilder.getAddress(values);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when input list is empty
     */
    @Test
    public void testGetAddress_WhenInputEmpty() throws Throwable {
        // arrange
        List<String> values = new ArrayList<>();
        // act
        String result = inventoryDetailBuilder.getAddress(values);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when input list contains only blank strings
     */
    @Test
    public void testGetAddress_WhenOnlyBlankStrings() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("", " ", "  ");
        // act
        String result = inventoryDetailBuilder.getAddress(values);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when input list contains mix of blank and non-blank strings
     */
    @Test
    public void testGetAddress_WhenMixedStrings() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("Beijing", "", "Haidian", " ", "Street");
        // act
        String result = inventoryDetailBuilder.getAddress(values);
        // assert
        assertEquals("Beijing-Haidian-Street", result);
    }

    /**
     * Test when input list contains all valid strings
     */
    @Test
    public void testGetAddress_WhenAllValidStrings() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("Beijing", "Haidian", "Street");
        // act
        String result = inventoryDetailBuilder.getAddress(values);
        // assert
        assertEquals("Beijing-Haidian-Street", result);
    }

    /**
     * Test exception handling when stream operation fails
     */
    @Test
    public void testGetAddress_WhenExceptionOccurs() throws Throwable {
        // arrange
        List<String> values = new ArrayList<String>() {

            @Override
            public java.util.stream.Stream<String> stream() {
                throw new RuntimeException("Test exception");
            }
        };
        // Add an element to avoid empty list short-circuit
        values.add("test");
        // act
        String result = inventoryDetailBuilder.getAddress(values);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when input list contains single value
     */
    @Test
    public void testGetAddress_WhenSingleValue() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("Beijing");
        // act
        String result = inventoryDetailBuilder.getAddress(values);
        // assert
        assertEquals("Beijing", result);
    }

    /**
     * Test when input list contains strings with special characters
     */
    @Test
    public void testGetAddress_WhenSpecialCharacters() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("Beijing#", "$Haidian", "Street@123");
        // act
        String result = inventoryDetailBuilder.getAddress(values);
        // assert
        assertEquals("Beijing#-$Haidian-Street@123", result);
    }
}
