package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.LayerConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.experimental.var;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertNull;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;

@ExtendWith(MockitoExtension.class)
public class StandardGuaranteeModuleBuilderTest {

    private static final String HOLIDAY_AVAILABLE = "HOLIDAY_AVAILABLE";

    @InjectMocks
    private StandardGuaranteeModuleBuilder builder = new StandardGuaranteeModuleBuilder() {

        @Override
        public ProductDetailGuaranteeVO doBuild() {
            return null;
        }
    };

    private static class TestableStandardGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {

        private final Map<String, LayerConfig> configMap;

        public TestableStandardGuaranteeModuleBuilder(Map<String, LayerConfig> configMap) {
            this.configMap = configMap;
        }

        @Override
        public ProductDetailGuaranteeVO doBuild() {
            return null;
        }

        @Override
        protected List<LayerConfig> buildLayerConfigs(List<String> layerConfigKeys) {
            if (layerConfigKeys == null || layerConfigKeys.isEmpty()) {
                return Collections.emptyList();
            }
            return layerConfigKeys.stream().map(configMap::get).collect(java.util.stream.Collectors.toList());
        }
    }

    /**
     * Test case for null input
     */
    @Test
    public void testBuildLayerConfigs_NullInput() throws Throwable {
        // arrange
        Map<String, LayerConfig> configMap = new HashMap<>();
        TestableStandardGuaranteeModuleBuilder builder = new TestableStandardGuaranteeModuleBuilder(configMap);
        // act
        List<LayerConfig> result = builder.buildLayerConfigs(null);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty list input
     */
    @Test
    public void testBuildLayerConfigs_EmptyInput() throws Throwable {
        // arrange
        Map<String, LayerConfig> configMap = new HashMap<>();
        TestableStandardGuaranteeModuleBuilder builder = new TestableStandardGuaranteeModuleBuilder(configMap);
        // act
        List<LayerConfig> result = builder.buildLayerConfigs(Collections.emptyList());
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for valid input with existing config keys
     */
    @Test
    public void testBuildLayerConfigs_ValidInput() throws Throwable {
        // arrange
        LayerConfig config1 = new LayerConfig();
        LayerConfig config2 = new LayerConfig();
        Map<String, LayerConfig> configMap = new HashMap<>();
        configMap.put("key1", config1);
        configMap.put("key2", config2);
        TestableStandardGuaranteeModuleBuilder builder = new TestableStandardGuaranteeModuleBuilder(configMap);
        // act
        List<LayerConfig> result = builder.buildLayerConfigs(Arrays.asList("key1", "key2"));
        // assert
        assertEquals(2, result.size());
        assertEquals(config1, result.get(0));
        assertEquals(config2, result.get(1));
    }

    /**
     * Test case for input with non-existing config keys
     */
    @Test
    public void testBuildLayerConfigs_NonExistingKeys() throws Throwable {
        // arrange
        Map<String, LayerConfig> configMap = new HashMap<>();
        TestableStandardGuaranteeModuleBuilder builder = new TestableStandardGuaranteeModuleBuilder(configMap);
        // act
        List<LayerConfig> result = builder.buildLayerConfigs(Arrays.asList("nonexistent1", "nonexistent2"));
        // assert
        assertEquals(2, result.size());
        assertTrue(result.stream().allMatch(config -> config == null));
    }

    /**
     * Test case for mixed input with both existing and non-existing keys
     */
    @Test
    public void testBuildLayerConfigs_MixedKeys() throws Throwable {
        // arrange
        LayerConfig config1 = new LayerConfig();
        Map<String, LayerConfig> configMap = new HashMap<>();
        configMap.put("key1", config1);
        TestableStandardGuaranteeModuleBuilder builder = new TestableStandardGuaranteeModuleBuilder(configMap);
        // act
        List<LayerConfig> result = builder.buildLayerConfigs(Arrays.asList("key1", "nonexistent"));
        // assert
        assertEquals(2, result.size());
        assertEquals(config1, result.get(0));
        assertEquals(null, result.get(1));
    }

    @Test
    public void testGetApplicableTime_OralTeethCategory() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 506, 0);
        ProductAttr productAttr = mock(ProductAttr.class);
        // act
        String result = builder.getApplicableTime(productAttr, productCategory);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetApplicableTime_NotWorkDayAvailable_ContainsWorkDays() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 100, 0);
        ProductAttr productAttr = mock(ProductAttr.class);
        // Create AttrDTO for holiday available
        AttrDTO holidayAttr = new AttrDTO();
        holidayAttr.setName(HOLIDAY_AVAILABLE);
        // Contains work day (1001)
        holidayAttr.setValue(Arrays.asList("1001", "1006", "1007"));
        // Mock the behavior
        when(productAttr.getSkuAttrList()).thenReturn(Collections.singletonList(holidayAttr));
        // act
        String result = builder.getApplicableTime(productAttr, productCategory);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetApplicableTime_NotWorkDayAvailable_MissingWeekend() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 100, 0);
        ProductAttr productAttr = mock(ProductAttr.class);
        // Create AttrDTO for holiday available
        AttrDTO holidayAttr = new AttrDTO();
        holidayAttr.setName(HOLIDAY_AVAILABLE);
        // Missing 1007
        holidayAttr.setValue(Collections.singletonList("1006"));
        // Mock the behavior
        when(productAttr.getSkuAttrList()).thenReturn(Collections.singletonList(holidayAttr));
        // act
        String result = builder.getApplicableTime(productAttr, productCategory);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetApplicableTime_EmptyHolidayAttr() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 100, 0);
        ProductAttr productAttr = mock(ProductAttr.class);
        when(productAttr.getSkuAttrList()).thenReturn(Collections.emptyList());
        // act
        String result = builder.getApplicableTime(productAttr, productCategory);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetApplicableTime_NullHolidayAttrList() throws Throwable {
        // arrange
        ProductCategory productCategory = new ProductCategory(0, 100, 0);
        ProductAttr productAttr = mock(ProductAttr.class);
        when(productAttr.getSkuAttrList()).thenReturn(null);
        // act
        String result = builder.getApplicableTime(productAttr, productCategory);
        // assert
        assertNull(result);
    }
}
