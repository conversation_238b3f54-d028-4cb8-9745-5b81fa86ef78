package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import org.junit.jupiter.api.DisplayName;
import java.util.Optional;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;

@ExtendWith(MockitoExtension.class)
@DisplayName("ReminderInfoUtils buildUnavailableReminderInfo Test")
class ReminderInfoUtilsTest {

    /**
     * 测试正常情况下的buildLayer方法
     * 验证所有参数都能正确设置到返回对象中
     */
    @Test
    void testBuildLayerWithValidParameters() {
        // arrange
        int expectedType = 1;
        String expectedJumpUrl = "https://example.com";
        String expectedModuleKey = "test-module";
        // act
        GuaranteeInstructionsBarLayerVO result = ReminderInfoUtils.buildLayer(expectedType, expectedJumpUrl, expectedModuleKey);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedType, result.getType(), "Type should match input");
        assertEquals(expectedJumpUrl, result.getJumpUrl(), "JumpUrl should match input");
        assertEquals(expectedModuleKey, result.getModulekey(), "ModuleKey should match input");
    }

    /**
     * 测试jumpUrl为null的情况
     * 验证方法能正确处理null跳转URL
     */
    @Test
    void testBuildLayerWithNullJumpUrl() {
        // arrange
        int expectedType = 2;
        String expectedModuleKey = "test-module";
        // act
        GuaranteeInstructionsBarLayerVO result = ReminderInfoUtils.buildLayer(expectedType, null, expectedModuleKey);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedType, result.getType(), "Type should match input");
        assertNull(result.getJumpUrl(), "JumpUrl should be null");
        assertEquals(expectedModuleKey, result.getModulekey(), "ModuleKey should match input");
    }

    /**
     * 测试moduleKey为null的情况
     * 验证方法能正确处理null模块键
     */
    @Test
    void testBuildLayerWithNullModuleKey() {
        // arrange
        int expectedType = 3;
        String expectedJumpUrl = "https://example.com";
        // act
        GuaranteeInstructionsBarLayerVO result = ReminderInfoUtils.buildLayer(expectedType, expectedJumpUrl, null);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedType, result.getType(), "Type should match input");
        assertEquals(expectedJumpUrl, result.getJumpUrl(), "JumpUrl should match input");
        assertNull(result.getModulekey(), "ModuleKey should be null");
    }

    /**
     * 测试边界值情况 - 最小int值
     * 验证方法能处理最小整数值作为类型参数
     */
    @Test
    void testBuildLayerWithMinIntType() {
        // arrange
        int expectedType = Integer.MIN_VALUE;
        String expectedJumpUrl = "https://example.com";
        String expectedModuleKey = "test-module";
        // act
        GuaranteeInstructionsBarLayerVO result = ReminderInfoUtils.buildLayer(expectedType, expectedJumpUrl, expectedModuleKey);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedType, result.getType(), "Type should match input");
        assertEquals(expectedJumpUrl, result.getJumpUrl(), "JumpUrl should match input");
        assertEquals(expectedModuleKey, result.getModulekey(), "ModuleKey should match input");
    }

    /**
     * 测试边界值情况 - 最大int值
     * 验证方法能处理最大整数值作为类型参数
     */
    @Test
    void testBuildLayerWithMaxIntType() {
        // arrange
        int expectedType = Integer.MAX_VALUE;
        String expectedJumpUrl = "https://example.com";
        String expectedModuleKey = "test-module";
        // act
        GuaranteeInstructionsBarLayerVO result = ReminderInfoUtils.buildLayer(expectedType, expectedJumpUrl, expectedModuleKey);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedType, result.getType(), "Type should match input");
        assertEquals(expectedJumpUrl, result.getJumpUrl(), "JumpUrl should match input");
        assertEquals(expectedModuleKey, result.getModulekey(), "ModuleKey should match input");
    }

    /**
     * 测试空字符串参数情况
     * 验证方法能正确处理空字符串参数
     */
    @Test
    void testBuildLayerWithEmptyStringParameters() {
        // arrange
        int expectedType = 4;
        String expectedJumpUrl = "";
        String expectedModuleKey = "";
        // act
        GuaranteeInstructionsBarLayerVO result = ReminderInfoUtils.buildLayer(expectedType, expectedJumpUrl, expectedModuleKey);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedType, result.getType(), "Type should match input");
        assertEquals(expectedJumpUrl, result.getJumpUrl(), "JumpUrl should be empty string");
        assertEquals(expectedModuleKey, result.getModulekey(), "ModuleKey should be empty string");
    }

    /**
     * 测试所有参数为null的情况
     * 验证方法能正确处理所有参数为null的情况
     */
    @Test
    void testBuildLayerWithAllNullParameters() {
        // arrange
        int expectedType = 0;
        // act
        GuaranteeInstructionsBarLayerVO result = ReminderInfoUtils.buildLayer(expectedType, null, null);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedType, result.getType(), "Type should match input");
        assertNull(result.getJumpUrl(), "JumpUrl should be null");
        assertNull(result.getModulekey(), "ModuleKey should be null");
    }

    @Test
    @DisplayName("Should return null when input is null")
    public void testBuildUnavailableReminderInfo_NullInput() {
        // arrange
        String text = null;
        // act
        GuaranteeInstructionsContentVO result = ReminderInfoUtils.buildUnavailableReminderInfo(text);
        // assert
        assertNull(result, "Result should be null for null input");
    }

    @Test
    @DisplayName("Should return null when input is empty")
    public void testBuildUnavailableReminderInfo_EmptyInput() {
        // arrange
        String text = "";
        // act
        GuaranteeInstructionsContentVO result = ReminderInfoUtils.buildUnavailableReminderInfo(text);
        // assert
        assertNull(result, "Result should be null for empty input");
    }

    @Test
    @DisplayName("Should return valid VO when input is valid text")
    public void testBuildUnavailableReminderInfo_ValidInput() {
        // arrange
        String text = "Test reminder";
        // act
        GuaranteeInstructionsContentVO result = ReminderInfoUtils.buildUnavailableReminderInfo(text);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(text, result.getText(), "Text should match input");
        assertEquals("12", result.getFontSize(), "Font size should be '12'");
        assertEquals("#555555", result.getFontColor(), "Font color should be '#555555'");
        assertEquals(1, result.getStyle(), "Style should be 1");
    }

    @Test
    @DisplayName("Should handle special characters correctly")
    public void testBuildUnavailableReminderInfo_SpecialCharacters() {
        // arrange
        String text = "Special!@#$%^&*()_+";
        // act
        GuaranteeInstructionsContentVO result = ReminderInfoUtils.buildUnavailableReminderInfo(text);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(text, result.getText(), "Text should preserve special characters");
        assertEquals("12", result.getFontSize(), "Font size should be '12'");
        assertEquals("#555555", result.getFontColor(), "Font color should be '#555555'");
        assertEquals(1, result.getStyle(), "Style should be 1");
    }

    @Test
    @DisplayName("Should handle whitespace-only input correctly")
    public void testBuildUnavailableReminderInfo_WhitespaceOnly() {
        // arrange
        String text = "   ";
        // act
        GuaranteeInstructionsContentVO result = ReminderInfoUtils.buildUnavailableReminderInfo(text);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(text, result.getText(), "Text should preserve whitespace");
        assertEquals("12", result.getFontSize(), "Font size should be '12'");
        assertEquals("#555555", result.getFontColor(), "Font color should be '#555555'");
        assertEquals(1, result.getStyle(), "Style should be 1");
    }

    @Test
    void testBuildReminderInfo_NullText_ReturnsEmptyOptional() throws Throwable {
        // arrange
        String text = null;
        // act
        Optional<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildReminderInfo(text);
        // assert
        assertFalse(result.isPresent(), "当输入为null时应返回空的Optional");
    }

    @Test
    void testBuildReminderInfo_EmptyText_ReturnsEmptyOptional() throws Throwable {
        // arrange
        String text = "";
        // act
        Optional<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildReminderInfo(text);
        // assert
        assertFalse(result.isPresent(), "当输入为空字符串时应返回空的Optional");
    }

    @Test
    void testBuildReminderInfo_BlankText_ReturnsConfiguredOptional() throws Throwable {
        // arrange
        String text = "   ";
        // act
        Optional<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildReminderInfo(text);
        // assert
        assertAll(() -> assertTrue(result.isPresent(), "当输入为空白字符串时应返回非空的Optional"), () -> assertEquals(text, result.get().getText(), "空白字符串内容应正确设置"), () -> assertEquals("12", result.get().getFontSize(), "字体大小应设置为默认值12"), () -> assertEquals("#555555", result.get().getFontColor(), "字体颜色应设置为默认值#555555"));
    }

    @Test
    void testBuildReminderInfo_ValidText_ReturnsConfiguredOptional() throws Throwable {
        // arrange
        String text = "Valid reminder text";
        // act
        Optional<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildReminderInfo(text);
        // assert
        assertAll(() -> assertTrue(result.isPresent(), "当输入有效文本时应返回非空的Optional"), () -> assertEquals(text, result.get().getText(), "文本内容应正确设置"), () -> assertEquals("12", result.get().getFontSize(), "字体大小应设置为默认值12"), () -> assertEquals("#555555", result.get().getFontColor(), "字体颜色应设置为默认值#555555"));
    }

    @Test
    void testBuildReminderInfo_LongText_ReturnsConfiguredOptional() throws Throwable {
        // arrange
        String text = new String(new char[10000]).replace('\0', 'a');
        // act
        Optional<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildReminderInfo(text);
        // assert
        assertAll(() -> assertTrue(result.isPresent(), "当输入超长文本时应返回非空的Optional"), () -> assertEquals(text, result.get().getText(), "超长文本内容应正确设置"), () -> assertEquals("12", result.get().getFontSize(), "字体大小应设置为默认值12"), () -> assertEquals("#555555", result.get().getFontColor(), "字体颜色应设置为默认值#555555"));
    }

    @Test
    void testBuildReminderInfo_SpecialCharacters_ReturnsConfiguredOptional() throws Throwable {
        // arrange
        String text = "特殊字符!@#$%^&*()_+";
        // act
        Optional<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildReminderInfo(text);
        // assert
        assertAll(() -> assertTrue(result.isPresent(), "当输入包含特殊字符时应返回非空的Optional"), () -> assertEquals(text, result.get().getText(), "特殊字符文本内容应正确设置"), () -> assertEquals("12", result.get().getFontSize(), "字体大小应设置为默认值12"), () -> assertEquals("#555555", result.get().getFontColor(), "字体颜色应设置为默认值#555555"));
    }

    @Test
    void testBuildModel0WithValidTitleAndMultipleReminderInfos() {
        // arrange
        String title = "Guarantee Title";
        List<String> reminderInfo = Arrays.asList("Info 1", "Info 2");
        // act
        ProductDetailGuaranteeVO result = ReminderInfoUtils.buildModel0(title, reminderInfo);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(title, result.getTitle(), "Title should match input");
        assertEquals(2, result.getContents().size(), "Should have 2 content items");
        GuaranteeInstructionsContentVO firstContent = result.getContents().get(0);
        assertEquals("Info 1", firstContent.getText(), "First content text should match");
        assertEquals("12", firstContent.getFontSize(), "Font size should be 12");
        assertEquals("#555555", firstContent.getFontColor(), "Font color should be #555555 for new detail");
    }

    @Test
    void testBuildModel0WithEmptyReminderInfoList() {
        // arrange
        String title = "Empty Info Test";
        List<String> reminderInfo = Collections.emptyList();
        // act
        ProductDetailGuaranteeVO result = ReminderInfoUtils.buildModel0(title, reminderInfo);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(title, result.getTitle(), "Title should match input");
        assertTrue(result.getContents().isEmpty(), "Contents should be empty");
    }

    @Test
    void testBuildModel0WithNullReminderInfo() {
        // arrange
        String title = "Null Info Test";
        List<String> reminderInfo = null;
        // act
        ProductDetailGuaranteeVO result = ReminderInfoUtils.buildModel0(title, reminderInfo);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(title, result.getTitle(), "Title should match input");
        assertTrue(result.getContents().isEmpty(), "Contents should be empty");
    }

    @Test
    void testBuildModel0WithNullTitle() {
        // arrange
        String title = null;
        List<String> reminderInfo = Arrays.asList("Info 1");
        // act
        ProductDetailGuaranteeVO result = ReminderInfoUtils.buildModel0(title, reminderInfo);
        // assert
        assertNotNull(result, "Result should not be null");
        assertNull(result.getTitle(), "Title should be null");
        assertEquals(1, result.getContents().size(), "Should have 1 content item");
    }

    @Test
    void testBuildModel0WithEmptyTitle() {
        // arrange
        String title = "";
        List<String> reminderInfo = Arrays.asList("Single Info");
        // act
        ProductDetailGuaranteeVO result = ReminderInfoUtils.buildModel0(title, reminderInfo);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals("", result.getTitle(), "Title should be empty string");
        assertEquals(1, result.getContents().size(), "Should have 1 content item");
    }

    @Test
    void testBuildModel0WithSingleReminderInfo() {
        // arrange
        String title = "Single Info Test";
        List<String> reminderInfo = Collections.singletonList("Only One Info");
        // act
        ProductDetailGuaranteeVO result = ReminderInfoUtils.buildModel0(title, reminderInfo);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(title, result.getTitle(), "Title should match input");
        assertEquals(1, result.getContents().size(), "Should have 1 content item");
        assertEquals("Only One Info", result.getContents().get(0).getText(), "Content text should match");
    }

    @Test
    void testBuildContentWhenReminderInfoIsNull() {
        // arrange
        List<String> reminderInfo = null;
        boolean isOldDetail = true;
        // act
        List<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildContent(reminderInfo, isOldDetail);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testBuildContentWhenReminderInfoIsEmpty() {
        // arrange
        List<String> reminderInfo = Collections.emptyList();
        boolean isOldDetail = false;
        // act
        List<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildContent(reminderInfo, isOldDetail);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testBuildContentWhenIsOldDetailTrue() {
        // arrange
        List<String> reminderInfo = Arrays.asList("info1", "info2");
        boolean isOldDetail = true;
        // act
        List<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildContent(reminderInfo, isOldDetail);
        // assert
        assertEquals(2, result.size());
        result.forEach(content -> {
            assertEquals("12", content.getFontSize());
            assertEquals("#222222", content.getFontColor());
        });
        assertEquals("info1", result.get(0).getText());
        assertEquals("info2", result.get(1).getText());
    }

    @Test
    void testBuildContentWhenIsOldDetailFalse() {
        // arrange
        List<String> reminderInfo = Arrays.asList("info1", "info2", "info3");
        boolean isOldDetail = false;
        // act
        List<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildContent(reminderInfo, isOldDetail);
        // assert
        assertEquals(3, result.size());
        result.forEach(content -> {
            assertEquals("12", content.getFontSize());
            assertEquals("#555555", content.getFontColor());
        });
        assertEquals("info1", result.get(0).getText());
        assertEquals("info2", result.get(1).getText());
        assertEquals("info3", result.get(2).getText());
    }

    @Test
    void testBuildContentWithEmptyString() {
        // arrange
        List<String> reminderInfo = Arrays.asList("", "info2");
        boolean isOldDetail = true;
        // act
        List<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildContent(reminderInfo, isOldDetail);
        // assert
        assertEquals(2, result.size());
        assertEquals("", result.get(0).getText());
        assertEquals("info2", result.get(1).getText());
        assertEquals("#222222", result.get(0).getFontColor());
        assertEquals("#222222", result.get(1).getFontColor());
    }

    @Test
    void testBuildContentWithNullElement() {
        // arrange
        List<String> reminderInfo = Arrays.asList(null, "info2");
        boolean isOldDetail = false;
        // act
        List<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildContent(reminderInfo, isOldDetail);
        // assert
        assertEquals(2, result.size());
        assertNull(result.get(0).getText());
        assertEquals("info2", result.get(1).getText());
        assertEquals("#555555", result.get(0).getFontColor());
        assertEquals("#555555", result.get(1).getFontColor());
    }

    @Test
    void testBuildContentFontSizeAlways12() {
        // arrange
        List<String> reminderInfo = Arrays.asList("info1", "info2");
        boolean isOldDetail = true;
        // act
        List<GuaranteeInstructionsContentVO> result = ReminderInfoUtils.buildContent(reminderInfo, isOldDetail);
        // assert
        result.forEach(content -> assertEquals("12", content.getFontSize()));
    }

    @Test
    void testBuildModelWithValidParametersAndOldDetail() {
        // Arrange
        String title = "Test Title";
        List<String> reminderInfo = Arrays.asList("Info 1", "Info 2");
        GuaranteeInstructionsBarLayerVO mockLayer = mock(GuaranteeInstructionsBarLayerVO.class);
        boolean isOldDetail = true;
        // Act
        ProductDetailReminderVO result = ReminderInfoUtils.buildModel(title, reminderInfo, mockLayer, isOldDetail);
        // Assert
        assertNotNull(result);
        assertEquals(title, result.getTitle());
        assertEquals(mockLayer, result.getLayer());
        List<GuaranteeInstructionsContentVO> contents = result.getContents();
        assertEquals(2, contents.size());
        assertEquals("Info 1", contents.get(0).getText());
        assertEquals("12", contents.get(0).getFontSize());
        assertEquals("#222222", contents.get(0).getFontColor());
    }

    @Test
    void testBuildModelWithValidParametersAndNewDetail() {
        // Arrange
        String title = "Test Title";
        List<String> reminderInfo = Arrays.asList("Info 1", "Info 2");
        GuaranteeInstructionsBarLayerVO mockLayer = mock(GuaranteeInstructionsBarLayerVO.class);
        boolean isOldDetail = false;
        // Act
        ProductDetailReminderVO result = ReminderInfoUtils.buildModel(title, reminderInfo, mockLayer, isOldDetail);
        // Assert
        assertNotNull(result);
        assertEquals("#555555", result.getContents().get(0).getFontColor());
    }

    @Test
    void testBuildModelWithEmptyReminderInfo() {
        // Arrange
        String title = "Test Title";
        List<String> reminderInfo = Collections.emptyList();
        GuaranteeInstructionsBarLayerVO mockLayer = mock(GuaranteeInstructionsBarLayerVO.class);
        boolean isOldDetail = true;
        // Act
        ProductDetailReminderVO result = ReminderInfoUtils.buildModel(title, reminderInfo, mockLayer, isOldDetail);
        // Assert
        assertNotNull(result);
        assertTrue(result.getContents().isEmpty());
    }

    @Test
    void testBuildModelWithNullReminderInfo() {
        // Arrange
        String title = "Test Title";
        GuaranteeInstructionsBarLayerVO mockLayer = mock(GuaranteeInstructionsBarLayerVO.class);
        boolean isOldDetail = true;
        // Act
        ProductDetailReminderVO result = ReminderInfoUtils.buildModel(title, null, mockLayer, isOldDetail);
        // Assert
        assertNotNull(result);
        assertTrue(result.getContents().isEmpty());
    }

    @Test
    void testBuildModelWithNullLayer() {
        // Arrange
        String title = "Test Title";
        List<String> reminderInfo = Arrays.asList("Info 1", "Info 2");
        boolean isOldDetail = true;
        // Act
        ProductDetailReminderVO result = ReminderInfoUtils.buildModel(title, reminderInfo, null, isOldDetail);
        // Assert
        assertNotNull(result);
        assertNull(result.getLayer());
    }

    @Test
    void testBuildModelWithNullTitle() {
        // Arrange
        List<String> reminderInfo = Arrays.asList("Info 1", "Info 2");
        GuaranteeInstructionsBarLayerVO mockLayer = mock(GuaranteeInstructionsBarLayerVO.class);
        boolean isOldDetail = true;
        // Act
        ProductDetailReminderVO result = ReminderInfoUtils.buildModel(null, reminderInfo, mockLayer, isOldDetail);
        // Assert
        assertNotNull(result);
        assertNull(result.getTitle());
    }

    @Test
    void testBuildModelWithSingleReminderInfo() {
        // Arrange
        String title = "Test Title";
        List<String> reminderInfo = Collections.singletonList("Single Info");
        GuaranteeInstructionsBarLayerVO mockLayer = mock(GuaranteeInstructionsBarLayerVO.class);
        boolean isOldDetail = true;
        // Act
        ProductDetailReminderVO result = ReminderInfoUtils.buildModel(title, reminderInfo, mockLayer, isOldDetail);
        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContents().size());
        assertEquals("Single Info", result.getContents().get(0).getText());
    }
}
