package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertEquals;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import java.util.Arrays;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import java.util.ArrayList;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;

@ExtendWith(MockitoExtension.class)
class ProductStandardServiceProjectTest {

    private StandardAttributeDTO standardAttributeDTO;

    private List<StandardAttributeItemDTO> attrItems;

    @Mock
    private StandardServiceProjectDTO standardServiceProject;

    /**
     * 测试正常场景 - 有复杂属性值且 processor 处理成功
     */
    @Test
    public void testProcessComplexValueNormalCase() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        String attrKey = "testKey";
        List<StandardAttributeDTO> complexValues = Collections.singletonList(new StandardAttributeDTO());
        Function<List<StandardAttributeDTO>, String> processor = list -> "processed";
        try (MockedStatic<DealAttrHelper> mocked = mockStatic(DealAttrHelper.class)) {
            mocked.when(() -> DealAttrHelper.getComplexAttrValue(attr, attrKey)).thenReturn(complexValues);
            // act
            String result = ProductStandardServiceProject.processComplexValue(attr, attrKey, processor, "default");
            // assert
            assertEquals("processed", result);
            mocked.verify(() -> DealAttrHelper.getComplexAttrValue(attr, attrKey));
        }
    }

    /**
     * 测试边界场景 - 复杂属性值为空列表
     */
    @Test
    public void testProcessComplexValueEmptyList() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        String attrKey = "testKey";
        List<StandardAttributeDTO> complexValues = Collections.emptyList();
        Function<List<StandardAttributeDTO>, String> processor = list -> "processed";
        try (MockedStatic<DealAttrHelper> mocked = mockStatic(DealAttrHelper.class)) {
            mocked.when(() -> DealAttrHelper.getComplexAttrValue(attr, attrKey)).thenReturn(complexValues);
            // act
            String result = ProductStandardServiceProject.processComplexValue(attr, attrKey, processor, "default");
            // assert
            assertEquals("default", result);
            mocked.verify(() -> DealAttrHelper.getComplexAttrValue(attr, attrKey));
        }
    }

    /**
     * 测试异常场景 - attr 为 null
     */
    @Test
    public void testProcessComplexValueNullAttr() throws Throwable {
        // arrange
        String attrKey = "testKey";
        Function<List<StandardAttributeDTO>, String> processor = list -> "processed";
        // act & assert
        String result = ProductStandardServiceProject.processComplexValue(null, attrKey, processor, "default");
        assertEquals("default", result);
    }

    /**
     * 测试异常场景 - attrKey 为空
     */
    @Test
    public void testProcessComplexValueEmptyAttrKey() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        Function<List<StandardAttributeDTO>, String> processor = list -> "processed";
        // act & assert
        String result = ProductStandardServiceProject.processComplexValue(attr, "", processor, "default");
        assertEquals("default", result);
    }

    /**
     * 测试异常场景 - processor 为 null
     */
    @Test
    public void testProcessComplexValueNullProcessor() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        String attrKey = "testKey";
        List<StandardAttributeDTO> complexValues = Collections.singletonList(new StandardAttributeDTO());
        try (MockedStatic<DealAttrHelper> mocked = mockStatic(DealAttrHelper.class)) {
            mocked.when(() -> DealAttrHelper.getComplexAttrValue(attr, attrKey)).thenReturn(complexValues);
            // act & assert
            assertThrows(NullPointerException.class, () -> ProductStandardServiceProject.processComplexValue(attr, attrKey, null, "default"));
        }
    }

    /**
     * 测试异常场景 - getComplexAttrValue 返回 null
     */
    @Test
    public void testProcessComplexValueNullComplexValues() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        String attrKey = "testKey";
        Function<List<StandardAttributeDTO>, String> processor = list -> "processed";
        try (MockedStatic<DealAttrHelper> mocked = mockStatic(DealAttrHelper.class)) {
            mocked.when(() -> DealAttrHelper.getComplexAttrValue(attr, attrKey)).thenReturn(null);
            // act
            String result = ProductStandardServiceProject.processComplexValue(attr, attrKey, processor, "default");
            // assert
            assertEquals("default", result);
            mocked.verify(() -> DealAttrHelper.getComplexAttrValue(attr, attrKey));
        }
    }

    @Test
    public void testGetValueWithPrefix_NormalCase() throws Throwable {
        // arrange
        StandardAttributeDTO attr = new StandardAttributeDTO();
        String attrKey = "testKey";
        String prefix = "prefix-";
        // Create test data that will make getValue return "testValue"
        StandardAttributeItemDTO item = new StandardAttributeItemDTO();
        item.setAttrName(attrKey);
        StandardAttributeValueDTO valueDTO = new StandardAttributeValueDTO();
        valueDTO.setType(0);
        valueDTO.setSimpleValues(Collections.singletonList("testValue"));
        item.setAttrValues(Collections.singletonList(valueDTO));
        attr.setAttrs(Collections.singletonList(item));
        // act
        String result = ProductStandardServiceProject.getValueWithPrefix(attr, attrKey, prefix);
        // assert
        assertEquals("prefix-testValue", result);
    }

    @Test
    public void testGetValueWithPrefix_EmptyValue() throws Throwable {
        // arrange
        StandardAttributeDTO attr = new StandardAttributeDTO();
        String attrKey = "testKey";
        String prefix = "prefix-";
        // Create test data that will make getValue return empty string
        StandardAttributeItemDTO item = new StandardAttributeItemDTO();
        item.setAttrName(attrKey);
        StandardAttributeValueDTO valueDTO = new StandardAttributeValueDTO();
        valueDTO.setType(0);
        valueDTO.setSimpleValues(Collections.singletonList(""));
        item.setAttrValues(Collections.singletonList(valueDTO));
        attr.setAttrs(Collections.singletonList(item));
        // act
        String result = ProductStandardServiceProject.getValueWithPrefix(attr, attrKey, prefix);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetValueWithPrefix_NullAttribute() throws Throwable {
        // arrange
        StandardAttributeDTO attr = null;
        String attrKey = "testKey";
        String prefix = "prefix-";
        // act
        String result = ProductStandardServiceProject.getValueWithPrefix(attr, attrKey, prefix);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetValueWithPrefix_NullPrefix() throws Throwable {
        // arrange
        StandardAttributeDTO attr = new StandardAttributeDTO();
        String attrKey = "testKey";
        String prefix = null;
        // Create test data that will make getValue return "testValue"
        StandardAttributeItemDTO item = new StandardAttributeItemDTO();
        item.setAttrName(attrKey);
        StandardAttributeValueDTO valueDTO = new StandardAttributeValueDTO();
        valueDTO.setType(0);
        valueDTO.setSimpleValues(Collections.singletonList("testValue"));
        item.setAttrValues(Collections.singletonList(valueDTO));
        attr.setAttrs(Collections.singletonList(item));
        // act
        String result = ProductStandardServiceProject.getValueWithPrefix(attr, attrKey, prefix);
        // assert
        assertEquals("nulltestValue", result);
    }

    @Test
    public void testGetValueWithPrefix_NullAttrKey() throws Throwable {
        // arrange
        StandardAttributeDTO attr = new StandardAttributeDTO();
        String attrKey = null;
        String prefix = "prefix-";
        // act
        String result = ProductStandardServiceProject.getValueWithPrefix(attr, attrKey, prefix);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testJoinListByDelimiterWhenListIsNull() throws Throwable {
        // arrange
        List<String> list = null;
        String delimiter = ",";
        // act
        String result = ProductStandardServiceProject.joinListByDelimiter(list, delimiter);
        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    @Test
    public void testJoinListByDelimiterWhenListIsEmpty() throws Throwable {
        // arrange
        List<String> list = Collections.emptyList();
        String delimiter = ",";
        // act
        String result = ProductStandardServiceProject.joinListByDelimiter(list, delimiter);
        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    @Test
    public void testJoinListByDelimiterWhenListHasSingleElement() throws Throwable {
        // arrange
        List<String> list = Collections.singletonList("item1");
        String delimiter = ",";
        // act
        String result = ProductStandardServiceProject.joinListByDelimiter(list, delimiter);
        // assert
        assertEquals("item1", result);
    }

    @Test
    public void testJoinListByDelimiterWhenListHasMultipleElements() throws Throwable {
        // arrange
        List<String> list = Arrays.asList("item1", "item2", "item3");
        String delimiter = ",";
        // act
        String result = ProductStandardServiceProject.joinListByDelimiter(list, delimiter);
        // assert
        assertEquals("item1,item2,item3", result);
    }

    @Test
    public void testJoinListByDelimiterWhenDelimiterIsEmpty() throws Throwable {
        // arrange
        List<String> list = Arrays.asList("item1", "item2");
        String delimiter = "";
        // act
        String result = ProductStandardServiceProject.joinListByDelimiter(list, delimiter);
        // assert
        assertEquals("item1item2", result);
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsBothParamsNull() {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(null, null);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsServiceProjectNull() {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(1L, null);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsMustGroupsEmpty() {
        // arrange
        StandardServiceProjectDTO projectDTO = mock(StandardServiceProjectDTO.class);
        when(projectDTO.getMustGroups()).thenReturn(new ArrayList<>());
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(1L, projectDTO);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsGroupNull() {
        // arrange
        StandardServiceProjectDTO projectDTO = mock(StandardServiceProjectDTO.class);
        List<StandardServiceProjectGroupDTO> groups = new ArrayList<>();
        groups.add(null);
        when(projectDTO.getMustGroups()).thenReturn(groups);
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(1L, projectDTO);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsItemsEmpty() {
        // arrange
        StandardServiceProjectDTO projectDTO = mock(StandardServiceProjectDTO.class);
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        when(group.getServiceProjectItems()).thenReturn(new ArrayList<>());
        List<StandardServiceProjectGroupDTO> groups = new ArrayList<>();
        groups.add(group);
        when(projectDTO.getMustGroups()).thenReturn(groups);
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(1L, projectDTO);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsOptionalCountZeroWithMatch() {
        // arrange
        Long targetCpvId = 1L;
        StandardServiceProjectItemDTO matchedItem = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO matchedAttr = mock(StandardAttributeDTO.class);
        when(matchedItem.getStandardAttribute()).thenReturn(matchedAttr);
        when(matchedAttr.getCpvObjectId()).thenReturn(targetCpvId);
        StandardServiceProjectItemDTO unmatchedItem = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO unmatchedAttr = mock(StandardAttributeDTO.class);
        when(unmatchedItem.getStandardAttribute()).thenReturn(unmatchedAttr);
        when(unmatchedAttr.getCpvObjectId()).thenReturn(2L);
        List<StandardServiceProjectItemDTO> items = new ArrayList<>();
        items.add(matchedItem);
        items.add(unmatchedItem);
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        when(group.getOptionalCount()).thenReturn(0);
        when(group.getServiceProjectItems()).thenReturn(items);
        StandardServiceProjectDTO projectDTO = mock(StandardServiceProjectDTO.class);
        List<StandardServiceProjectGroupDTO> groups = new ArrayList<>();
        groups.add(group);
        when(projectDTO.getMustGroups()).thenReturn(groups);
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(targetCpvId, projectDTO);
        // assert
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getServiceProjectItems().size());
        assertEquals(targetCpvId, result.get(0).getServiceProjectItems().get(0).getStandardAttribute().getCpvObjectId());
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsOptionalCountZeroNoMatch() {
        // arrange
        Long targetCpvId = 1L;
        StandardServiceProjectItemDTO unmatchedItem = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO unmatchedAttr = mock(StandardAttributeDTO.class);
        when(unmatchedItem.getStandardAttribute()).thenReturn(unmatchedAttr);
        when(unmatchedAttr.getCpvObjectId()).thenReturn(2L);
        List<StandardServiceProjectItemDTO> items = new ArrayList<>();
        items.add(unmatchedItem);
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        when(group.getOptionalCount()).thenReturn(0);
        when(group.getServiceProjectItems()).thenReturn(items);
        StandardServiceProjectDTO projectDTO = mock(StandardServiceProjectDTO.class);
        List<StandardServiceProjectGroupDTO> groups = new ArrayList<>();
        groups.add(group);
        when(projectDTO.getMustGroups()).thenReturn(groups);
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(targetCpvId, projectDTO);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsOptionalCountPositiveAllMatch() {
        // arrange
        Long targetCpvId = 1L;
        StandardServiceProjectItemDTO item1 = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO attr1 = mock(StandardAttributeDTO.class);
        when(item1.getStandardAttribute()).thenReturn(attr1);
        when(attr1.getCpvObjectId()).thenReturn(targetCpvId);
        StandardServiceProjectItemDTO item2 = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO attr2 = mock(StandardAttributeDTO.class);
        when(item2.getStandardAttribute()).thenReturn(attr2);
        when(attr2.getCpvObjectId()).thenReturn(targetCpvId);
        List<StandardServiceProjectItemDTO> items = new ArrayList<>();
        items.add(item1);
        items.add(item2);
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        when(group.getOptionalCount()).thenReturn(1);
        when(group.getServiceProjectItems()).thenReturn(items);
        StandardServiceProjectDTO projectDTO = mock(StandardServiceProjectDTO.class);
        List<StandardServiceProjectGroupDTO> groups = new ArrayList<>();
        groups.add(group);
        when(projectDTO.getMustGroups()).thenReturn(groups);
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(targetCpvId, projectDTO);
        // assert
        assertEquals(1, result.size());
        assertSame(group, result.get(0));
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsOptionalCountPositivePartialMatch() {
        // arrange
        Long targetCpvId = 1L;
        StandardServiceProjectItemDTO matchedItem = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO matchedAttr = mock(StandardAttributeDTO.class);
        when(matchedItem.getStandardAttribute()).thenReturn(matchedAttr);
        when(matchedAttr.getCpvObjectId()).thenReturn(targetCpvId);
        StandardServiceProjectItemDTO unmatchedItem = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO unmatchedAttr = mock(StandardAttributeDTO.class);
        when(unmatchedItem.getStandardAttribute()).thenReturn(unmatchedAttr);
        when(unmatchedAttr.getCpvObjectId()).thenReturn(2L);
        List<StandardServiceProjectItemDTO> items = new ArrayList<>();
        items.add(matchedItem);
        items.add(unmatchedItem);
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        when(group.getOptionalCount()).thenReturn(1);
        when(group.getServiceProjectItems()).thenReturn(items);
        StandardServiceProjectDTO projectDTO = mock(StandardServiceProjectDTO.class);
        List<StandardServiceProjectGroupDTO> groups = new ArrayList<>();
        groups.add(group);
        when(projectDTO.getMustGroups()).thenReturn(groups);
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(targetCpvId, projectDTO);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsItemAttributeNull() {
        // arrange
        Long targetCpvId = 1L;
        StandardServiceProjectItemDTO item = mock(StandardServiceProjectItemDTO.class);
        when(item.getStandardAttribute()).thenReturn(null);
        List<StandardServiceProjectItemDTO> items = new ArrayList<>();
        items.add(item);
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        when(group.getOptionalCount()).thenReturn(0);
        when(group.getServiceProjectItems()).thenReturn(items);
        StandardServiceProjectDTO projectDTO = mock(StandardServiceProjectDTO.class);
        List<StandardServiceProjectGroupDTO> groups = new ArrayList<>();
        groups.add(group);
        when(projectDTO.getMustGroups()).thenReturn(groups);
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(targetCpvId, projectDTO);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetSameCPVObjectIdFromMustGroupsItemCpvObjectIdNull() {
        // arrange
        Long targetCpvId = 1L;
        StandardServiceProjectItemDTO item = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        when(item.getStandardAttribute()).thenReturn(attr);
        when(attr.getCpvObjectId()).thenReturn(null);
        List<StandardServiceProjectItemDTO> items = new ArrayList<>();
        items.add(item);
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        when(group.getOptionalCount()).thenReturn(0);
        when(group.getServiceProjectItems()).thenReturn(items);
        StandardServiceProjectDTO projectDTO = mock(StandardServiceProjectDTO.class);
        List<StandardServiceProjectGroupDTO> groups = new ArrayList<>();
        groups.add(group);
        when(projectDTO.getMustGroups()).thenReturn(groups);
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromMustGroups(targetCpvId, projectDTO);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetValueWithSuffix_NormalCase() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        StandardAttributeItemDTO item = mock(StandardAttributeItemDTO.class);
        StandardAttributeValueDTO valueDTO = mock(StandardAttributeValueDTO.class);
        when(attr.getAttrs()).thenReturn(Collections.singletonList(item));
        when(item.getAttrName()).thenReturn("testKey");
        when(item.getAttrValues()).thenReturn(Collections.singletonList(valueDTO));
        when(valueDTO.getType()).thenReturn(0);
        when(valueDTO.getSimpleValues()).thenReturn(Collections.singletonList("testValue"));
        // act
        String result = ProductStandardServiceProject.getValueWithSuffix(attr, "testKey", "_suffix");
        // assert
        assertEquals("testValue_suffix", result);
    }

    @Test
    public void testGetValueWithSuffix_EmptyValue() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        StandardAttributeItemDTO item = mock(StandardAttributeItemDTO.class);
        StandardAttributeValueDTO valueDTO = mock(StandardAttributeValueDTO.class);
        when(attr.getAttrs()).thenReturn(Collections.singletonList(item));
        when(item.getAttrName()).thenReturn("testKey");
        when(item.getAttrValues()).thenReturn(Collections.singletonList(valueDTO));
        when(valueDTO.getType()).thenReturn(0);
        when(valueDTO.getSimpleValues()).thenReturn(Collections.singletonList(""));
        // act
        String result = ProductStandardServiceProject.getValueWithSuffix(attr, "testKey", "_suffix");
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetValueWithSuffix_NullAttr() throws Throwable {
        // act
        String result = ProductStandardServiceProject.getValueWithSuffix(null, "testKey", "_suffix");
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetValueWithSuffix_NullAttrKey() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        // act
        String result = ProductStandardServiceProject.getValueWithSuffix(attr, null, "_suffix");
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetValueWithSuffix_NullSuffix() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        StandardAttributeItemDTO item = mock(StandardAttributeItemDTO.class);
        StandardAttributeValueDTO valueDTO = mock(StandardAttributeValueDTO.class);
        when(attr.getAttrs()).thenReturn(Collections.singletonList(item));
        when(item.getAttrName()).thenReturn("testKey");
        when(item.getAttrValues()).thenReturn(Collections.singletonList(valueDTO));
        when(valueDTO.getType()).thenReturn(0);
        when(valueDTO.getSimpleValues()).thenReturn(Collections.singletonList("testValue"));
        // act
        String result = ProductStandardServiceProject.getValueWithSuffix(attr, "testKey", null);
        // assert
        assertEquals("testValuenull", result);
    }

    @Test
    public void testGetValueWithSuffix_AttributeNotFound() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        StandardAttributeItemDTO item = mock(StandardAttributeItemDTO.class);
        when(attr.getAttrs()).thenReturn(Collections.singletonList(item));
        when(item.getAttrName()).thenReturn("otherKey");
        // act
        String result = ProductStandardServiceProject.getValueWithSuffix(attr, "testKey", "_suffix");
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetValueWithSuffix_MultipleValues() throws Throwable {
        // arrange
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        StandardAttributeItemDTO item = mock(StandardAttributeItemDTO.class);
        StandardAttributeValueDTO valueDTO = mock(StandardAttributeValueDTO.class);
        when(attr.getAttrs()).thenReturn(Collections.singletonList(item));
        when(item.getAttrName()).thenReturn("testKey");
        when(item.getAttrValues()).thenReturn(Collections.singletonList(valueDTO));
        when(valueDTO.getType()).thenReturn(0);
        // 修改为单个值
        when(valueDTO.getSimpleValues()).thenReturn(Collections.singletonList("value1"));
        // act
        String result = ProductStandardServiceProject.getValueWithSuffix(attr, "testKey", "_suffix");
        // assert
        // 修改期望值以匹配实际行为
        assertEquals("value1_suffix", result);
    }

    @BeforeEach
    public void setUp() {
        standardAttributeDTO = new StandardAttributeDTO();
        attrItems = Lists.newArrayList();
        standardAttributeDTO.setAttrs(attrItems);
    }

    private StandardAttributeItemDTO createAttributeItem(String attrName, String value) {
        StandardAttributeItemDTO item = new StandardAttributeItemDTO();
        item.setAttrName(attrName);
        List<StandardAttributeValueDTO> attrValues = new ArrayList<>();
        if (value != null) {
            StandardAttributeValueDTO valueDTO = new StandardAttributeValueDTO();
            valueDTO.setType(0);
            valueDTO.setSimpleValues(Collections.singletonList(value));
            attrValues.add(valueDTO);
        }
        item.setAttrValues(attrValues);
        return item;
    }

    @Test
    public void testGetTimeRangeBothTimesExist() throws Throwable {
        // arrange
        attrItems.add(createAttributeItem("startTime", "09:00"));
        attrItems.add(createAttributeItem("endTime", "18:00"));
        // act
        String result = ProductStandardServiceProject.getTimeRange(standardAttributeDTO, "startTime", "endTime");
        // assert
        assertEquals("09:00-18:00", result);
    }

    @Test
    public void testGetTimeRangeNullAttribute() throws Throwable {
        // act
        String result = ProductStandardServiceProject.getTimeRange(null, "startTime", "endTime");
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeRangeNullAttributeList() throws Throwable {
        // arrange
        standardAttributeDTO.setAttrs(null);
        // act
        String result = ProductStandardServiceProject.getTimeRange(standardAttributeDTO, "startTime", "endTime");
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeRangeNullStartTimeKey() throws Throwable {
        // arrange
        attrItems.add(createAttributeItem("endTime", "18:00"));
        // act
        String result = ProductStandardServiceProject.getTimeRange(standardAttributeDTO, null, "endTime");
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeRangeNullEndTimeKey() throws Throwable {
        // arrange
        attrItems.add(createAttributeItem("startTime", "09:00"));
        // act
        String result = ProductStandardServiceProject.getTimeRange(standardAttributeDTO, "startTime", null);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeRangeBothKeysNull() throws Throwable {
        // act
        String result = ProductStandardServiceProject.getTimeRange(standardAttributeDTO, null, null);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeRangeEmptyStartTimeKey() throws Throwable {
        // arrange
        attrItems.add(createAttributeItem("endTime", "18:00"));
        // act
        String result = ProductStandardServiceProject.getTimeRange(standardAttributeDTO, "", "endTime");
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeRangeEmptyEndTimeKey() throws Throwable {
        // arrange
        attrItems.add(createAttributeItem("startTime", "09:00"));
        // act
        String result = ProductStandardServiceProject.getTimeRange(standardAttributeDTO, "startTime", "");
        // assert
        assertEquals("", result);
    }

    @Test
    void testGetDifferentCPVObjectId_NullInput() throws Throwable {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getDifferentCPVObjectId(null);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void testGetDifferentCPVObjectId_EmptyGroups() throws Throwable {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        when(standardServiceProject.getMustGroups()).thenReturn(new ArrayList<>());
        when(standardServiceProject.getOptionalGroups()).thenReturn(new ArrayList<>());
        // act
        List<StandardServiceProjectGroupDTO> result = service.getDifferentCPVObjectId(standardServiceProject);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void testGetDifferentCPVObjectId_MustGroupsSameCpvObjectId() throws Throwable {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        doReturn(1L).when(attr).getCpvObjectId();
        StandardServiceProjectItemDTO item1 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectItemDTO item2 = mock(StandardServiceProjectItemDTO.class);
        doReturn(attr).when(item1).getStandardAttribute();
        doReturn(attr).when(item2).getStandardAttribute();
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        doReturn(Arrays.asList(item1, item2)).when(group).getServiceProjectItems();
        doReturn(1).when(group).getOptionalCount();
        when(standardServiceProject.getMustGroups()).thenReturn(Collections.singletonList(group));
        when(standardServiceProject.getOptionalGroups()).thenReturn(new ArrayList<>());
        // act
        List<StandardServiceProjectGroupDTO> result = service.getDifferentCPVObjectId(standardServiceProject);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void testGetDifferentCPVObjectId_MustGroupsDifferentCpvObjectIds() throws Throwable {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        StandardAttributeDTO attr1 = mock(StandardAttributeDTO.class);
        StandardAttributeDTO attr2 = mock(StandardAttributeDTO.class);
        doReturn(1L).when(attr1).getCpvObjectId();
        doReturn(2L).when(attr2).getCpvObjectId();
        StandardServiceProjectItemDTO item1 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectItemDTO item2 = mock(StandardServiceProjectItemDTO.class);
        doReturn(attr1).when(item1).getStandardAttribute();
        doReturn(attr2).when(item2).getStandardAttribute();
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        doReturn(Arrays.asList(item1, item2)).when(group).getServiceProjectItems();
        doReturn(1).when(group).getOptionalCount();
        when(standardServiceProject.getMustGroups()).thenReturn(Collections.singletonList(group));
        when(standardServiceProject.getOptionalGroups()).thenReturn(new ArrayList<>());
        // act
        List<StandardServiceProjectGroupDTO> result = service.getDifferentCPVObjectId(standardServiceProject);
        // assert
        assertFalse(CollectionUtils.isEmpty(result));
        assertEquals(1, result.size());
        assertSame(group, result.get(0));
    }

    @Test
    void testGetDifferentCPVObjectId_OptionalGroupsDifferentCpvObjectIds() throws Throwable {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        StandardAttributeDTO attr1 = mock(StandardAttributeDTO.class);
        StandardAttributeDTO attr2 = mock(StandardAttributeDTO.class);
        doReturn(1L).when(attr1).getCpvObjectId();
        doReturn(2L).when(attr2).getCpvObjectId();
        StandardServiceProjectItemDTO item1 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectItemDTO item2 = mock(StandardServiceProjectItemDTO.class);
        doReturn(attr1).when(item1).getStandardAttribute();
        doReturn(attr2).when(item2).getStandardAttribute();
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        doReturn(Arrays.asList(item1, item2)).when(group).getServiceProjectItems();
        doReturn(1).when(group).getOptionalCount();
        when(standardServiceProject.getMustGroups()).thenReturn(new ArrayList<>());
        when(standardServiceProject.getOptionalGroups()).thenReturn(Collections.singletonList(group));
        // act
        List<StandardServiceProjectGroupDTO> result = service.getDifferentCPVObjectId(standardServiceProject);
        // assert
        assertFalse(CollectionUtils.isEmpty(result));
        assertEquals(1, result.size());
        assertSame(group, result.get(0));
    }

    @Test
    void testGetDifferentCPVObjectId_LessThanTwoItems() throws Throwable {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        StandardServiceProjectItemDTO item = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        doReturn(Collections.singletonList(item)).when(group).getServiceProjectItems();
        when(standardServiceProject.getMustGroups()).thenReturn(Collections.singletonList(group));
        when(standardServiceProject.getOptionalGroups()).thenReturn(new ArrayList<>());
        // act
        List<StandardServiceProjectGroupDTO> result = service.getDifferentCPVObjectId(standardServiceProject);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void testGetDifferentCPVObjectId_ZeroOptionalCount() throws Throwable {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        StandardAttributeDTO attr1 = mock(StandardAttributeDTO.class);
        StandardAttributeDTO attr2 = mock(StandardAttributeDTO.class);
        StandardServiceProjectItemDTO item1 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectItemDTO item2 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        doReturn(Arrays.asList(item1, item2)).when(group).getServiceProjectItems();
        doReturn(0).when(group).getOptionalCount();
        when(standardServiceProject.getMustGroups()).thenReturn(Collections.singletonList(group));
        when(standardServiceProject.getOptionalGroups()).thenReturn(new ArrayList<>());
        // act
        List<StandardServiceProjectGroupDTO> result = service.getDifferentCPVObjectId(standardServiceProject);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_NullInputs() throws Throwable {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(null);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(null, null);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_NullServiceProject() throws Throwable {
        // arrange
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, null);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_NullOptionalGroups() throws Throwable {
        // arrange
        when(standardServiceProject.getOptionalGroups()).thenReturn(null);
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
        verify(standardServiceProject).getOptionalGroups();
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_EmptyOptionalGroups() throws Throwable {
        // arrange
        when(standardServiceProject.getOptionalGroups()).thenReturn(Collections.emptyList());
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
        verify(standardServiceProject).getOptionalGroups();
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_NullServiceProjectItems() throws Throwable {
        // arrange
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        when(group.getServiceProjectItems()).thenReturn(null);
        when(standardServiceProject.getOptionalGroups()).thenReturn(Collections.singletonList(group));
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
        verify(standardServiceProject).getOptionalGroups();
        verify(group).getServiceProjectItems();
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_EmptyServiceProjectItems() throws Throwable {
        // arrange
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        when(group.getServiceProjectItems()).thenReturn(Collections.emptyList());
        when(standardServiceProject.getOptionalGroups()).thenReturn(Collections.singletonList(group));
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
        verify(standardServiceProject).getOptionalGroups();
        verify(group).getServiceProjectItems();
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_OptionalCountZeroWithMatch() throws Throwable {
        // arrange
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        StandardServiceProjectItemDTO item1 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectItemDTO item2 = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO attr1 = mock(StandardAttributeDTO.class);
        StandardAttributeDTO attr2 = mock(StandardAttributeDTO.class);
        when(group.getOptionalCount()).thenReturn(0);
        when(item1.getStandardAttribute()).thenReturn(attr1);
        when(item2.getStandardAttribute()).thenReturn(attr2);
        when(attr1.getCpvObjectId()).thenReturn(1L);
        when(attr2.getCpvObjectId()).thenReturn(2L);
        when(group.getServiceProjectItems()).thenReturn(Arrays.asList(item1, item2));
        when(standardServiceProject.getOptionalGroups()).thenReturn(Collections.singletonList(group));
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.size(), "Should have one group");
        StandardServiceProjectGroupDTO resultGroup = result.get(0);
        assertNotNull(resultGroup, "Result group should not be null");
        assertEquals(0, resultGroup.getOptionalCount(), "OptionalCount should be 0");
        assertEquals(1, resultGroup.getServiceProjectItems().size(), "Should have one matching item");
        verify(standardServiceProject).getOptionalGroups();
        verify(group, times(2)).getServiceProjectItems();
        verify(item1, atLeastOnce()).getStandardAttribute();
        verify(item2, atLeastOnce()).getStandardAttribute();
        verify(attr1, atLeastOnce()).getCpvObjectId();
        verify(attr2, atLeastOnce()).getCpvObjectId();
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_OptionalCountZeroNoMatch() throws Throwable {
        // arrange
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        StandardServiceProjectItemDTO item = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO attr = mock(StandardAttributeDTO.class);
        when(group.getOptionalCount()).thenReturn(0);
        when(item.getStandardAttribute()).thenReturn(attr);
        when(attr.getCpvObjectId()).thenReturn(2L);
        when(group.getServiceProjectItems()).thenReturn(Collections.singletonList(item));
        when(standardServiceProject.getOptionalGroups()).thenReturn(Collections.singletonList(group));
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
        verify(standardServiceProject).getOptionalGroups();
        verify(group, times(2)).getServiceProjectItems();
        verify(item, atLeastOnce()).getStandardAttribute();
        verify(attr, atLeastOnce()).getCpvObjectId();
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_OptionalCountPositiveAllMatch() throws Throwable {
        // arrange
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        StandardServiceProjectItemDTO item1 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectItemDTO item2 = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO attr1 = mock(StandardAttributeDTO.class);
        StandardAttributeDTO attr2 = mock(StandardAttributeDTO.class);
        when(group.getOptionalCount()).thenReturn(2);
        when(item1.getStandardAttribute()).thenReturn(attr1);
        when(item2.getStandardAttribute()).thenReturn(attr2);
        when(attr1.getCpvObjectId()).thenReturn(1L);
        when(attr2.getCpvObjectId()).thenReturn(1L);
        when(group.getServiceProjectItems()).thenReturn(Arrays.asList(item1, item2));
        when(standardServiceProject.getOptionalGroups()).thenReturn(Collections.singletonList(group));
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.size(), "Should have one group");
        assertSame(group, result.get(0), "Should return the original group");
        verify(standardServiceProject).getOptionalGroups();
        verify(group, times(2)).getServiceProjectItems();
        verify(item1, atLeastOnce()).getStandardAttribute();
        verify(item2, atLeastOnce()).getStandardAttribute();
        verify(attr1, atLeastOnce()).getCpvObjectId();
        verify(attr2, atLeastOnce()).getCpvObjectId();
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_OptionalCountPositivePartialMatch() throws Throwable {
        // arrange
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        StandardServiceProjectItemDTO item1 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectItemDTO item2 = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO attr1 = mock(StandardAttributeDTO.class);
        StandardAttributeDTO attr2 = mock(StandardAttributeDTO.class);
        when(group.getOptionalCount()).thenReturn(2);
        when(item1.getStandardAttribute()).thenReturn(attr1);
        when(item2.getStandardAttribute()).thenReturn(attr2);
        when(attr1.getCpvObjectId()).thenReturn(1L);
        when(attr2.getCpvObjectId()).thenReturn(2L);
        when(group.getServiceProjectItems()).thenReturn(Arrays.asList(item1, item2));
        when(standardServiceProject.getOptionalGroups()).thenReturn(Collections.singletonList(group));
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty for partial match");
        verify(standardServiceProject).getOptionalGroups();
        verify(group, times(2)).getServiceProjectItems();
        verify(item1, atLeastOnce()).getStandardAttribute();
        verify(item2, atLeastOnce()).getStandardAttribute();
        verify(attr1, atLeastOnce()).getCpvObjectId();
        verify(attr2, atLeastOnce()).getCpvObjectId();
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_NullStandardAttribute() throws Throwable {
        // arrange
        StandardServiceProjectGroupDTO group = mock(StandardServiceProjectGroupDTO.class);
        StandardServiceProjectItemDTO item = mock(StandardServiceProjectItemDTO.class);
        when(group.getOptionalCount()).thenReturn(0);
        when(item.getStandardAttribute()).thenReturn(null);
        when(group.getServiceProjectItems()).thenReturn(Collections.singletonList(item));
        when(standardServiceProject.getOptionalGroups()).thenReturn(Collections.singletonList(group));
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty when standardAttribute is null");
        verify(standardServiceProject).getOptionalGroups();
        verify(group, times(2)).getServiceProjectItems();
        verify(item, atLeastOnce()).getStandardAttribute();
    }

    @Test
    void testGetSameCPVObjectIdFromOptionGroups_ComplexScenario() throws Throwable {
        // arrange
        StandardServiceProjectGroupDTO group1 = mock(StandardServiceProjectGroupDTO.class);
        StandardServiceProjectGroupDTO group2 = mock(StandardServiceProjectGroupDTO.class);
        StandardServiceProjectItemDTO item1 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectItemDTO item2 = mock(StandardServiceProjectItemDTO.class);
        StandardServiceProjectItemDTO item3 = mock(StandardServiceProjectItemDTO.class);
        StandardAttributeDTO attr1 = mock(StandardAttributeDTO.class);
        StandardAttributeDTO attr2 = mock(StandardAttributeDTO.class);
        StandardAttributeDTO attr3 = mock(StandardAttributeDTO.class);
        when(group1.getOptionalCount()).thenReturn(0);
        when(item1.getStandardAttribute()).thenReturn(attr1);
        when(item2.getStandardAttribute()).thenReturn(attr2);
        when(attr1.getCpvObjectId()).thenReturn(1L);
        when(attr2.getCpvObjectId()).thenReturn(2L);
        when(group1.getServiceProjectItems()).thenReturn(Arrays.asList(item1, item2));
        when(group2.getOptionalCount()).thenReturn(1);
        when(item3.getStandardAttribute()).thenReturn(attr3);
        when(attr3.getCpvObjectId()).thenReturn(1L);
        when(group2.getServiceProjectItems()).thenReturn(Collections.singletonList(item3));
        when(standardServiceProject.getOptionalGroups()).thenReturn(Arrays.asList(group1, group2));
        ProductStandardServiceProject service = new ProductStandardServiceProject(standardServiceProject);
        // act
        List<StandardServiceProjectGroupDTO> result = service.getSameCPVObjectIdFromOptionGroups(1L, standardServiceProject);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(2, result.size(), "Should have two groups");
        StandardServiceProjectGroupDTO resultGroup1 = result.get(0);
        assertEquals(0, resultGroup1.getOptionalCount(), "First group should have optionalCount=0");
        assertEquals(1, resultGroup1.getServiceProjectItems().size(), "First group should have one matching item");
        assertSame(group2, result.get(1), "Second group should be the original group");
        verify(standardServiceProject).getOptionalGroups();
        verify(group1, times(2)).getServiceProjectItems();
        verify(group2, times(2)).getServiceProjectItems();
        verify(item1, atLeastOnce()).getStandardAttribute();
        verify(item2, atLeastOnce()).getStandardAttribute();
        verify(item3, atLeastOnce()).getStandardAttribute();
        verify(attr1, atLeastOnce()).getCpvObjectId();
        verify(attr2, atLeastOnce()).getCpvObjectId();
        verify(attr3, atLeastOnce()).getCpvObjectId();
    }
}
