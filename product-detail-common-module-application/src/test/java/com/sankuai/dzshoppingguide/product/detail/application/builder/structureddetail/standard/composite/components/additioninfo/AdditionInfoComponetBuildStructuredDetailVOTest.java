package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.components.additioninfo;

import static org.junit.Assert.*;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AdditionInfoComponetBuildStructuredDetailVOTest {

    private AdditionInfoComponet additionInfoComponet = new AdditionInfoComponet();

    /**
     * 测试title，content和subcontent都为null的情况
     */
    @Test
    public void testBuildStructuredDetailVONullAll() {
        // arrange
        String title = null;
        String content = null;
        String subcontent = null;
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = additionInfoComponet.buildStructuredDetailVO(title, content, subcontent, type);
        // assert
        assertNotNull(result);
        assertNull(result.getTitle());
        assertNull(result.getContent());
        assertNull(result.getSubContent());
        assertEquals(type, result.getType());
    }

    /**
     * 测试title为null，content和subcontent不为null的情况
     */
    @Test
    public void testBuildStructuredDetailVONullTitle() {
        // arrange
        String title = null;
        String content = "content";
        String subcontent = "subcontent";
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = additionInfoComponet.buildStructuredDetailVO(title, content, subcontent, type);
        // assert
        assertNotNull(result);
        assertNull(result.getTitle());
        assertEquals(content, result.getContent());
        assertEquals(subcontent, result.getSubContent());
        assertEquals(type, result.getType());
    }

    /**
     * 测试title不为null，content和subcontent为null的情况
     */
    @Test
    public void testBuildStructuredDetailVONullContent() {
        // arrange
        String title = "title";
        String content = null;
        String subcontent = null;
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = additionInfoComponet.buildStructuredDetailVO(title, content, subcontent, type);
        // assert
        assertNotNull(result);
        assertEquals(title, result.getTitle());
        assertNull(result.getContent());
        assertNull(result.getSubContent());
        assertEquals(type, result.getType());
    }

    /**
     * 测试title，content和subcontent都不为null的情况
     */
    @Test
    public void testBuildStructuredDetailVONotNullAll() {
        // arrange
        String title = "title";
        String content = "content";
        String subcontent = "subcontent";
        int type = 1;
        // act
        DealDetailStructuredDetailVO result = additionInfoComponet.buildStructuredDetailVO(title, content, subcontent, type);
        // assert
        assertNotNull(result);
        assertEquals(title, result.getTitle());
        assertEquals(content, result.getContent());
        assertEquals(subcontent, result.getSubContent());
        assertEquals(type, result.getType());
    }
}
