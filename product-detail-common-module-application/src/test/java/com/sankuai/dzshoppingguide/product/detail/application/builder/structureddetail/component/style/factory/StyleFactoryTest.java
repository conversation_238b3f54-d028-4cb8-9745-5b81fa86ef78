package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.factory;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.OverNightStyle;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.BathStyle;
import org.junit.Before;
import org.junit.Test;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.AdditionNotesStyle;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.AdditionalInfoStyle0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.FootMassageStyle;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.BuffetFootStyle;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.ServiceFacilityStyle;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.GameCurrencyStyle;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.AdditionalInfoStyle;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.ServiceFacilityStyle0;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.FreeMealStyle;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.component.style.ChessCardPackagesStyle;

/**
 * Test class for StyleFactory's createOverNightStyle method
 */
@RunWith(MockitoJUnitRunner.class)
public class StyleFactoryTest {

    @Mock
    private OverNightStyle overNightStyle;

    @InjectMocks
    private StyleFactory styleFactory;

    @Mock
    private BathStyle bathStyle;

    @Mock
    private AdditionNotesStyle additionNotesStyle;

    @Mock
    private AdditionalInfoStyle0 additionalInfoStyle0;

    @Mock
    private FootMassageStyle footMassageStyle;

    @Mock
    private BuffetFootStyle buffetFootStyle;

    @Mock
    private ServiceFacilityStyle serviceFacilityStyle;

    @Mock
    private GameCurrencyStyle gameCurrencyStyle;

    @Mock
    private AdditionalInfoStyle additionalInfoStyle;

    @Mock
    private ServiceFacilityStyle0 serviceFacilityStyle0;

    @Mock
    private FreeMealStyle freeMealStyle;

    @Mock
    private ChessCardPackagesStyle chessCardPackagesStyle;

    /**
     * Test that createOverNightStyle returns the injected OverNightStyle instance
     */
    @Test
    public void testCreateOverNightStyle_ReturnsInjectedInstance() throws Throwable {
        // arrange
        // The @Mock and @InjectMocks annotations handle the arrangement
        // act
        OverNightStyle result = styleFactory.createOverNightStyle();
        // assert
        assertNotNull("The returned OverNightStyle should not be null", result);
        assertEquals("The returned OverNightStyle should be the injected instance", overNightStyle, result);
    }

    /**
     * Test that createOverNightStyle correctly returns the same instance on multiple calls
     */
    @Test
    public void testCreateOverNightStyle_ReturnsSameInstanceOnMultipleCalls() throws Throwable {
        // arrange
        // The @Mock and @InjectMocks annotations handle the arrangement
        // act
        OverNightStyle result1 = styleFactory.createOverNightStyle();
        OverNightStyle result2 = styleFactory.createOverNightStyle();
        // assert
        assertNotNull("The first returned OverNightStyle should not be null", result1);
        assertNotNull("The second returned OverNightStyle should not be null", result2);
        assertEquals("Multiple calls should return the same instance", result1, result2);
    }

    public OverNightStyle createOverNightStyle() {
        return overNightStyle;
    }

    @Test
    public void testCreateBathStyle_ShouldReturnInjectedBathStyle() throws Throwable {
        // act
        BathStyle result = styleFactory.createBathStyle();
        // assert
        assertNotNull("返回的 BathStyle 不应为 null", result);
        assertSame("返回的 BathStyle 应该是注入的同一个实例", bathStyle, result);
    }

    @Test
    public void testCreateBathStyle_WhenBathStyleIsNull() throws Throwable {
        // arrange
        // 创建一个新实例，没有注入依赖
        styleFactory = new StyleFactory();
        // act
        BathStyle result = styleFactory.createBathStyle();
        // assert
        assertNull("当没有注入 bathStyle 时，应该返回 null", result);
    }

    @Test
    public void testCreateAdditionNotesStyle_ShouldReturnInjectedInstance() throws Throwable {
        // arrange
        // The mock is already set up by MockitoJUnitRunner
        // act
        AdditionNotesStyle result = styleFactory.createAdditionNotesStyle();
        // assert
        assertNotNull("The returned AdditionNotesStyle should not be null", result);
        assertSame("The returned AdditionNotesStyle should be the same as the injected instance", additionNotesStyle, result);
    }

    @Test
    public void testCreateAdditionNotesStyle_WithNullInjectedInstance_ShouldReturnNull() throws Throwable {
        // arrange
        StyleFactory styleFactoryWithNullDependency = new StyleFactory();
        // The additionNotesStyle field is null by default
        // act
        AdditionNotesStyle result = styleFactoryWithNullDependency.createAdditionNotesStyle();
        // assert
        assertNull("The returned AdditionNotesStyle should be null when the injected instance is null", result);
    }

    @Test
    public void testCreateAdditionalInfoStyle0_ShouldReturnAutowiredInstance() throws Throwable {
        // arrange
        // The mock is already set up via @Mock and @InjectMocks annotations
        // act
        AdditionalInfoStyle0 result = styleFactory.createAdditionalInfoStyle0();
        // assert
        assertNotNull("The returned AdditionalInfoStyle0 should not be null", result);
        assertEquals("The method should return the autowired instance", additionalInfoStyle0, result);
    }

    @Test
    public void testCreateFootMassageStyle() throws Throwable {
        // arrange
        // 无需准备任何数据
        // act
        FootMassageStyle result = styleFactory.createFootMassageStyle();
        // assert
        assertEquals(footMassageStyle, result);
    }

    @Test
    public void testCreateBuffetFootStyle_ReturnsInjectedInstance() throws Throwable {
        // arrange
        // The buffetFootStyle is already mocked and injected via @Mock and @InjectMocks
        // act
        BuffetFootStyle result = styleFactory.createBuffetFootStyle();
        // assert
        assertNotNull("The returned BuffetFootStyle should not be null", result);
        assertEquals("The returned BuffetFootStyle should be the injected instance", buffetFootStyle, result);
    }

    @Test
    public void testCreateBuffetFootStyle_WithSpecificImplementation() throws Throwable {
        // arrange
        BuffetFootStyle specificStyle = mock(BuffetFootStyle.class);
        StyleFactory localStyleFactory = new StyleFactory();
        // Use reflection to set the private field
        java.lang.reflect.Field field = StyleFactory.class.getDeclaredField("buffetFootStyle");
        field.setAccessible(true);
        field.set(localStyleFactory, specificStyle);
        // act
        BuffetFootStyle result = localStyleFactory.createBuffetFootStyle();
        // assert
        assertNotNull("The returned BuffetFootStyle should not be null", result);
        assertEquals("The returned BuffetFootStyle should be the specific instance", specificStyle, result);
    }

    @Test
    public void testCreateBuffetFootStyle_WithNullInjection() throws Throwable {
        // arrange
        StyleFactory localStyleFactory = new StyleFactory();
        // The buffetFootStyle field remains null
        // act
        BuffetFootStyle result = localStyleFactory.createBuffetFootStyle();
        // assert
        assertEquals("The returned BuffetFootStyle should be null when not injected", null, result);
    }

    @Test
    public void testCreateServiceFacilityStyle_ReturnsAutowiredInstance() throws Throwable {
        // arrange
        // The serviceFacilityStyle is already mocked and injected via @Mock and @InjectMocks
        // act
        ServiceFacilityStyle result = styleFactory.createServiceFacilityStyle();
        // assert
        assertNotNull("The returned ServiceFacilityStyle should not be null", result);
        assertEquals("The method should return the autowired serviceFacilityStyle instance", serviceFacilityStyle, result);
    }

    @Test
    public void testCreateServiceFacilityStyle_ReturnsSameInstanceOnMultipleCalls() throws Throwable {
        // arrange
        // The serviceFacilityStyle is already mocked and injected via @Mock and @InjectMocks
        // act
        ServiceFacilityStyle result1 = styleFactory.createServiceFacilityStyle();
        ServiceFacilityStyle result2 = styleFactory.createServiceFacilityStyle();
        // assert
        assertNotNull("The first returned ServiceFacilityStyle should not be null", result1);
        assertNotNull("The second returned ServiceFacilityStyle should not be null", result2);
        assertEquals("Multiple calls should return the same instance", result1, result2);
    }

    @Test
    public void testCreateGameCurrencyStyle_ReturnsInjectedInstance() throws Throwable {
        // arrange
        // The mock is already set up by the MockitoJUnitRunner
        // act
        GameCurrencyStyle result = styleFactory.createGameCurrencyStyle();
        // assert
        assertNotNull("The returned GameCurrencyStyle should not be null", result);
        assertEquals("The returned GameCurrencyStyle should be the injected instance", gameCurrencyStyle, result);
    }

    @Test
    public void testCreateGameCurrencyStyle_ManualSetup() throws Throwable {
        // arrange
        StyleFactory manualStyleFactory = new StyleFactory();
        GameCurrencyStyle mockStyle = mock(GameCurrencyStyle.class);
        // Use reflection to set the private field
        java.lang.reflect.Field field = StyleFactory.class.getDeclaredField("gameCurrencyStyle");
        field.setAccessible(true);
        field.set(manualStyleFactory, mockStyle);
        // act
        GameCurrencyStyle result = manualStyleFactory.createGameCurrencyStyle();
        // assert
        assertNotNull("The returned GameCurrencyStyle should not be null", result);
        assertEquals("The returned GameCurrencyStyle should be the manually set instance", mockStyle, result);
    }

    @Test
    public void testCreateAdditionalInfoStyle() throws Throwable {
        // Act
        AdditionalInfoStyle result = styleFactory.createAdditionalInfoStyle();
        // Assert
        assertEquals(additionalInfoStyle, result);
    }

    @Test
    public void testCreateServiceFacilityStyle0_ReturnsInjectedInstance() throws Throwable {
        // arrange
        // The mock is already set up by the MockitoJUnitRunner
        // act
        ServiceFacilityStyle0 result = styleFactory.createServiceFacilityStyle0();
        // assert
        assertNotNull("The returned ServiceFacilityStyle0 should not be null", result);
        assertEquals("The returned instance should be the same as the injected one", serviceFacilityStyle0, result);
    }

    @Test
    public void testCreateServiceFacilityStyle0_PreservesState() throws Throwable {
        // arrange
        boolean expectedNeedNewLine = true;
        when(serviceFacilityStyle0.needNewLine()).thenReturn(expectedNeedNewLine);
        // act
        ServiceFacilityStyle0 result = styleFactory.createServiceFacilityStyle0();
        // assert
        assertEquals("The state of the returned instance should be preserved", expectedNeedNewLine, result.needNewLine());
    }

    @Test
    public void testCreateFreeMealStyle() throws Throwable {
        // Act
        FreeMealStyle result = styleFactory.createFreeMealStyle();
        // Assert
        assertEquals(freeMealStyle, result);
    }

    @Test
    public void testCreateChessCardPackagesStyle() throws Throwable {
        // Act
        ChessCardPackagesStyle result = styleFactory.createChessCardPackagesStyle();
        // Assert
        assertEquals(chessCardPackagesStyle, result);
    }
}
