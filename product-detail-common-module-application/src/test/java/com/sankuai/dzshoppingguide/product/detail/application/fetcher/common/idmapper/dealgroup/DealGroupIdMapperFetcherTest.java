package com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.domain.idmapper.MapperCacheWrapper;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DealGroupIdMapperFetcherTest {

    @Mock
    private MapperCacheWrapper mapperCacheWrapper;

    @Mock
    private ProductDetailPageRequest request;

    @InjectMocks
    private DealGroupIdMapperFetcher dealGroupIdMapperFetcher;

    /**
     * Test when product type is not DEAL, should return null
     */
    @Test
    public void testDoFetchNonDealProductType() throws Throwable {
        // arrange
        when(request.getProductType()).thenReturn(ProductTypeEnum.RESERVE.getCode());
        // act
        CompletableFuture<DealGroupIdMapper> result = dealGroupIdMapperFetcher.doFetch();
        // assert
        assertNull(result.get());
    }

    /**
     * Test MT client type scenario with successful cache fetch
     */
    @Test
    public void testDoFetchMtClientTypeSuccess() throws Throwable {
        // arrange
        long productId = 12345L;
        long expectedDpDealId = 67890L;
        when(request.getProductType()).thenReturn(ProductTypeEnum.DEAL.getCode());
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getProductId()).thenReturn(productId);
        when(mapperCacheWrapper.fetchDpDealId(productId)).thenReturn(expectedDpDealId);
        // act
        CompletableFuture<DealGroupIdMapper> result = dealGroupIdMapperFetcher.doFetch();
        // assert
        DealGroupIdMapper mapper = result.get();
        assertEquals(productId, mapper.getMtDealGroupId());
        assertEquals(expectedDpDealId, mapper.getDpDealGroupId());
    }

    /**
     * Test DP client type scenario with successful cache fetch
     */
    @Test
    public void testDoFetchDpClientTypeSuccess() throws Throwable {
        // arrange
        long productId = 67890L;
        long expectedMtDealId = 12345L;
        when(request.getProductType()).thenReturn(ProductTypeEnum.DEAL.getCode());
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getProductId()).thenReturn(productId);
        when(mapperCacheWrapper.fetchMtDealId(productId)).thenReturn(expectedMtDealId);
        // act
        CompletableFuture<DealGroupIdMapper> result = dealGroupIdMapperFetcher.doFetch();
        // assert
        DealGroupIdMapper mapper = result.get();
        assertEquals(expectedMtDealId, mapper.getMtDealGroupId());
        assertEquals(productId, mapper.getDpDealGroupId());
    }

    /**
     * Test MT client type scenario when cache fetch returns 0
     */
    @Test
    public void testDoFetchMtClientTypeCacheReturnsZero() throws Throwable {
        // arrange
        long productId = 12345L;
        when(request.getProductType()).thenReturn(ProductTypeEnum.DEAL.getCode());
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getProductId()).thenReturn(productId);
        when(mapperCacheWrapper.fetchDpDealId(productId)).thenReturn(0L);
        // act
        CompletableFuture<DealGroupIdMapper> result = dealGroupIdMapperFetcher.doFetch();
        // assert
        DealGroupIdMapper mapper = result.get();
        assertEquals(productId, mapper.getMtDealGroupId());
        assertEquals(0L, mapper.getDpDealGroupId());
    }

    /**
     * Test DP client type scenario when cache fetch returns 0
     */
    @Test
    public void testDoFetchDpClientTypeCacheReturnsZero() throws Throwable {
        // arrange
        long productId = 67890L;
        when(request.getProductType()).thenReturn(ProductTypeEnum.DEAL.getCode());
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getProductId()).thenReturn(productId);
        when(mapperCacheWrapper.fetchMtDealId(productId)).thenReturn(0L);
        // act
        CompletableFuture<DealGroupIdMapper> result = dealGroupIdMapperFetcher.doFetch();
        // assert
        DealGroupIdMapper mapper = result.get();
        assertEquals(0L, mapper.getMtDealGroupId());
        assertEquals(productId, mapper.getDpDealGroupId());
    }

    /**
     * Test MT client type scenario with max long value
     */
    @Test
    public void testDoFetchMtClientTypeMaxLongValue() throws Throwable {
        // arrange
        long productId = Long.MAX_VALUE;
        long expectedDpDealId = Long.MAX_VALUE - 1;
        when(request.getProductType()).thenReturn(ProductTypeEnum.DEAL.getCode());
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getProductId()).thenReturn(productId);
        when(mapperCacheWrapper.fetchDpDealId(productId)).thenReturn(expectedDpDealId);
        // act
        CompletableFuture<DealGroupIdMapper> result = dealGroupIdMapperFetcher.doFetch();
        // assert
        DealGroupIdMapper mapper = result.get();
        assertEquals(productId, mapper.getMtDealGroupId());
        assertEquals(expectedDpDealId, mapper.getDpDealGroupId());
    }

    /**
     * Test DP client type scenario with min long value
     */
    @Test
    public void testDoFetchDpClientTypeMinLongValue() throws Throwable {
        // arrange
        long productId = Long.MIN_VALUE;
        long expectedMtDealId = Long.MIN_VALUE + 1;
        when(request.getProductType()).thenReturn(ProductTypeEnum.DEAL.getCode());
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getProductId()).thenReturn(productId);
        when(mapperCacheWrapper.fetchMtDealId(productId)).thenReturn(expectedMtDealId);
        // act
        CompletableFuture<DealGroupIdMapper> result = dealGroupIdMapperFetcher.doFetch();
        // assert
        DealGroupIdMapper mapper = result.get();
        assertEquals(expectedMtDealId, mapper.getMtDealGroupId());
        assertEquals(productId, mapper.getDpDealGroupId());
    }
}
