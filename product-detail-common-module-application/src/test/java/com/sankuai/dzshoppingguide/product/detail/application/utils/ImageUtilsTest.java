package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.piccentercloud.display.api.PictureUrlGenerator;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dianping.piccentercloud.display.api.PictureVisitParams;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.DisplayName;

@ExtendWith(MockitoExtension.class)
class ImageUtilsTest {

    private static final String TEST_KEY = "test-image-key";

    private static final int VALID_MODE = 1;

    private static final int VALID_WIDTH = 100;

    private static final int VALID_HEIGHT = 100;

    @Mock
    private PictureUrlGenerator pictureUrlGenerator;

    @Mock
    private PictureVisitParams pictureVisitParams;

    /**
     * 测试正常情况下的JPG URL生成
     */
    @Test
    public void testGetJpgUrlNormalCase() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.DIANPING;
        String quality = "80";
        // act
        String result = ImageUtils.getJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试key为空的情况
     */
    @Test
    public void testGetJpgUrlEmptyKey() throws Throwable {
        // arrange
        String key = "";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.DIANPING;
        String quality = "80";
        // act
        String result = ImageUtils.getJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试quality为空的情况
     */
    @Test
    public void testGetJpgUrlEmptyQuality() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.DIANPING;
        String quality = "";
        // act
        String result = ImageUtils.getJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试不同的WaterMark值
     */
    @Test
    public void testGetJpgUrlDifferentWaterMarks() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 100;
        int mode = 1;
        String quality = "80";
        // act & assert for each WaterMark
        for (WaterMark waterMark : WaterMark.values()) {
            String result = ImageUtils.getJpgUrl(key, width, height, mode, waterMark, quality);
            assertNotNull(result);
        }
    }

    /**
     * 测试PictureUrlGenerator异常情况
     */
    @Test
    public void testGetJpgUrlGeneratorException() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.DIANPING;
        String quality = "80";
        PictureUrlGenerator mockGenerator = mock(PictureUrlGenerator.class);
        // act
        String result = ImageUtils.getJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试quality参数包含特殊字符的情况
     */
    @Test
    public void testGetJpgUrlSpecialQuality() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.DIANPING;
        String quality = "80%";
        // act
        String result = ImageUtils.getJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testGetGifUrlWithEmptyKey() throws Throwable {
        // arrange
        String key = "";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        String quality = "90";
        // act
        String result = ImageUtils.getGifUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetGifUrlWithNullKey() throws Throwable {
        // arrange
        String key = null;
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        String quality = "90";
        // act
        String result = ImageUtils.getGifUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetGifUrlWithValidParameters() throws Throwable {
        // arrange
        String key = "test-key";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        String quality = "90";
        // act
        String result = ImageUtils.getGifUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.endsWith(".gif"));
    }

    @Test
    public void testGetGifUrlWithNullQuality() throws Throwable {
        // arrange
        String key = "test-key";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        String quality = null;
        // act
        String result = ImageUtils.getGifUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.endsWith(".gif"));
    }

    @Test
    public void testGetGifUrlWithDifferentWatermarks() throws Throwable {
        // arrange
        String key = "test-key";
        int width = 100;
        int height = 100;
        int mode = 1;
        String quality = "90";
        // act & assert
        for (WaterMark waterMark : WaterMark.values()) {
            String result = ImageUtils.getGifUrl(key, width, height, mode, waterMark, quality);
            assertNotNull(result);
            assertTrue(result.endsWith(".gif"));
        }
    }

    @Test
    public void testGetGifUrlWithZeroDimensions() throws Throwable {
        // arrange
        String key = "test-key";
        int width = 0;
        int height = 0;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        String quality = "90";
        // act
        String result = ImageUtils.getGifUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.endsWith(".gif"));
    }

    @Test
    public void testGetGifUrlWithNegativeDimensions() throws Throwable {
        // arrange
        String key = "test-key";
        int width = -1;
        int height = -1;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        String quality = "90";
        // act
        String result = ImageUtils.getGifUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.endsWith(".gif"));
    }

@Test
public void testGetImageUrlWithValidParameters() throws Throwable {
// arrange
String key = "test_key";
int width = 100;
int height = 200;
int mode = 1;
WaterMark waterMark = WaterMark.DIANPING;
// act
String result = ImageUtils.getImageUrl(key, width, height, mode, waterMark);
// assert
assertNotNull(result);
assertTrue(result.contains("qcloud.dpfile.com"));
assertTrue(result.contains(".jpg"));
}

@Test
public void testGetImageUrlWithEmptyKey() throws Throwable {
// arrange
String key = "";
int width = 100;
int height = 200;
int mode = 1;
WaterMark waterMark = WaterMark.DIANPING;
// act
String result = ImageUtils.getImageUrl(key, width, height, mode, waterMark);
// assert
assertEquals("", result);
}

@Test
public void testGetImageUrlWithZeroDimensions() throws Throwable {
// arrange
String key = "test_key";
int width = 0;
int height = 0;
int mode = 1;
WaterMark waterMark = WaterMark.DIANPING;
// act
String result = ImageUtils.getImageUrl(key, width, height, mode, waterMark);
// assert
assertNotNull(result);
assertTrue(result.contains("qcloud.dpfile.com"));
assertTrue(result.contains(".jpg"));
}

@Test
public void testGetImageUrlWithNullKey() throws Throwable {
// arrange
String key = null;
int width = 100;
int height = 200;
int mode = 1;
WaterMark waterMark = WaterMark.DIANPING;
// act
String result = ImageUtils.getImageUrl(key, width, height, mode, waterMark);
// assert
assertEquals("", result);
}

@Test
public void testGetImageUrlWithNullWaterMark() throws Throwable {
// arrange
String key = "test_key";
int width = 100;
int height = 200;
int mode = 1;
WaterMark waterMark = null;
// act & assert
NullPointerException thrown = assertThrows(NullPointerException.class, () -> ImageUtils.getImageUrl(key, width, height, mode, waterMark), "Expected getImageUrl to throw NullPointerException when waterMark is null");
}

@Test
public void testGetImageUrlWithDifferentWatermarks() throws Throwable {
// arrange
String key = "test_key";
int width = 100;
int height = 200;
int mode = 1;
// act & assert
for (WaterMark waterMark : WaterMark.values()) {
String result = ImageUtils.getImageUrl(key, width, height, mode, waterMark);
assertNotNull(result);
assertTrue(result.contains("qcloud.dpfile.com"));
assertTrue(result.contains(".jpg"));
}
}

@Test
public void testGetImageUrlWithInvalidMode() throws Throwable {
// arrange
String key = "test_key";
int width = 100;
int height = 200;
// 无效的模式
int mode = -1;
WaterMark waterMark = WaterMark.DIANPING;
// act & assert
assertThrows(RuntimeException.class, () -> {
ImageUtils.getImageUrl(key, width, height, mode, waterMark);
}, "Expected getImageUrl to throw RuntimeException for invalid mode");
}

    @Test
    public void testGetFullJpgUrlEmptyKey() throws Throwable {
        // arrange
        String key = "";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.MEITUAN;
        String quality = "80";
        // act
        String result = ImageUtils.getFullJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetFullJpgUrlNullKey() throws Throwable {
        // arrange
        String key = null;
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.MEITUAN;
        String quality = "80";
        // act
        String result = ImageUtils.getFullJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetFullJpgUrlNormalCase() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.MEITUAN;
        String quality = "80";
        // act
        String result = ImageUtils.getFullJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.startsWith("https://"));
        assertTrue(result.endsWith(".jpg"));
        assertTrue(result.contains("pc/"));
    }

    @Test
    public void testGetFullJpgUrlEmptyQuality() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.MEITUAN;
        String quality = null;
        // act
        String result = ImageUtils.getFullJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.startsWith("https://"));
        assertTrue(result.endsWith(".jpg"));
        assertTrue(result.contains("pc/"));
    }

    @Test
    public void testGetFullJpgUrlEmptyWaterMark() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        String quality = "80";
        // act
        String result = ImageUtils.getFullJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.startsWith("https://"));
        assertTrue(result.endsWith(".jpg"));
        assertTrue(result.contains("pc/"));
    }

    @Test
    public void testGetFullJpgUrlZeroWidth() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 0;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.MEITUAN;
        String quality = "80";
        // act
        String result = ImageUtils.getFullJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.startsWith("https://"));
        assertTrue(result.endsWith(".jpg"));
        assertTrue(result.contains("pc/"));
    }

    @Test
    public void testGetFullJpgUrlZeroHeight() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 0;
        int mode = 1;
        WaterMark waterMark = WaterMark.MEITUAN;
        String quality = "80";
        // act
        String result = ImageUtils.getFullJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.startsWith("https://"));
        assertTrue(result.endsWith(".jpg"));
        assertTrue(result.contains("pc/"));
    }

    @Test
    public void testGetFullJpgUrlZeroMode() throws Throwable {
        // arrange
        String key = "testKey";
        int width = 100;
        int height = 100;
        int mode = 0;
        WaterMark waterMark = WaterMark.MEITUAN;
        String quality = "80";
        // act
        String result = ImageUtils.getFullJpgUrl(key, width, height, mode, waterMark, quality);
        // assert
        assertNotNull(result);
        assertTrue(result.startsWith("https://"));
        assertTrue(result.endsWith(".jpg"));
        assertTrue(result.contains("pc/"));
    }

    @Test
    public void testGetPicUrlEmptyKey() throws Throwable {
        // arrange
        String key = "";
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getPicUrl(key, VALID_WIDTH, VALID_HEIGHT, VALID_MODE, WaterMark.DIANPING, optionalParams);
        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    @Test
    public void testGetPicUrlNullKey() throws Throwable {
        // arrange
        String key = null;
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getPicUrl(key, VALID_WIDTH, VALID_HEIGHT, VALID_MODE, WaterMark.DIANPING, optionalParams);
        // assert
        assertEquals(StringUtils.EMPTY, result);
    }

    @Test
    public void testGetPicUrlValidKeyNoOptionalParams() throws Throwable {
        // arrange
        Map<String, String> optionalParams = null;
        // act
        String result = ImageUtils.getPicUrl(TEST_KEY, VALID_WIDTH, VALID_HEIGHT, VALID_MODE, WaterMark.DIANPING, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testGetPicUrlValidKeyWithOptionalParams() throws Throwable {
        // arrange
        Map<String, String> optionalParams = new HashMap<>();
        optionalParams.put("quality", "high");
        // act
        String result = ImageUtils.getPicUrl(TEST_KEY, VALID_WIDTH, VALID_HEIGHT, VALID_MODE, WaterMark.DIANPING, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testGetPicUrlZeroDimensions() throws Throwable {
        // arrange
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getPicUrl(TEST_KEY, 0, 0, VALID_MODE, WaterMark.EMPTY, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testGetPicUrlNegativeDimensions() throws Throwable {
        // arrange
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getPicUrl(TEST_KEY, -1, -1, VALID_MODE, WaterMark.DIANPING, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testGetPicUrlDifferentWaterMarks() throws Throwable {
        // arrange
        Map<String, String> optionalParams = new HashMap<>();
        for (WaterMark waterMark : WaterMark.values()) {
            // act
            String result = ImageUtils.getPicUrl(TEST_KEY, VALID_WIDTH, VALID_HEIGHT, VALID_MODE, waterMark, optionalParams);
            // assert
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
    }

    @Test
    public void testGetPicUrlDifferentModes() throws Throwable {
        // arrange
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getPicUrl(TEST_KEY, VALID_WIDTH, VALID_HEIGHT, VALID_MODE, WaterMark.DIANPING, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testGetPicUrlEmptyOptionalParams() throws Throwable {
        // arrange
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getPicUrl(TEST_KEY, VALID_WIDTH, VALID_HEIGHT, VALID_MODE, WaterMark.DIANPING, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testGetPicUrlLargeDimensions() throws Throwable {
        // arrange
        Map<String, String> optionalParams = new HashMap<>();
        int largeWidth = 1000;
        int largeHeight = 1000;
        // act
        String result = ImageUtils.getPicUrl(TEST_KEY, largeWidth, largeHeight, VALID_MODE, WaterMark.DIANPING, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    @DisplayName("Should return empty string when key is empty")
    public void testGetFullPicUrl_EmptyKey() throws Throwable {
        // arrange
        String key = "";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getFullPicUrl(key, width, height, mode, waterMark, optionalParams);
        // assert
        assertEquals("", result);
    }

    @Test
    @DisplayName("Should return empty string when key is null")
    public void testGetFullPicUrl_NullKey() throws Throwable {
        // arrange
        String key = null;
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getFullPicUrl(key, width, height, mode, waterMark, optionalParams);
        // assert
        assertEquals("", result);
    }

    @Test
    @DisplayName("Should generate URL successfully without optional params")
    public void testGetFullPicUrl_NoOptionalParams() throws Throwable {
        // arrange
        String key = "test-image-key";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        Map<String, String> optionalParams = null;
        // act
        String result = ImageUtils.getFullPicUrl(key, width, height, mode, waterMark, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    @DisplayName("Should generate URL successfully with optional params")
    public void testGetFullPicUrl_WithOptionalParams() throws Throwable {
        // arrange
        String key = "test-image-key";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        Map<String, String> optionalParams = new HashMap<>();
        optionalParams.put("quality", "90");
        optionalParams.put("format", "jpg");
        // act
        String result = ImageUtils.getFullPicUrl(key, width, height, mode, waterMark, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    @DisplayName("Should handle boundary values correctly")
    public void testGetFullPicUrl_BoundaryValues() throws Throwable {
        // arrange
        String key = "test-image-key";
        int width = 0;
        int height = 0;
        int mode = 0;
        WaterMark waterMark = WaterMark.EMPTY;
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getFullPicUrl(key, width, height, mode, waterMark, optionalParams);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    @DisplayName("Should handle different watermark types")
    public void testGetFullPicUrl_DifferentWatermarks() throws Throwable {
        // arrange
        String key = "test-image-key";
        int width = 100;
        int height = 100;
        int mode = 1;
        Map<String, String> optionalParams = new HashMap<>();
        // act & assert
        for (WaterMark waterMark : WaterMark.values()) {
            String result = ImageUtils.getFullPicUrl(key, width, height, mode, waterMark, optionalParams);
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
    }

    @Test
    @DisplayName("Should handle extreme dimension values")
    public void testGetFullPicUrl_ExtremeDimensions() throws Throwable {
        // arrange
        String key = "test-image-key";
        int[] dimensions = { Integer.MIN_VALUE, -1, 0, 1, Integer.MAX_VALUE };
        WaterMark waterMark = WaterMark.EMPTY;
        Map<String, String> optionalParams = new HashMap<>();
        // act & assert
        for (int dim : dimensions) {
            String result = ImageUtils.getFullPicUrl(key, dim, dim, 1, waterMark, optionalParams);
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
    }

    @Test
    @DisplayName("Should handle special characters in key")
    public void testGetFullPicUrl_SpecialCharactersInKey() throws Throwable {
        // arrange
        String[] specialKeys = { "test!@#$%^&*()", "test空格测试", "test\n\r\t", "test?=&" };
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        Map<String, String> optionalParams = new HashMap<>();
        // act & assert
        for (String key : specialKeys) {
            String result = ImageUtils.getFullPicUrl(key, width, height, mode, waterMark, optionalParams);
            assertNotNull(result);
            assertFalse(result.isEmpty());
        }
    }

    @Test
    public void testGetFullPicUrlEmptyKey() throws Throwable {
        // arrange
        String key = "";
        int width = 100;
        int height = 100;
        int mode = 1;
        WaterMark waterMark = WaterMark.EMPTY;
        Map<String, String> optionalParams = new HashMap<>();
        // act
        String result = ImageUtils.getFullPicUrl(key, width, height, mode, waterMark, optionalParams);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testFormatWithEmptyKey() throws Throwable {
        // arrange
        String key = "";
        int width = 100;
        int height = 100;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testFormatWithNullKey() throws Throwable {
        // arrange
        String key = null;
        int width = 100;
        int height = 100;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testFormatWithValidKeyHttpsSuccess() throws Throwable {
        // arrange
        String key = "test_key";
        int width = 100;
        int height = 100;
        String expectedUrl = "https://test.url";
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertNotNull(result);
        assertTrue(result.equals(key) || result.startsWith("http"));
    }

    @Test
    public void testFormatWithHttpsFailureFallbackToFullUrl() throws Throwable {
        // arrange
        String key = "test_key";
        int width = 100;
        int height = 100;
        String expectedUrl = "http://test.url";
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertNotNull(result);
        assertTrue(result.equals(key) || result.startsWith("http"));
    }

    @Test
    public void testFormatWithAllUrlFailures() throws Throwable {
        // arrange
        String key = "test_key";
        int width = 100;
        int height = 100;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertNotNull(result);
        assertTrue(result.equals(key) || result.startsWith("http"));
    }

    @Test
    public void testFormatWithMaxDimensions() throws Throwable {
        // arrange
        String key = "test_key";
        int width = Integer.MAX_VALUE;
        int height = Integer.MAX_VALUE;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertNotNull(result);
        assertTrue(result.equals(key) || result.startsWith("http"));
    }

    @Test
    public void testFormatWithMinDimensions() throws Throwable {
        // arrange
        String key = "test_key";
        int width = 1;
        int height = 1;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertNotNull(result);
        assertTrue(result.equals(key) || result.startsWith("http"));
    }

    @Test
    public void testFormatWithZeroDimensions() throws Throwable {
        // arrange
        String key = "test_key";
        int width = 0;
        int height = 0;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertNotNull(result);
        assertTrue(result.equals(key) || result.startsWith("http"));
    }

    @Test
    public void testFormatWithSpecialCharacters() throws Throwable {
        // arrange
        String key = "test/key#with@special&chars";
        int width = 100;
        int height = 100;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertNotNull(result);
        assertTrue(result.equals(key) || result.startsWith("http"));
    }

    @Test
    public void testFormatWithNegativeDimensions() throws Throwable {
        // arrange
        String key = "test_key";
        int width = -1;
        int height = -1;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertNotNull(result);
        assertTrue(result.equals(key) || result.startsWith("http"));
    }

    @Test
    public void testFormatEmptyKey() throws Throwable {
        // arrange
        String key = "";
        int width = 100;
        int height = 100;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testFormatNullKey() throws Throwable {
        // arrange
        String key = null;
        int width = 100;
        int height = 100;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testFormatValidKey() throws Throwable {
        // arrange
        String key = "test_key";
        int width = 100;
        int height = 100;
        // act
        String result = ImageUtils.format(key, width, height);
        // assert
        assertNotNull(result);
        // 由于无法mock静态方法和构造函数，这里只能验证返回值不为空
        // 实际返回值将依赖于真实的PictureUrlGenerator实现
    }
}
