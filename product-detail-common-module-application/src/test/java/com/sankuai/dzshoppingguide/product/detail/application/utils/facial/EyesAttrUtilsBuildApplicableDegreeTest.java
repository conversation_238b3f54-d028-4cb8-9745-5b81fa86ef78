package com.sankuai.dzshoppingguide.product.detail.application.utils.facial;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.*;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class EyesAttrUtilsBuildApplicableDegreeTest {

    /**
     * 测试applicable_degrees为空的情况
     */
    @Test
    public void testBuildApplicableDegree_EmptyApplicableDegrees() {
        // arrange
        ProductAttr productAttr = mock(ProductAttr.class);
        when(productAttr.getSkuAttrValues("applicable_degrees")).thenReturn(Collections.emptyList());
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildApplicableDegree(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * 测试只有近视且只有最大值的情况
     */
    @Test
    public void testBuildApplicableDegree_OnlyMyopiaWithMaxValue() {
        // arrange
        ProductAttr productAttr = mock(ProductAttr.class);
        when(productAttr.getSkuAttrValues("applicable_degrees")).thenReturn(Arrays.asList("近视"));
        when(productAttr.getSkuAttrFirstValue("ApplicableMinDiameter")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("ApplicableMyopiaMaxValue")).thenReturn("500");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildApplicableDegree(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("适用度数", result.get().getTitle());
        assertEquals("近视500度以下", result.get().getContent());
    }

    /**
     * 测试近视和散光组合的情况
     */
    @Test
    public void testBuildApplicableDegree_MyopiaAndAstigmatism() {
        // arrange
        ProductAttr productAttr = mock(ProductAttr.class);
        when(productAttr.getSkuAttrValues("applicable_degrees")).thenReturn(Arrays.asList("近视", "散光"));
        // 近视参数
        when(productAttr.getSkuAttrFirstValue("ApplicableMinDiameter")).thenReturn("100");
        when(productAttr.getSkuAttrFirstValue("ApplicableMyopiaMaxValue")).thenReturn("500");
        // 散光参数
        when(productAttr.getSkuAttrFirstValue("ApplicableMinimumAstigmatism")).thenReturn("50");
        when(productAttr.getSkuAttrFirstValue("ApplicableMaximumAstigmatism")).thenReturn("200");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildApplicableDegree(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("适用度数", result.get().getTitle());
        assertEquals("近视100-500度、散光50-200度", result.get().getContent());
    }

    /**
     * 测试处理后degreeRanges为空的情况
     */
    @Test
    public void testBuildApplicableDegree_EmptyDegreeRanges() {
        // arrange
        ProductAttr productAttr = mock(ProductAttr.class);
        when(productAttr.getSkuAttrValues("applicable_degrees")).thenReturn(Arrays.asList("近视", "散光", "远视"));
        // 所有参数都为空
        when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn(null);
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildApplicableDegree(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * 测试远视只有最小值的情况
     */
    @Test
    public void testBuildApplicableDegree_HyperopiaWithMinValue() {
        // arrange
        ProductAttr productAttr = mock(ProductAttr.class);
        when(productAttr.getSkuAttrValues("applicable_degrees")).thenReturn(Arrays.asList("远视"));
        // 远视参数
        when(productAttr.getSkuAttrFirstValue("ApplicableHyperopicMinimumValue")).thenReturn("100");
        when(productAttr.getSkuAttrFirstValue("ApplicableHyperopicMaximumValue")).thenReturn(null);
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildApplicableDegree(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("适用度数", result.get().getTitle());
        assertEquals("远视100度以上", result.get().getContent());
    }

    /**
     * 测试所有度数类型都有且参数完整的情况
     */
    @Test
    public void testBuildApplicableDegree_AllTypesWithCompleteParams() {
        // arrange
        ProductAttr productAttr = mock(ProductAttr.class);
        when(productAttr.getSkuAttrValues("applicable_degrees")).thenReturn(Arrays.asList("近视", "散光", "远视"));
        // 近视参数
        when(productAttr.getSkuAttrFirstValue("ApplicableMinDiameter")).thenReturn("100");
        when(productAttr.getSkuAttrFirstValue("ApplicableMyopiaMaxValue")).thenReturn("500");
        // 散光参数
        when(productAttr.getSkuAttrFirstValue("ApplicableMinimumAstigmatism")).thenReturn("50");
        when(productAttr.getSkuAttrFirstValue("ApplicableMaximumAstigmatism")).thenReturn("200");
        // 远视参数
        when(productAttr.getSkuAttrFirstValue("ApplicableHyperopicMinimumValue")).thenReturn("75");
        when(productAttr.getSkuAttrFirstValue("ApplicableHyperopicMaximumValue")).thenReturn("300");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildApplicableDegree(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("适用度数", result.get().getTitle());
        assertEquals("近视100-500度、散光50-200度、远视75-300度", result.get().getContent());
    }
}
