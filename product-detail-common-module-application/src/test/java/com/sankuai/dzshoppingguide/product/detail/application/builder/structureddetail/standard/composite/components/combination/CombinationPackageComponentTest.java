package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.components.combination;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.base.DetailComponentParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.base.DetailComposite;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.DealGroupServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.OptionalServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CombinationPackageComponentTest {

    @InjectMocks
    private CombinationPackageComponent combinationPackageComponent;

    @Mock
    private DetailComposite subComponents;

    /**
     * Test build method when productServiceProject is null
     */
    @Test
    public void testBuildWhenProductServiceProjectIsNull() throws Throwable {
        // arrange
        DetailComponentParam param = DetailComponentParam.builder().build();
        // act
        List<DealDetailStructuredDetailVO> result = combinationPackageComponent.build(param);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test build method when productServiceProject.getServiceProject() is null
     */
    @Test
    public void testBuildWhenServiceProjectIsNull() throws Throwable {
        // arrange
        DetailComponentParam param = DetailComponentParam.builder().productServiceProject(new ProductServiceProject(null)).build();
        // act
        List<DealDetailStructuredDetailVO> result = combinationPackageComponent.build(param);
        // assert
        assertTrue(result.isEmpty());
    }
}
