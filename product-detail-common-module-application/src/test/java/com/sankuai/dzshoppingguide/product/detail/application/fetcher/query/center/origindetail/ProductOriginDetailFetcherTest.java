package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.builder.model.OriginDetailBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
class ProductOriginDetailFetcherTest {

    private final ProductOriginDetailFetcher fetcher = new ProductOriginDetailFetcher();

    @Mock
    private QueryByDealGroupIdRequestBuilder requestBuilder;

    /**
     * Test when aggregateResult is null
     */
    @Test
    public void testMapResultWhenAggregateResultIsNull() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = null;
        // act
        FetcherResponse<ProductOriginDetail> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNull(result.getReturnValue().getProductIntroduction());
    }

    /**
     * Test when aggregateResult's returnValue is null
     */
    @Test
    public void testMapResultWhenReturnValueIsNull() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(null);
        // act
        FetcherResponse<ProductOriginDetail> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNull(result.getReturnValue().getProductIntroduction());
    }

    /**
     * Test when dealGroupDTO is null
     */
    @Test
    public void testMapResultWhenDealGroupDTOIsNull() throws Throwable {
        // arrange
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(returnValue.getDealGroupDTO()).thenReturn(null);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        // act
        FetcherResponse<ProductOriginDetail> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNull(result.getReturnValue().getProductIntroduction());
    }

    /**
     * Test when originDetails is null
     */
    @Test
    public void testMapResultWhenOriginDetailsIsNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getOriginDetails()).thenReturn(null);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        // act
        FetcherResponse<ProductOriginDetail> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNull(result.getReturnValue().getProductIntroduction());
    }

    /**
     * Test when originDetails is empty
     */
    @Test
    public void testMapResultWhenOriginDetailsIsEmpty() throws Throwable {
        // arrange
        Map<String, String> originDetails = new HashMap<>();
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getOriginDetails()).thenReturn(originDetails);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        // act
        FetcherResponse<ProductOriginDetail> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNull(result.getReturnValue().getProductIntroduction());
    }

    /**
     * Test when productIntro is missing in originDetails
     */
    @Test
    public void testMapResultWhenProductIntroIsMissing() throws Throwable {
        // arrange
        Map<String, String> originDetails = new HashMap<>();
        originDetails.put("otherKey", "value");
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getOriginDetails()).thenReturn(originDetails);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        // act
        FetcherResponse<ProductOriginDetail> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNull(result.getReturnValue().getProductIntroduction());
    }

    /**
     * Test when productIntro is blank
     */
    @Test
    public void testMapResultWhenProductIntroIsBlank() throws Throwable {
        // arrange
        Map<String, String> originDetails = new HashMap<>();
        originDetails.put("productIntro", " ");
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getOriginDetails()).thenReturn(originDetails);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        // act
        FetcherResponse<ProductOriginDetail> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNull(result.getReturnValue().getProductIntroduction());
    }

    /**
     * Test when productIntro is invalid JSON
     */
    @Test
    public void testMapResultWhenProductIntroIsInvalidJson() throws Throwable {
        // arrange
        Map<String, String> originDetails = new HashMap<>();
        originDetails.put("productIntro", "{invalid json");
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getOriginDetails()).thenReturn(originDetails);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        // act
        FetcherResponse<ProductOriginDetail> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNull(result.getReturnValue().getProductIntroduction());
    }

    /**
     * Test when productIntro is valid JSON and can be converted to ProductIntroductionDTO
     */
    @Test
    public void testMapResultWhenProductIntroIsValidJson() throws Throwable {
        // arrange
        String validJson = "{\"version\":1,\"headline\":\"Test Headline\",\"content\":[]}";
        Map<String, String> originDetails = new HashMap<>();
        originDetails.put("productIntro", validJson);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(dealGroupDTO.getOriginDetails()).thenReturn(originDetails);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        // act
        FetcherResponse<ProductOriginDetail> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNotNull(result.getReturnValue().getProductIntroduction());
        assertEquals(1, result.getReturnValue().getProductIntroduction().getVersion());
        assertEquals("Test Headline", result.getReturnValue().getProductIntroduction().getHeadline());
    }

    @Test
    public void testFulfillRequestNormalCase() throws Throwable {
        // arrange
        ProductOriginDetailFetcher fetcher = new ProductOriginDetailFetcher();
        when(requestBuilder.originDetail(any())).thenReturn(requestBuilder);
        // act
        fetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder).originDetail(any(OriginDetailBuilder.class));
    }

    @Test
    public void testFulfillRequestWithNullBuilder() throws Throwable {
        // arrange
        ProductOriginDetailFetcher fetcher = new ProductOriginDetailFetcher();
        // act & assert
        assertThrows(NullPointerException.class, () -> fetcher.fulfillRequest(null));
    }

    @Test
    public void testFulfillRequestPropagatesBuilderExceptions() throws Throwable {
        // arrange
        ProductOriginDetailFetcher fetcher = new ProductOriginDetailFetcher();
        when(requestBuilder.originDetail(any())).thenThrow(new RuntimeException("Builder error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> fetcher.fulfillRequest(requestBuilder));
        verify(requestBuilder).originDetail(any(OriginDetailBuilder.class));
    }
}
