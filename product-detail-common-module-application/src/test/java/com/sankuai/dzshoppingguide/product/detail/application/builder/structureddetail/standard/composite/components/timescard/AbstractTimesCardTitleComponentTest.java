package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.components.timescard;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.base.DetailComponentParam;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import java.util.*;
import org.apache.commons.lang3.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

/**
 * 单元测试：覆盖 AbstractTimesCardTitleComponent.build(DetailComponentParam param) 主要分支和逻辑
 */
@RunWith(MockitoJUnitRunner.class)
public class AbstractTimesCardTitleComponentTest {

    /**
     * 测试：baseInfo为null时，直接返回null
     */
    @Test
    public void testBuild_BaseInfoNull_ReturnsNull() {
        // arrange
        DetailComponentParam param = mock(DetailComponentParam.class);
        when(param.getBaseInfo()).thenReturn(null);
        AbstractTimesCardTitleComponent component = new TestTimesCardTitleComponent();
        // act
        List<DealDetailStructuredDetailVO> result = component.build(param);
        // assert
        assertNull(result);
    }

    /**
     * 测试：skuAttr为null时，直接返回null
     */
    @Test
    public void testBuild_SkuAttrNull_ReturnsNull() {
        // arrange
        DetailComponentParam param = mock(DetailComponentParam.class);
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        when(param.getBaseInfo()).thenReturn(baseInfo);
        when(param.getSkuAttr()).thenReturn(null);
        AbstractTimesCardTitleComponent component = new TestTimesCardTitleComponent();
        // act
        List<DealDetailStructuredDetailVO> result = component.build(param);
        // assert
        assertNull(result);
    }

    /**
     * 测试：isTimesCard=false，返回null
     */
    @Test
    public void testBuild_IsTimesCardFalse_ReturnsNull() {
        // arrange
        DetailComponentParam param = mock(DetailComponentParam.class);
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        SkuAttr skuAttr = mock(SkuAttr.class);
        when(param.getBaseInfo()).thenReturn(baseInfo);
        when(param.getSkuAttr()).thenReturn(skuAttr);
        // mock TimesDealUtil.isDealTimesCard 返回false
        try (MockedStatic<TimesDealUtil> timesDealUtilMockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            timesDealUtilMockedStatic.when(() -> TimesDealUtil.isDealTimesCard(baseInfo)).thenReturn(false);
            AbstractTimesCardTitleComponent component = new TestTimesCardTitleComponent();
            // act
            List<DealDetailStructuredDetailVO> result = component.build(param);
            // assert
            assertNull(result);
        }
    }

    /**
     * 测试：isTimesCard=true，但times为null，返回null
     */
    @Test
    public void testBuild_TimesIsNull_ReturnsNull() {
        // arrange
        DetailComponentParam param = mock(DetailComponentParam.class);
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        SkuAttr skuAttr = mock(SkuAttr.class);
        when(param.getBaseInfo()).thenReturn(baseInfo);
        when(param.getSkuAttr()).thenReturn(skuAttr);
        SkuDefaultSelect skuDefaultSelect = mock(SkuDefaultSelect.class);
        when(param.getSkuDefaultSelect()).thenReturn(skuDefaultSelect);
        when(skuDefaultSelect.getSelectedSkuId()).thenReturn(123L);
        // mock TimesDealUtil.isDealTimesCard 返回true
        try (MockedStatic<TimesDealUtil> timesDealUtilMockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            timesDealUtilMockedStatic.when(() -> TimesDealUtil.isDealTimesCard(baseInfo)).thenReturn(true);
            // skuAttr.getSkuAttrs
            when(skuAttr.getSkuAttrs(123L)).thenReturn(Collections.emptyMap());
            // skuAttr.getSkuAttrFirstValue 返回null
            when(skuAttr.getSkuAttrFirstValue(123L, com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrKeys.SYS_MULTI_SALE_NUMBER)).thenReturn(null);
            AbstractTimesCardTitleComponent component = new TestTimesCardTitleComponent();
            // act
            List<DealDetailStructuredDetailVO> result = component.build(param);
            // assert
            assertNull(result);
        }
    }

    /**
     * 测试：isTimesCard=true，但times为空字符串，返回null
     */
    @Test
    public void testBuild_TimesIsBlank_ReturnsNull() {
        // arrange
        DetailComponentParam param = mock(DetailComponentParam.class);
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        SkuAttr skuAttr = mock(SkuAttr.class);
        when(param.getBaseInfo()).thenReturn(baseInfo);
        when(param.getSkuAttr()).thenReturn(skuAttr);
        SkuDefaultSelect skuDefaultSelect = mock(SkuDefaultSelect.class);
        when(param.getSkuDefaultSelect()).thenReturn(skuDefaultSelect);
        when(skuDefaultSelect.getSelectedSkuId()).thenReturn(456L);
        // mock TimesDealUtil.isDealTimesCard 返回true
        try (MockedStatic<TimesDealUtil> timesDealUtilMockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            timesDealUtilMockedStatic.when(() -> TimesDealUtil.isDealTimesCard(baseInfo)).thenReturn(true);
            // skuAttr.getSkuAttrs
            when(skuAttr.getSkuAttrs(456L)).thenReturn(Collections.emptyMap());
            // skuAttr.getSkuAttrFirstValue 返回空字符串
            when(skuAttr.getSkuAttrFirstValue(456L, com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrKeys.SYS_MULTI_SALE_NUMBER)).thenReturn("   ");
            AbstractTimesCardTitleComponent component = new TestTimesCardTitleComponent();
            // act
            List<DealDetailStructuredDetailVO> result = component.build(param);
            // assert
            assertNull(result);
        }
    }

    /**
     * 测试：isTimesCard=true，times不为空，返回定制化标题VO
     */
    @Test
    public void testBuild_TimesCardAndTimesNotBlank_ReturnsVOList() {
        // arrange
        DetailComponentParam param = mock(DetailComponentParam.class);
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        SkuAttr skuAttr = mock(SkuAttr.class);
        when(param.getBaseInfo()).thenReturn(baseInfo);
        when(param.getSkuAttr()).thenReturn(skuAttr);
        SkuDefaultSelect skuDefaultSelect = mock(SkuDefaultSelect.class);
        when(param.getSkuDefaultSelect()).thenReturn(skuDefaultSelect);
        when(skuDefaultSelect.getSelectedSkuId()).thenReturn(789L);
        // mock TimesDealUtil.isDealTimesCard 返回true
        try (MockedStatic<TimesDealUtil> timesDealUtilMockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            timesDealUtilMockedStatic.when(() -> TimesDealUtil.isDealTimesCard(baseInfo)).thenReturn(true);
            // skuAttr.getSkuAttrs
            when(skuAttr.getSkuAttrs(789L)).thenReturn(Collections.emptyMap());
            // skuAttr.getSkuAttrFirstValue 返回非空字符串
            when(skuAttr.getSkuAttrFirstValue(789L, com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrKeys.SYS_MULTI_SALE_NUMBER)).thenReturn("5");
            AbstractTimesCardTitleComponent component = new TestTimesCardTitleComponent();
            // act
            List<DealDetailStructuredDetailVO> result = component.build(param);
            // assert
            assertNotNull(result);
            assertEquals(1, result.size());
            DealDetailStructuredDetailVO vo = result.get(0);
            assertEquals("次卡-5次", vo.getTitle());
            assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_14.getType(), vo.getType());
        }
    }

    /**
     * 测试：skuDefaultSelect为null时，skuId为0L
     */
    @Test
    public void testBuild_SkuDefaultSelectNull_SkuIdIsZero() {
        // arrange
        DetailComponentParam param = mock(DetailComponentParam.class);
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        SkuAttr skuAttr = mock(SkuAttr.class);
        when(param.getBaseInfo()).thenReturn(baseInfo);
        when(param.getSkuAttr()).thenReturn(skuAttr);
        when(param.getSkuDefaultSelect()).thenReturn(null);
        // mock TimesDealUtil.isDealTimesCard 返回true
        try (MockedStatic<TimesDealUtil> timesDealUtilMockedStatic = Mockito.mockStatic(TimesDealUtil.class)) {
            timesDealUtilMockedStatic.when(() -> TimesDealUtil.isDealTimesCard(baseInfo)).thenReturn(true);
            // skuAttr.getSkuAttrs
            when(skuAttr.getSkuAttrs(0L)).thenReturn(Collections.emptyMap());
            // skuAttr.getSkuAttrFirstValue 返回非空字符串
            when(skuAttr.getSkuAttrFirstValue(0L, com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrKeys.SYS_MULTI_SALE_NUMBER)).thenReturn("8");
            AbstractTimesCardTitleComponent component = new TestTimesCardTitleComponent();
            // act
            List<DealDetailStructuredDetailVO> result = component.build(param);
            // assert
            assertNotNull(result);
            assertEquals(1, result.size());
            DealDetailStructuredDetailVO vo = result.get(0);
            assertEquals("次卡-8次", vo.getTitle());
            assertEquals(ViewComponentTypeEnum.DETAIL_TYPE_14.getType(), vo.getType());
        }
    }

    /**
     * 测试用的实现类，定制buildTitleFormat
     */
    private static class TestTimesCardTitleComponent extends AbstractTimesCardTitleComponent {

        @Override
        String buildTitleFormat() {
            // 模拟定制化标题格式
            return "次卡-%s次";
        }

        @Override
        public String getName() {
            return "TestTimesCardTitleComponent";
        }

        @Override
        public List<DealDetailStructuredDetailVO> build(String componentName, DetailComponentParam param) {
            return null;
        }
    }
}
