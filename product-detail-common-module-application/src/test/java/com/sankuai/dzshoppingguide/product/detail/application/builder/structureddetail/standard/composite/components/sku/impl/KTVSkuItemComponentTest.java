package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.components.sku.impl;

import static org.junit.Assert.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.resource.ProductResourceInfo;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.ResourceDTO;
import java.util.ArrayList;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashMap;
import java.util.List;
import java.util.List;
import java.util.Map;
import java.util.Map;
import org.junit.Test;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class KTVSkuItemComponentTest {

    @Test
    public void testBuildCustomSkuAttrMapWithEmptyInputs() throws Throwable {
        KTVSkuItemComponent component = new KTVSkuItemComponent();
        Map<String, Object> skuAttrMap = new HashMap<>();
        ProductResourceInfo productResourceInfo = new ProductResourceInfo();
        component.buildCustomSkuAttrMap(skuAttrMap, productResourceInfo);
        assertFalse(skuAttrMap.containsKey("ktvRoomType"));
    }

    @Test
    public void testBuildCustomSkuAttrMapWithOldFormat() throws Throwable {
        KTVSkuItemComponent component = new KTVSkuItemComponent();
        Map<String, Object> skuAttrMap = new HashMap<>();
        skuAttrMap.put("ktv_roomtype", "大包");
        ProductResourceInfo productResourceInfo = new ProductResourceInfo();
        component.buildCustomSkuAttrMap(skuAttrMap, productResourceInfo);
        assertTrue(skuAttrMap.containsKey("ktvRoomType"));
        assertEquals("大包", skuAttrMap.get("ktvRoomType"));
    }

    @Test
    public void testBuildCustomSkuAttrMapWithNewFormat() throws Throwable {
        KTVSkuItemComponent component = new KTVSkuItemComponent();
        Map<String, Object> skuAttrMap = new HashMap<>();
        skuAttrMap.put("ktv_roomids", "1001、1002");
        ProductResourceInfo productResourceInfo = new ProductResourceInfo();
        List<ResourceDTO> resources = new ArrayList<>();
        ResourceDTO resource1 = new ResourceDTO();
        resource1.setResourceId(1001L);
        resource1.setResourceName("大包");
        ResourceDTO resource2 = new ResourceDTO();
        resource2.setResourceId(1002L);
        resource2.setResourceName("中包");
        resources.add(resource1);
        resources.add(resource2);
        productResourceInfo.setResourceInfos(resources);
        component.buildCustomSkuAttrMap(skuAttrMap, productResourceInfo);
        assertTrue(skuAttrMap.containsKey("ktvRoomType"));
        assertEquals("大包|中包", skuAttrMap.get("ktvRoomType"));
    }

    @Test
    public void testBuildCustomSkuAttrMapWithBothFormats() throws Throwable {
        KTVSkuItemComponent component = new KTVSkuItemComponent();
        Map<String, Object> skuAttrMap = new HashMap<>();
        skuAttrMap.put("ktv_roomtype", "小包");
        skuAttrMap.put("ktv_roomids", "1001");
        ProductResourceInfo productResourceInfo = new ProductResourceInfo();
        List<ResourceDTO> resources = new ArrayList<>();
        ResourceDTO resource = new ResourceDTO();
        resource.setResourceId(1001L);
        resource.setResourceName("大包");
        resources.add(resource);
        productResourceInfo.setResourceInfos(resources);
        component.buildCustomSkuAttrMap(skuAttrMap, productResourceInfo);
        assertTrue(skuAttrMap.containsKey("ktvRoomType"));
        assertEquals("大包", skuAttrMap.get("ktvRoomType"));
    }

    @Test
    public void testBuildCustomSkuAttrMapWithInvalidRoomIds() throws Throwable {
        KTVSkuItemComponent component = new KTVSkuItemComponent();
        Map<String, Object> skuAttrMap = new HashMap<>();
        skuAttrMap.put("ktv_roomids", "invalid");
        ProductResourceInfo productResourceInfo = new ProductResourceInfo();
        List<ResourceDTO> resources = new ArrayList<>();
        productResourceInfo.setResourceInfos(resources);
        component.buildCustomSkuAttrMap(skuAttrMap, productResourceInfo);
        assertTrue(skuAttrMap.containsKey("ktvRoomType"));
        // Adjusted expectation
        assertEquals("", skuAttrMap.get("ktvRoomType").toString());
    }

    @Test
    public void testBuildCustomSkuAttrMapWithNullProductResourceInfo() throws Throwable {
        KTVSkuItemComponent component = new KTVSkuItemComponent();
        Map<String, Object> skuAttrMap = new HashMap<>();
        skuAttrMap.put("ktv_roomids", "1001");
        component.buildCustomSkuAttrMap(skuAttrMap, null);
        // Adjusted expectation
        assertTrue(skuAttrMap.containsKey("ktvRoomType"));
    }
}
