package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.AbstractReminderInfoBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.EffectiveDateHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ReminderInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;

class FootMassageReminderBuilderTest {

    private TestFootMassageReminderBuilder builder;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private ProductBaseInfo baseInfo;

    private static class TestFootMassageReminderBuilder extends FootMassageReminderBuilder {

        private ProductDetailReminderVO baseReminderInfo;

        private ProductAttr productAttr;

        private ProductBaseInfo baseInfo;

        @Override
        public ProductDetailReminderVO getBaseReminderInfo() {
            return baseReminderInfo;
        }

        public void setBaseReminderInfo(ProductDetailReminderVO baseReminderInfo) {
            this.baseReminderInfo = baseReminderInfo;
        }

        public void setProductAttr(ProductAttr productAttr) {
            this.productAttr = productAttr;
        }

        public void setBaseInfo(ProductBaseInfo baseInfo) {
            this.baseInfo = baseInfo;
        }

        @Override
        public ProductDetailReminderVO preBuild() {
            ProductDetailReminderVO baseReminderInfo = getBaseReminderInfo();
            if (baseReminderInfo == null || CollectionUtils.isEmpty(baseReminderInfo.getContents())) {
                return null;
            }
            this.baseInfo = baseInfo;
            this.productAttr = productAttr;
            if (this.productAttr == null) {
                return null;
            }
            List<GuaranteeInstructionsContentVO> contents = baseReminderInfo.getContents();
            if (AvailableTimeHelper.hasAvailableTimePeriod(productAttr)) {
                AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
            } else {
                AvailableTimeHelper.allTimePeriodReminder(contents, baseInfo);
            }
            ReminderInfoUtils.buildReminderInfo(EffectiveDateHelper.getEffectiveDate(baseInfo)).ifPresent(contents::add);
            return baseReminderInfo;
        }
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        builder = new TestFootMassageReminderBuilder();
    }

    private ProductDetailReminderVO createBaseReminderInfo() {
        ProductDetailReminderVO reminder = new ProductDetailReminderVO();
        GuaranteeInstructionsBarLayerVO layer = new GuaranteeInstructionsBarLayerVO();
        layer.setType(2);
        reminder.setLayer(layer);
        reminder.setContents(new ArrayList<>());
        GuaranteeInstructionsContentVO content = new GuaranteeInstructionsContentVO();
        content.setText("Initial content");
        reminder.getContents().add(content);
        return reminder;
    }

    /**
     * Test when getBaseReminderInfo returns null
     */
    @Test
    public void testPreBuildWhenBaseReminderInfoIsNull() throws Throwable {
        // arrange
        builder.setBaseReminderInfo(null);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNull(result);
    }

    /**
     * Test when getBaseReminderInfo returns empty contents
     */
    @Test
    public void testPreBuildWhenBaseReminderInfoHasEmptyContents() throws Throwable {
        // arrange
        ProductDetailReminderVO emptyReminder = new ProductDetailReminderVO();
        emptyReminder.setContents(Collections.emptyList());
        builder.setBaseReminderInfo(emptyReminder);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNull(result);
    }

    /**
     * Test when productAttr is null
     */
    @Test
    public void testPreBuildWhenProductAttrIsNull() throws Throwable {
        // arrange
        ProductDetailReminderVO reminder = createBaseReminderInfo();
        builder.setBaseReminderInfo(reminder);
        builder.setProductAttr(null);
        builder.setBaseInfo(baseInfo);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNull(result);
    }

    /**
     * Test when product has partial available time periods
     */
    @Test
    public void testPreBuildWithPartialAvailableTimePeriods() throws Throwable {
        // arrange
        ProductDetailReminderVO reminder = createBaseReminderInfo();
        builder.setBaseReminderInfo(reminder);
        when(productAttr.getSkuAttrFirstValue("AvailableTimePeriod3")).thenReturn("部分时间可用");
        when(productAttr.getSkuAttrValue("TimeRange3")).thenReturn(Lists.newArrayList("1", "2", "3"));
        when(productAttr.getSkuAttrFirstValue("StartTimePoint")).thenReturn("09:00");
        when(productAttr.getSkuAttrFirstValue("EndTimePoint")).thenReturn("18:00");
        builder.setProductAttr(productAttr);
        builder.setBaseInfo(baseInfo);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNotNull(result);
        assertFalse(result.getContents().isEmpty());
    }

    /**
     * Test when product has all time periods available
     */
    @Test
    public void testPreBuildWithAllTimePeriodsAvailable() throws Throwable {
        // arrange
        ProductDetailReminderVO reminder = createBaseReminderInfo();
        builder.setBaseReminderInfo(reminder);
        when(productAttr.getSkuAttrFirstValue("AvailableTimePeriod3")).thenReturn("全部时间可用");
        when(baseInfo.getRule()).thenReturn(null);
        builder.setProductAttr(productAttr);
        builder.setBaseInfo(baseInfo);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNotNull(result);
        assertFalse(result.getContents().isEmpty());
    }

    /**
     * Test when effective date is available
     */
    @Test
    public void testPreBuildWithEffectiveDate() throws Throwable {
        // arrange
        ProductDetailReminderVO reminder = createBaseReminderInfo();
        builder.setBaseReminderInfo(reminder);
        when(productAttr.getSkuAttrFirstValue("AvailableTimePeriod3")).thenReturn("全部时间可用");
        DealGroupRuleDTO ruleDTO = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRuleDTO = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO effectiveDateDTO = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getUseRule()).thenReturn(useRuleDTO);
        when(useRuleDTO.getReceiptEffectiveDate()).thenReturn(effectiveDateDTO);
        when(effectiveDateDTO.getReceiptDateType()).thenReturn(0);
        when(effectiveDateDTO.getReceiptBeginDate()).thenReturn("2023-01-01 00:00:00");
        when(effectiveDateDTO.getReceiptEndDate()).thenReturn("2023-12-31 23:59:59");
        builder.setProductAttr(productAttr);
        builder.setBaseInfo(baseInfo);
        // act
        ProductDetailReminderVO result = builder.preBuild();
        // assert
        assertNotNull(result);
        assertFalse(result.getContents().isEmpty());
    }
}
