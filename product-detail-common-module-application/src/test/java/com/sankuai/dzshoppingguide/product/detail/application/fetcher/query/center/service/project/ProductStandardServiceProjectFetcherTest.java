package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import org.junit.jupiter.api.Test;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.builder.model.ServiceProjectBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProductStandardServiceProjectFetcherTest {

    private final ProductStandardServiceProjectFetcher fetcher = new ProductStandardServiceProjectFetcher();

    @Mock
    private QueryByDealGroupIdRequestBuilder requestBuilder;

    /**
     * Tests the happy path where all values are present in the response chain
     */
    @Test
    public void testMapResultAllValuesPresent() throws Throwable {
        // arrange
        StandardServiceProjectDTO expectedDto = new StandardServiceProjectDTO();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setStandardServiceProject(expectedDto);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> inputResponse = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductStandardServiceProject> result = fetcher.mapResult(inputResponse);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(expectedDto, result.getReturnValue().getStandardServiceProject());
    }

    /**
     * Tests when aggregateResult is null
     */
    @Test
    public void testMapResultAggregateResultNull() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> inputResponse = null;
        // act
        FetcherResponse<ProductStandardServiceProject> result = fetcher.mapResult(inputResponse);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue().getStandardServiceProject());
    }

    /**
     * Tests when aggregateResult.getReturnValue() is null
     */
    @Test
    public void testMapResultReturnValueNull() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> inputResponse = FetcherResponse.succeed(null);
        // act
        FetcherResponse<ProductStandardServiceProject> result = fetcher.mapResult(inputResponse);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue().getStandardServiceProject());
    }

    /**
     * Tests when getDealGroupDTO() returns null
     */
    @Test
    public void testMapResultDealGroupDTONull() throws Throwable {
        // arrange
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(null);
        FetcherResponse<QueryCenterAggregateReturnValue> inputResponse = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductStandardServiceProject> result = fetcher.mapResult(inputResponse);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue().getStandardServiceProject());
    }

    /**
     * Tests when getStandardServiceProject() returns null
     */
    @Test
    public void testMapResultStandardServiceProjectNull() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setStandardServiceProject(null);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> inputResponse = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductStandardServiceProject> result = fetcher.mapResult(inputResponse);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue().getStandardServiceProject());
    }

    @Test
    public void testFulfillRequest_NormalCase() throws Throwable {
        // arrange
        ProductStandardServiceProjectFetcher fetcher = new ProductStandardServiceProjectFetcher();
        when(requestBuilder.serviceProject(any(ServiceProjectBuilder.class))).thenReturn(requestBuilder);
        // act
        fetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder).serviceProject(any(ServiceProjectBuilder.class));
    }

    @Test
    public void testFulfillRequest_NullBuilder() throws Throwable {
        // arrange
        ProductStandardServiceProjectFetcher fetcher = new ProductStandardServiceProjectFetcher();
        // act & assert
        org.junit.jupiter.api.Assertions.assertThrows(NullPointerException.class, () -> {
            fetcher.fulfillRequest(null);
        });
    }

    @Test
    public void testFulfillRequest_VerifyServiceProjectBuilderConfiguration() throws Throwable {
        // arrange
        ProductStandardServiceProjectFetcher fetcher = new ProductStandardServiceProjectFetcher();
        when(requestBuilder.serviceProject(any(ServiceProjectBuilder.class))).thenReturn(requestBuilder);
        // act
        fetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder).serviceProject(any(ServiceProjectBuilder.class));
    }
}
