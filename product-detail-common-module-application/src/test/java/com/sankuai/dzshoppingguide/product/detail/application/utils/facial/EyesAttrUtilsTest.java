package com.sankuai.dzshoppingguide.product.detail.application.utils.facial;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Arrays;
import java.util.Collections;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.mockito.Mockito;

@ExtendWith(MockitoExtension.class)
public class EyesAttrUtilsTest {

    @Mock
    private ProductAttr productAttr;

    /**
     * Test when eyeglassFrame is null
     */
    @Test
    public void testBuildFrameBrand_WhenEyeglassFrameIsNull() {
        // arrange
        when(productAttr.getSkuAttrFirstValue("EyeglassFrame")).thenReturn(null);
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFrameBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test when eyeglassFrame is empty string
     */
    @Test
    public void testBuildFrameBrand_WhenEyeglassFrameIsEmpty() {
        // arrange
        when(productAttr.getSkuAttrFirstValue("EyeglassFrame")).thenReturn("");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFrameBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test when eyeglassFrame is "指定价格范围内任选" with valid availablePriceRange
     */
    @Test
    public void testBuildFrameBrand_WhenPriceRangeWithValidValue() {
        // arrange
        when(productAttr.getSkuAttrFirstValue("EyeglassFrame")).thenReturn("指定价格范围内任选");
        when(productAttr.getSkuAttrFirstValue("AvailablePriceRange")).thenReturn("1000");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFrameBrand(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("镜框品牌", result.get().getTitle());
        assertEquals("1000元以内任选", result.get().getContent());
    }

    /**
     * Test when eyeglassFrame is "指定价格范围内任选" but availablePriceRange is empty
     */
    @Test
    public void testBuildFrameBrand_WhenPriceRangeWithEmptyValue() {
        // arrange
        when(productAttr.getSkuAttrFirstValue("EyeglassFrame")).thenReturn("指定价格范围内任选");
        when(productAttr.getSkuAttrFirstValue("AvailablePriceRange")).thenReturn("");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFrameBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test when eyeglassFrame is "指定品牌" with valid brand name
     */
    @Test
    public void testBuildFrameBrand_WhenSpecificBrandWithValidName() {
        // arrange
        when(productAttr.getSkuAttrFirstValue("EyeglassFrame")).thenReturn("指定品牌");
        when(productAttr.getSkuAttrFirstValue("MirrorFrameBrandName")).thenReturn("雷朋");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFrameBrand(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("镜框品牌", result.get().getTitle());
        assertEquals("雷朋", result.get().getContent());
    }

    /**
     * Test when eyeglassFrame is "指定品牌" but brand name is empty
     */
    @Test
    public void testBuildFrameBrand_WhenSpecificBrandWithEmptyName() {
        // arrange
        when(productAttr.getSkuAttrFirstValue("EyeglassFrame")).thenReturn("指定品牌");
        when(productAttr.getSkuAttrFirstValue("MirrorFrameBrandName")).thenReturn("");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFrameBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test when eyeglassFrame has other invalid value
     */
    @Test
    public void testBuildFrameBrand_WhenOtherValue() {
        // arrange
        when(productAttr.getSkuAttrFirstValue("EyeglassFrame")).thenReturn("其他值");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFrameBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildFunctionWhenFunctionsIsEmpty() {
        // arrange
        when(productAttr.getSkuAttrValues("lens_function")).thenReturn(Collections.emptyList());
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFunction(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildFunctionWhenSingleFunction() {
        // arrange
        when(productAttr.getSkuAttrValues("lens_function")).thenReturn(Collections.singletonList("防蓝光"));
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFunction(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("功能", result.get().getTitle());
        assertEquals("防蓝光", result.get().getContent());
    }

    @Test
    public void testBuildFunctionWhenMultipleFunctions() {
        // arrange
        when(productAttr.getSkuAttrValues("lens_function")).thenReturn(Arrays.asList("防蓝光", "防紫外线", "抗疲劳"));
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildFunction(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("功能", result.get().getTitle());
        assertEquals("防蓝光、防紫外线、抗疲劳", result.get().getContent());
    }

    @Test
    public void testBuildFunctionWhenProductAttrIsNull() {
        // arrange
        ProductAttr nullProductAttr = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            EyesAttrUtils.buildFunction(nullProductAttr);
        });
    }

    @Test
    public void testBuildBrandWhenLensSelectionIsEmpty() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("LensSelection")).thenReturn(null);
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildBrandWhenLensPriceIsEmpty() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("LensSelection")).thenReturn("指定价格范围内任选");
        when(productAttr.getSkuAttrFirstValue("LensPrice")).thenReturn(null);
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildBrandWhenLensPriceHasValue() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("LensSelection")).thenReturn("指定价格范围内任选");
        when(productAttr.getSkuAttrFirstValue("LensPrice")).thenReturn("500");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildBrand(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("品牌", result.get().getTitle());
        assertEquals("500元以内任选", result.get().getContent());
    }

    @Test
    public void testBuildBrandWhenBrandsIsEmpty() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("LensSelection")).thenReturn("指定品牌");
        when(productAttr.getSkuAttrValues("lens_brand")).thenReturn(Collections.emptyList());
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildBrandWhenBrandsHasSingleValue() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("LensSelection")).thenReturn("指定品牌");
        when(productAttr.getSkuAttrValues("lens_brand")).thenReturn(Collections.singletonList("蔡司"));
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildBrand(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("品牌", result.get().getTitle());
        assertEquals("蔡司", result.get().getContent());
    }

    @Test
    public void testBuildBrandWhenBrandsHasMultipleValues() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("LensSelection")).thenReturn("指定品牌");
        when(productAttr.getSkuAttrValues("lens_brand")).thenReturn(Arrays.asList("蔡司", "依视路", "豪雅"));
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildBrand(productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("品牌", result.get().getTitle());
        assertEquals("蔡司、依视路、豪雅 3选1", result.get().getContent());
    }

    @Test
    public void testBuildBrandWhenLensSelectionIsOtherValue() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("LensSelection")).thenReturn("其他值");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildBrandWhenContentIsEmpty() throws Throwable {
        // arrange
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        when(productAttr.getSkuAttrFirstValue("LensSelection")).thenReturn("无效值");
        // act
        Optional<DealDetailStructuredDetailVO> result = EyesAttrUtils.buildBrand(productAttr);
        // assert
        assertFalse(result.isPresent());
    }


}
