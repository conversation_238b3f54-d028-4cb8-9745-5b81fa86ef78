package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.dot.PoiFieldsDTO;
import org.junit.Test;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/12 10:16
 */
public class LionConfigUtilsTest {
    private static final List<String> dpPoiFields = Lists.newArrayList("shopId", "shopType", "mainCategoryId", "cityId", "mainRegionId",
            "backMainCategoryPath");

    private static final List<String> mtPoiFields = Lists.newArrayList("dpPoiId", "mtPoiId");
    @Test
    public void test() {
        PoiFieldsDTO poiFieldsDTO = new PoiFieldsDTO(dpPoiFields, mtPoiFields);
        String s = JsonCodec.encodeWithUTF8(poiFieldsDTO);
        System.out.println("s = " + s);

        System.out.println("-------------------------------");
        
        PoiFieldsDTO decode = JsonCodec.decode(s, PoiFieldsDTO.class);
        System.out.println("decode = " + decode);

        System.out.println("-------------------------------");

        Map<String,List<String>> map = new HashMap<>();
        map.put("mt",mtPoiFields);
        map.put("dp",dpPoiFields);
        String s1 = JsonCodec.encodeWithUTF8(map);
        System.out.println("s1 = " + s1);
        Assert.notEmpty(map);
    }

}