package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.builder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MassageDealHighlightsModuleBuilderTest {

    /**
     * Test case when freeFood attribute is not in FOOD_NEW_DATA set
     * Should return null
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_WhenFreeFoodNotInFoodNewData_ThenReturnNull() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr = mock(ServiceProjectAttrDTO.class);
        when(attr.getAttrName()).thenReturn("freeFood");
        // Not in FOOD_NEW_DATA
        when(attr.getAttrValue()).thenReturn("regular_food");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr);
        // act
        String result = MassageDealHighlightsModuleBuilder.getNewFoodDataByServiceProjectAttr(attrs);
        // assert
        assertNull(result);
    }

    /**
     * Test case when freeFood is "茶点水果" and has Fruit attribute
     * Should return "茶点水果"
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_WhenTeaFruitWithFruit_ThenReturnTeaFruit() throws Throwable {
        // arrange
        ServiceProjectAttrDTO freeFoodAttr = mock(ServiceProjectAttrDTO.class);
        when(freeFoodAttr.getAttrName()).thenReturn("freeFood");
        when(freeFoodAttr.getAttrValue()).thenReturn("茶点水果");
        ServiceProjectAttrDTO fruitAttr = mock(ServiceProjectAttrDTO.class);
        when(fruitAttr.getAttrName()).thenReturn("Fruit");
        when(fruitAttr.getAttrValue()).thenReturn("有水果");
        List<ServiceProjectAttrDTO> attrs = Arrays.asList(freeFoodAttr, fruitAttr);
        // act
        String result = MassageDealHighlightsModuleBuilder.getNewFoodDataByServiceProjectAttr(attrs);
        // assert
        assertEquals("茶点水果", result);
    }

    /**
     * Test case when freeFood is "茶点水果" without Fruit attribute
     * Should return "茶点"
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_WhenTeaFruitWithoutFruit_ThenReturnTea() throws Throwable {
        // arrange
        ServiceProjectAttrDTO freeFoodAttr = mock(ServiceProjectAttrDTO.class);
        when(freeFoodAttr.getAttrName()).thenReturn("freeFood");
        when(freeFoodAttr.getAttrValue()).thenReturn("茶点水果");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(freeFoodAttr);
        // act
        String result = MassageDealHighlightsModuleBuilder.getNewFoodDataByServiceProjectAttr(attrs);
        // assert
        assertEquals("茶点", result);
    }

    /**
     * Test case when freeFood is "自助餐畅吃" (in FOOD_NEW_DATA)
     * Should return the same value
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_WhenBuffetAllYouCanEat_ThenReturnSameValue() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr = mock(ServiceProjectAttrDTO.class);
        when(attr.getAttrName()).thenReturn("freeFood");
        when(attr.getAttrValue()).thenReturn("自助餐畅吃");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr);
        // act
        String result = MassageDealHighlightsModuleBuilder.getNewFoodDataByServiceProjectAttr(attrs);
        // assert
        assertEquals("自助餐畅吃", result);
    }

    /**
     * Test case with empty input list
     * Should return null as no freeFood attribute is found
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_WhenEmptyList_ThenReturnNull() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = Collections.emptyList();
        // act
        String result = MassageDealHighlightsModuleBuilder.getNewFoodDataByServiceProjectAttr(attrs);
        // assert
        assertNull(result);
    }

    /**
     * Test case when input list is null
     * Expected to throw NullPointerException
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_WhenNullInput_ThenThrowNPE() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> MassageDealHighlightsModuleBuilder.getNewFoodDataByServiceProjectAttr(null));
    }

    /**
     * Test case when freeFood attribute exists but its value is empty string
     * Should return null
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_WhenFreeFoodValueIsEmpty_ThenReturnNull() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr = mock(ServiceProjectAttrDTO.class);
        when(attr.getAttrName()).thenReturn("freeFood");
        when(attr.getAttrValue()).thenReturn("");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr);
        // act
        String result = MassageDealHighlightsModuleBuilder.getNewFoodDataByServiceProjectAttr(attrs);
        // assert
        assertNull(result);
    }

    /**
     * Test case when freeFood attribute name is null
     * Should return null
     */
    @Test
    public void testGetNewFoodDataByServiceProjectAttr_WhenAttrNameIsNull_ThenReturnNull() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr = mock(ServiceProjectAttrDTO.class);
        when(attr.getAttrName()).thenReturn(null);
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr);
        // act
        String result = MassageDealHighlightsModuleBuilder.getNewFoodDataByServiceProjectAttr(attrs);
        // assert
        assertNull(result);
    }
}
