package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DealTimesCardReminderInfoBuilderTest {

    /**
     * 测试当输入ObjectGuaranteeTagDTO为null时返回false
     */
    @Test
    void testIsAnXinXueWhenInputIsNull() {
        // arrange
        DealTimesCardReminderInfoBuilder builder = new DealTimesCardReminderInfoBuilder();
        // act
        boolean result = builder.isAnXinXue(null);
        // assert
        assertFalse(result, "当输入为null时应返回false");
    }

    /**
     * 测试当tags列表为空时返回false
     */
    @Test
    void testIsAnXinXueWhenTagsListIsEmpty() {
        // arrange
        DealTimesCardReminderInfoBuilder builder = new DealTimesCardReminderInfoBuilder();
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        when(mockInput.getTags()).thenReturn(Collections.emptyList());
        // act
        boolean result = builder.isAnXinXue(mockInput);
        // assert
        assertFalse(result, "当tags列表为空时应返回false");
    }

    /**
     * 测试当tags列表包含ANXIN_LEARNING_GUARANTEE标签时返回true
     */
    @Test
    void testIsAnXinXueWhenContainsTargetTag() {
        // arrange
        DealTimesCardReminderInfoBuilder builder = new DealTimesCardReminderInfoBuilder();
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        TagDTO mockTag = mock(TagDTO.class);
        when(mockInput.getTags()).thenReturn(Collections.singletonList(mockTag));
        when(mockTag.getCode()).thenReturn(GuaranteeTagNameEnum.ANXIN_LEARNING_GUARANTEE.getCode());
        // act
        boolean result = builder.isAnXinXue(mockInput);
        // assert
        assertTrue(result, "当包含安心学标签时应返回true");
    }

    /**
     * 测试当tags列表不包含ANXIN_LEARNING_GUARANTEE标签时返回false
     */
    @Test
    void testIsAnXinXueWhenNotContainsTargetTag() {
        // arrange
        DealTimesCardReminderInfoBuilder builder = new DealTimesCardReminderInfoBuilder();
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        TagDTO mockTag = mock(TagDTO.class);
        when(mockInput.getTags()).thenReturn(Collections.singletonList(mockTag));
        when(mockTag.getCode()).thenReturn(GuaranteeTagNameEnum.ANXIN_MEDICAL_IMPLANT_GUARANTEE.getCode());
        // act
        boolean result = builder.isAnXinXue(mockInput);
        // assert
        assertFalse(result, "当不包含安心学标签时应返回false");
    }

    /**
     * 测试当tags列表包含null元素时能正确处理
     */
    @Test
    void testIsAnXinXueWhenTagsListContainsNull() {
        // arrange
        DealTimesCardReminderInfoBuilder builder = new DealTimesCardReminderInfoBuilder();
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        TagDTO mockTag = mock(TagDTO.class);
        when(mockInput.getTags()).thenReturn(Arrays.asList(null, mockTag, null));
        when(mockTag.getCode()).thenReturn(GuaranteeTagNameEnum.ANXIN_LEARNING_GUARANTEE.getCode());
        // act
        boolean result = builder.isAnXinXue(mockInput);
        // assert
        assertTrue(result, "当tags列表包含null元素但存在安心学标签时应返回true");
    }

    /**
     * 测试当tags列表包含多个标签且包含目标标签时返回true
     */
    @Test
    void testIsAnXinXueWhenMultipleTagsIncludingTarget() {
        // arrange
        DealTimesCardReminderInfoBuilder builder = new DealTimesCardReminderInfoBuilder();
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        TagDTO mockTag1 = mock(TagDTO.class);
        TagDTO mockTag2 = mock(TagDTO.class);
        TagDTO mockTag3 = mock(TagDTO.class);
        when(mockInput.getTags()).thenReturn(Arrays.asList(mockTag1, mockTag2, mockTag3));
        when(mockTag1.getCode()).thenReturn(GuaranteeTagNameEnum.ANXIN_MEDICAL_IMPLANT_GUARANTEE.getCode());
        when(mockTag2.getCode()).thenReturn(GuaranteeTagNameEnum.ANXIN_LEARNING_GUARANTEE.getCode());
        // act
        boolean result = builder.isAnXinXue(mockInput);
        // assert
        assertTrue(result, "当tags列表包含多个标签且包含安心学标签时应返回true");
    }

    /**
     * 测试当tags列表包含多个标签但不包含目标标签时返回false
     */
    @Test
    void testIsAnXinXueWhenMultipleTagsExcludingTarget() {
        // arrange
        DealTimesCardReminderInfoBuilder builder = new DealTimesCardReminderInfoBuilder();
        ObjectGuaranteeTagDTO mockInput = mock(ObjectGuaranteeTagDTO.class);
        TagDTO mockTag1 = mock(TagDTO.class);
        TagDTO mockTag2 = mock(TagDTO.class);
        TagDTO mockTag3 = mock(TagDTO.class);
        when(mockInput.getTags()).thenReturn(Arrays.asList(mockTag1, mockTag2, mockTag3));
        when(mockTag1.getCode()).thenReturn(GuaranteeTagNameEnum.ANXIN_MEDICAL_IMPLANT_GUARANTEE.getCode());
        when(mockTag2.getCode()).thenReturn(GuaranteeTagNameEnum.UNSATISFIED_REDO.getCode());
        when(mockTag3.getCode()).thenReturn(GuaranteeTagNameEnum.NO_ACTIVE_SOLICITATION.getCode());
        // act
        boolean result = builder.isAnXinXue(mockInput);
        // assert
        assertFalse(result, "当tags列表包含多个标签但不包含安心学标签时应返回false");
    }
}
