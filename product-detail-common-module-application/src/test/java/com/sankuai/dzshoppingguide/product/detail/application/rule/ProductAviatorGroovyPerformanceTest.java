package com.sankuai.dzshoppingguide.product.detail.application.rule;

import com.alibaba.fastjson.JSON;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Options;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.sankuai.dzshoppingguide.product.detail.application.utils.FileUtil;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import org.codehaus.groovy.control.CompilerConfiguration;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Aviator和Groovy在处理ProductAviatorExample3#test7相同功能时的性能对比测试
 */
public class ProductAviatorGroovyPerformanceTest extends RuleEngineBaseTest{

    // 创建独立的 Aviator 实例
    private static final AviatorEvaluatorInstance aviatorInstance = AviatorEvaluator.newInstance();

    // 创建 Groovy Shell
    private static final GroovyShell groovyShell = new GroovyShell(new CompilerConfiguration());

    static {
        // 设置Aviator选项
        aviatorInstance.setOption(Options.MAX_LOOP_COUNT, 10000);
        aviatorInstance.setOption(Options.OPTIMIZE_LEVEL, 0);

        // 注册自定义函数
        aviatorInstance.addFunction(new GetProductAttrJSONValueFunction());
        aviatorInstance.addFunction(new GetProductAttributeFunction());
    }

    @Test
    public void test() throws IOException {

        // 加载测试数据
        String s = FileUtil.file2str("product-massage2.json");
        String string = JSON.parseObject(s).getJSONObject("data").getString("list");
        DealGroupDTO dealGroupDTO = JSON.parseArray(string, DealGroupDTO.class).get(0);

        // 准备Aviator表达式
        String aviatorExpression =
                "let listValue = getProductAttrJSONValue(#product.standardServiceProject.mustGroups[0].serviceProjectItems[0].standardAttribute, 'serviceBodyRange');\n" +
                        "let bodyRegion = getProductAttribute(#product.standardServiceProject.mustGroups[0].serviceProjectItems[0].standardAttribute, 'bodyRegion');\n" +
                        "let result = '';\n" +
                        "let size = count(listValue);\n" +
                        "for i, v in listValue {\n" +
                        "  result = result + v;\n" +
                        "  if (i < size - 1) {\n" +
                        "    result = result + '、';\n" +
                        "  }\n" +
                        "}\n" +
                        "if (bodyRegion == '全身') {\n" +
                        "  return bodyRegion + '（' + result + '）';\n" +
                        "} else {\n" +
                        "  return result;\n" +
                        "}";

        // 加载Groovy脚本
        File groovyFile = FileUtil.getFile("ProductTest1.groovy");
        long groovyScriptParse = System.nanoTime();
        Script groovyScript = groovyShell.parse(groovyFile);
        long groovyScriptParseEnd = System.nanoTime();
        long groovyScriptParseTime = TimeUnit.NANOSECONDS.toMillis(groovyScriptParseEnd - groovyScriptParse);
        System.out.println("Groovy脚本预编译时间: " + groovyScriptParseTime + "ms");

        // 准备环境变量
        Map<String, Object> aviatorEnv = new HashMap<>();
        aviatorEnv.put("product", dealGroupDTO);

        // 准备Groovy绑定
        Binding binding = new Binding();
        binding.setVariable("product", dealGroupDTO);
        groovyScript.setBinding(binding);


        // 验证两种方法的结果是否一致
        Object aviatorResult = aviatorInstance.execute(aviatorExpression, aviatorEnv);
        Object groovyResult = groovyScript.run();// 执行脚本初始化

        System.out.println("Aviator结果: " + aviatorResult);
        System.out.println("Groovy结果: " + groovyResult);
        System.out.println("结果一致: " + aviatorResult.equals(groovyResult));
        System.out.println();

        // 预热
        System.out.println("开始预热...");
        for (int i = 0; i < 10; i++) {
            aviatorInstance.execute(aviatorExpression, aviatorEnv, true);
            groovyScript.run();// 执行脚本初始化
        }
        System.out.println("预热完成");
        System.out.println();
        long aviatorElapsedTime;
        long aviatorElapsedTimeCache;
        {
            // 测试Aviator性能
            System.out.println("开始测试Aviator性能[无缓存模式]...");
            long aviatorStartTime = System.nanoTime();
            for (int i = 0; i < 10000; i++) {
                aviatorInstance.execute(aviatorExpression, aviatorEnv);
            }
            long aviatorEndTime = System.nanoTime();
            aviatorElapsedTime = TimeUnit.NANOSECONDS.toMillis(aviatorEndTime - aviatorStartTime);
            System.out.println("Aviator执行10000次耗时[无缓存模式]: " + aviatorElapsedTime + "ms");

        }
        {
            // 测试Aviator性能
            System.out.println("开始测试Aviator性能[缓存模式]...");
            long aviatorStartTime = System.nanoTime();
            for (int i = 0; i < 10000; i++) {
                aviatorInstance.execute(aviatorExpression, aviatorEnv, true);
            }
            long aviatorEndTime = System.nanoTime();
            aviatorElapsedTimeCache = TimeUnit.NANOSECONDS.toMillis(aviatorEndTime - aviatorStartTime);
            System.out.println("Aviator执行10000次耗时[缓存模式]: " + aviatorElapsedTimeCache + "ms");

        }
        // 测试Groovy性能
        System.out.println("开始测试Groovy性能...");
        long groovyStartTime = System.nanoTime();
        for (int i = 0; i < 10000; i++) {
            groovyScript.run();// 执行脚本初始化
        }
        long groovyEndTime = System.nanoTime();
        long groovyElapsedTime = TimeUnit.NANOSECONDS.toMillis(groovyEndTime - groovyStartTime);
        System.out.println("Groovy执行10000次耗时: " + groovyElapsedTime + "ms");

        // 输出性能对比结果
        System.out.println();
        System.out.println("性能对比结果:");
        System.out.println("Aviator执行时间: " + aviatorElapsedTime + "ms");
        System.out.println("Aviator执行时间[缓存]: " + aviatorElapsedTimeCache + "ms");
        System.out.println("Groovy执行时间: " + groovyElapsedTime + "ms");
//        System.out.println("性能比例(Groovy/Aviator): " + (double) groovyElapsedTime / aviatorElapsedTime);
    }

    /**
     * 自定义函数：从属性列表中获取JSON值
     */
    static class GetProductAttrJSONValueFunction extends AbstractFunction {
        @Override
        public String getName() {
            return "getProductAttrJSONValue";
        }

        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            Object javaObject = FunctionUtils.getJavaObject(arg1, env);
            StandardAttributeDTO productAttributeModel = (StandardAttributeDTO) javaObject;
            String stringValue = FunctionUtils.getStringValue(arg2, env);
            if (productAttributeModel != null && productAttributeModel.getAttrs() != null) {
                for (StandardAttributeItemDTO attribute : productAttributeModel.getAttrs()) {
                    if (stringValue.equals(attribute.getAttrName()) && attribute.getAttrValues() != null && !attribute.getAttrValues().isEmpty()) {
                        StandardAttributeValueDTO standardAttributeValueDTO = attribute.getAttrValues().get(0);
                        if (standardAttributeValueDTO != null && standardAttributeValueDTO.getSimpleValues() != null && !standardAttributeValueDTO.getSimpleValues().isEmpty()) {
                            List<String> strings = JSON.parseArray(standardAttributeValueDTO.getSimpleValues().get(0), String.class);
                            return AviatorRuntimeJavaType.valueOf(strings);
                        }
                    }
                }
            }

            return AviatorRuntimeJavaType.valueOf(new String[0]);
        }
    }

    /**
     * 自定义函数：从属性列表中获取属性值
     */
    static class GetProductAttributeFunction extends AbstractFunction {
        @Override
        public String getName() {
            return "getProductAttribute";
        }

        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            Object javaObject = FunctionUtils.getJavaObject(arg1, env);
            StandardAttributeDTO productAttributeModel = (StandardAttributeDTO) javaObject;
            String stringValue = FunctionUtils.getStringValue(arg2, env);
            if (productAttributeModel != null && productAttributeModel.getAttrs() != null) {
                for (StandardAttributeItemDTO attribute : productAttributeModel.getAttrs()) {
                    if (stringValue.equals(attribute.getAttrName()) && attribute.getAttrValues() != null && !attribute.getAttrValues().isEmpty()) {
                        StandardAttributeValueDTO standardAttributeValueDTO = attribute.getAttrValues().get(0);
                        if (standardAttributeValueDTO != null && standardAttributeValueDTO.getSimpleValues() != null && !standardAttributeValueDTO.getSimpleValues().isEmpty()) {
                            return new AviatorString(standardAttributeValueDTO.getSimpleValues().get(0));
                        }
                    }
                }
            }

            return new AviatorString("");
        }
    }
}