package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.resource;

import static org.junit.Assert.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.ResourceDTO;
import java.util.Arrays;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupResourceBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import org.junit.*;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test cases for ProductResourceFetcher#mapResult method
 */
@RunWith(MockitoJUnitRunner.class)
public class ProductResourceFetcherTest {

    @InjectMocks
    private ProductResourceFetcher productResourceFetcher;

    @Mock
    private QueryByDealGroupIdRequestBuilder requestBuilder;

    /**
     * Test case: When input aggregateResult is null
     * Expected: Returns FetcherResponse.succeed(null)
     */
    @Test
    public void testMapResult_WhenAggregateResultIsNull() {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = null;
        // act
        FetcherResponse<ProductResourceInfo> result = productResourceFetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    /**
     * Test case: When aggregateResult.getReturnValue() returns null
     * Expected: Returns FetcherResponse.succeed(null)
     */
    @Test
    public void testMapResult_WhenReturnValueIsNull() {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(null);
        // act
        FetcherResponse<ProductResourceInfo> result = productResourceFetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    /**
     * Test case: When getDealGroupDTO() returns null
     * Expected: Returns FetcherResponse.succeed(null)
     */
    @Test
    public void testMapResult_WhenDealGroupDTOIsNull() {
        // arrange
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(null);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductResourceInfo> result = productResourceFetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    /**
     * Test case: When getResourceInfos() returns null
     * Expected: Returns FetcherResponse.succeed(null)
     */
    @Test
    public void testMapResult_WhenResourceInfosIsNull() {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setResourceInfos(null);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductResourceInfo> result = productResourceFetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    /**
     * Test case: Happy path with valid resource infos
     * Expected: Returns FetcherResponse with ProductResourceInfo containing the resource infos
     */
    @Test
    public void testMapResult_WithValidResourceInfos() {
        // arrange
        ResourceDTO resourceDTO1 = new ResourceDTO();
        resourceDTO1.setResourceId(1L);
        ResourceDTO resourceDTO2 = new ResourceDTO();
        resourceDTO2.setResourceId(2L);
        List<ResourceDTO> resourceDTOs = Arrays.asList(resourceDTO1, resourceDTO2);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setResourceInfos(resourceDTOs);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductResourceInfo> result = productResourceFetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertNotNull(result.getReturnValue().getResourceInfos());
        assertEquals(2, result.getReturnValue().getResourceInfos().size());
        assertEquals(resourceDTOs, result.getReturnValue().getResourceInfos());
    }

    @Test
    public void testFulfillRequest_NormalCase() throws Throwable {
        // arrange
        ProductResourceFetcher fetcher = new TestableProductResourceFetcher();
        // act
        fetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder, times(1)).dealGroupResource(any());
    }

    @Test(expected = NullPointerException.class)
    public void testFulfillRequest_NullRequestBuilder() throws Throwable {
        // arrange
        ProductResourceFetcher fetcher = new TestableProductResourceFetcher();
        // act
        fetcher.fulfillRequest(null);
        // Method should throw NullPointerException
    }

    private static class TestableProductResourceFetcher extends ProductResourceFetcher {

        @Override
        protected FetcherResponse<ProductResourceInfo> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
            // Not needed for these tests
            return null;
        }
    }
}
