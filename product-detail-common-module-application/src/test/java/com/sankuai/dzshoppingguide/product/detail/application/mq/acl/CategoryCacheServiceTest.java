package com.sankuai.dzshoppingguide.product.detail.application.mq.acl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CategoryCacheServiceTest {

    @Mock
    private RedisStoreClient storeClient;

    @InjectMocks
    private CategoryCacheService categoryCacheService;

    /**
     * 测试productId为null时返回false
     */
    @Test
    public void testCleanWhenProductIdIsNull() throws Throwable {
        // arrange
        IdTypeEnum idTypeEnum = IdTypeEnum.DP;
        Long productId = null;
        // act
        boolean result = categoryCacheService.clean(idTypeEnum, productId);
        // assert
        assertFalse(result);
        verifyZeroInteractions(storeClient);
    }

    /**
     * 测试productId为0时返回false
     */
    @Test
    public void testCleanWhenProductIdIsZero() throws Throwable {
        // arrange
        IdTypeEnum idTypeEnum = IdTypeEnum.DP;
        Long productId = 0L;
        // act
        boolean result = categoryCacheService.clean(idTypeEnum, productId);
        // assert
        assertFalse(result);
        verifyZeroInteractions(storeClient);
    }

    /**
     * 测试productId有效但删除失败的情况
     */
    @Test
    public void testCleanWhenDeleteFailed() throws Throwable {
        // arrange
        IdTypeEnum idTypeEnum = IdTypeEnum.MT;
        Long productId = 12345L;
        StoreKey expectedKey = new StoreKey("ProductCategory", idTypeEnum.getCode(), productId);
        when(storeClient.delete(expectedKey)).thenReturn(false);
        // act
        boolean result = categoryCacheService.clean(idTypeEnum, productId);
        // assert
        assertFalse(result);
        verify(storeClient).delete(expectedKey);
    }

    /**
     * 测试productId有效且删除成功的情况
     */
    @Test
    public void testCleanWhenDeleteSuccess() throws Throwable {
        // arrange
        IdTypeEnum idTypeEnum = IdTypeEnum.BIZ_PRODUCT;
        Long productId = 67890L;
        StoreKey expectedKey = new StoreKey("ProductCategory", idTypeEnum.getCode(), productId);
        when(storeClient.delete(expectedKey)).thenReturn(true);
        // act
        boolean result = categoryCacheService.clean(idTypeEnum, productId);
        // assert
        assertTrue(result);
        verify(storeClient).delete(expectedKey);
    }

    /**
     * 测试不同IdTypeEnum的情况
     */
    @Test
    public void testCleanWithDifferentIdTypeEnum() throws Throwable {
        // arrange
        IdTypeEnum idTypeEnum = IdTypeEnum.DP;
        Long productId = 11111L;
        StoreKey expectedKey = new StoreKey("ProductCategory", idTypeEnum.getCode(), productId);
        when(storeClient.delete(expectedKey)).thenReturn(true);
        // act
        boolean result = categoryCacheService.clean(idTypeEnum, productId);
        // assert
        assertTrue(result);
        verify(storeClient).delete(expectedKey);
    }
}
