package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import static org.mockito.ArgumentMatchers.*;
import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for AvailableTimeHelper.partialTimePeriodReminder method
 */
@ExtendWith(MockitoExtension.class)
class AvailableTimeHelperPartialTimePeriodReminderTest {

    @Mock
    private ProductBaseInfo baseInfo;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private ProductServiceProject serviceProject;

    @Mock
    private DealGroupRuleDTO rule;

    @Mock
    private DealGroupUseRuleDTO useRule;

    @Mock
    private DisableDateDTO disableDate;

    private FetcherResultDTO fetcherResults;

    /**
     * Test when no weekday information is provided
     */
    @Test
    public void testPartialTimePeriodReminderNoWeekdayInfo() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        when(productAttr.getSkuAttrValue("TimeRange3")).thenReturn(null);
        when(productAttr.getSkuAttrFirstValue("StartTimePoint")).thenReturn("09:00");
        when(productAttr.getSkuAttrFirstValue("EndTimePoint")).thenReturn("18:00");
        // act
        AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
        // assert
        assertEquals(1, contents.size());
        assertTrue(contents.get(0).getText().contains("可用"));
        assertFalse(contents.get(0).getText().contains("部分节假日除外"));
    }

    /**
     * Test with consecutive weekdays (3+ consecutive days)
     */
    @Test
    public void testPartialTimePeriodReminderWithConsecutiveDays() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        when(productAttr.getSkuAttrValue("TimeRange3")).thenReturn(Arrays.asList("1", "2", "3", "5"));
        when(productAttr.getSkuAttrFirstValue("StartTimePoint")).thenReturn("09:00");
        when(productAttr.getSkuAttrFirstValue("EndTimePoint")).thenReturn("18:00");
        // act
        AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
        // assert
        assertEquals(1, contents.size());
        assertTrue(contents.get(0).getText().contains("周一至周三"));
        assertTrue(contents.get(0).getText().contains("09:00-18:00"));
    }

    /**
     * Test with non-consecutive weekdays
     */
    @Test
    public void testPartialTimePeriodReminderWithNonConsecutiveDays() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        when(productAttr.getSkuAttrValue("TimeRange3")).thenReturn(Arrays.asList("1", "3", "5"));
        when(productAttr.getSkuAttrFirstValue("StartTimePoint")).thenReturn("09:00");
        when(productAttr.getSkuAttrFirstValue("EndTimePoint")).thenReturn("18:00");
        // act
        AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
        // assert
        assertEquals(1, contents.size());
        assertTrue(contents.get(0).getText().contains("周一、周三、周五"));
        assertTrue(contents.get(0).getText().contains("09:00-18:00"));
    }

    /**
     * Test with invalid time format
     */
    @Test
    public void testPartialTimePeriodReminderWithInvalidTimeFormat() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        when(productAttr.getSkuAttrValue("TimeRange3")).thenReturn(Arrays.asList("1", "2", "3"));
        when(productAttr.getSkuAttrFirstValue("StartTimePoint")).thenReturn("invalid");
        when(productAttr.getSkuAttrFirstValue("EndTimePoint")).thenReturn("invalid");
        // act
        AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
        // assert
        assertEquals(1, contents.size());
        assertTrue(contents.get(0).getText().contains("周一至周三"));
        assertFalse(contents.get(0).getText().contains("-"));
    }

    /**
     * Test with empty weekday list
     */
    @Test
    public void testPartialTimePeriodReminderWithEmptyWeekdayList() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        when(productAttr.getSkuAttrValue("TimeRange3")).thenReturn(Collections.emptyList());
        when(productAttr.getSkuAttrFirstValue("StartTimePoint")).thenReturn("09:00");
        when(productAttr.getSkuAttrFirstValue("EndTimePoint")).thenReturn("18:00");
        // act
        AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
        // assert
        assertEquals(1, contents.size());
        assertTrue(contents.get(0).getText().contains("09:00-18:00"));
        assertTrue(contents.get(0).getText().contains("周一至周日"));
    }

    /**
     * Test with next day time format (e.g., 27:00)
     */
    @Test
    public void testPartialTimePeriodReminderWithNextDayTime() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        ProductAttr productAttr = Mockito.mock(ProductAttr.class);
        ProductBaseInfo baseInfo = Mockito.mock(ProductBaseInfo.class);
        when(productAttr.getSkuAttrValue("TimeRange3")).thenReturn(Arrays.asList("1", "2", "3"));
        when(productAttr.getSkuAttrFirstValue("StartTimePoint")).thenReturn("22:00");
        when(productAttr.getSkuAttrFirstValue("EndTimePoint")).thenReturn("27:00");
        // act
        AvailableTimeHelper.partialTimePeriodReminder(contents, productAttr, baseInfo);
        // assert
        assertEquals(1, contents.size());
        assertTrue(contents.get(0).getText().contains("22:00-次日03:00"));
    }

    @BeforeEach
    void setUp() {
        fetcherResults = FetcherResultDTO.builder().productBaseInfo(baseInfo).productAttr(productAttr).productSecondCategoryId(1).productServiceProject(serviceProject).build();
    }

    @Test
    public void testGetAvailableTimeNoRestrictions() throws Throwable {
        // arrange
        when(baseInfo.getRule()).thenReturn(null);
        when(productAttr.getSkuAttrList()).thenReturn(Collections.emptyList());
        // act
        String result = AvailableTimeHelper.getAvailableTime(fetcherResults);
        // assert
        assertEquals("周一至周日全天可用", result);
    }

    @Test
    public void testGetAvailableTimeWithDisableDays() throws Throwable {
        // arrange
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        // Monday and Tuesday disabled
        when(disableDate.getDisableDays()).thenReturn(Arrays.asList(1, 2));
        when(productAttr.getSkuAttrList()).thenReturn(Collections.emptyList());
        // act
        String result = AvailableTimeHelper.getAvailableTime(fetcherResults);
        // assert
        assertTrue(result.contains("部分日期"));
        assertTrue(result.contains("全天可用"));
    }

    @Test
    public void testGetAvailableTimeWithTimeRestrictions() throws Throwable {
        // arrange
        List<AttrDTO> attrList = new ArrayList<>();
        AttrDTO timeAttr = new AttrDTO();
        timeAttr.setName("available_time");
        timeAttr.setValue(Collections.singletonList("09:00-18:00"));
        attrList.add(timeAttr);
        when(baseInfo.getRule()).thenReturn(null);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        // act
        String result = AvailableTimeHelper.getAvailableTime(fetcherResults);
        // assert
        assertTrue(result.contains("09:00-18:00"));
        assertTrue(result.endsWith("可用"));
    }

    @Test
    public void testGetAvailableTimeWithBothRestrictions() throws Throwable {
        // arrange
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        // Monday disabled
        when(disableDate.getDisableDays()).thenReturn(Collections.singletonList(1));
        List<AttrDTO> attrList = new ArrayList<>();
        AttrDTO timeAttr = new AttrDTO();
        timeAttr.setName("available_time");
        timeAttr.setValue(Collections.singletonList("09:00-18:00"));
        attrList.add(timeAttr);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        // act
        String result = AvailableTimeHelper.getAvailableTime(fetcherResults);
        // assert
        assertTrue(result.contains("部分日期"));
        assertTrue(result.contains("09:00-18:00"));
        assertTrue(result.endsWith("可用"));
    }

    @Test
    public void testGetAvailableTimeException() throws Throwable {
        // arrange
        when(baseInfo.getRule()).thenThrow(new RuntimeException("Test exception"));
        // act
        String result = AvailableTimeHelper.getAvailableTime(fetcherResults);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetAvailableTimeNullInput() throws Throwable {
        // act
        String result = AvailableTimeHelper.getAvailableTime(null);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetAvailableTimeInvalidTimeFormat() throws Throwable {
        // arrange
        List<AttrDTO> attrList = new ArrayList<>();
        AttrDTO timeAttr = new AttrDTO();
        timeAttr.setName("available_time");
        timeAttr.setValue(Collections.singletonList("invalid-time-format"));
        attrList.add(timeAttr);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        when(baseInfo.getRule()).thenReturn(null);
        // act
        String result = AvailableTimeHelper.getAvailableTime(fetcherResults);
        // assert
        assertTrue(result.contains("部分时段"));
        assertTrue(result.endsWith("可用"));
    }

    @Test
    public void testGetAvailableTimeMultipleTimePeriods() throws Throwable {
        // arrange
        List<AttrDTO> attrList = new ArrayList<>();
        AttrDTO timeAttr = new AttrDTO();
        timeAttr.setName("available_time");
        timeAttr.setValue(Arrays.asList("09:00-12:00", "14:00-18:00"));
        attrList.add(timeAttr);
        when(productAttr.getSkuAttrList()).thenReturn(attrList);
        when(baseInfo.getRule()).thenReturn(null);
        // act
        String result = AvailableTimeHelper.getAvailableTime(fetcherResults);
        // assert
        assertTrue(result.contains("部分时段"));
        assertTrue(result.endsWith("可用"));
    }
}
