package com.sankuai.dzshoppingguide.product.detail.application.builder.detailimage;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.ProductOriginDetail;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.ProductOriginDetailFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.ContentItem;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.ProductIntroductionDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.origindetail.dto.RichTextContent;
import com.sankuai.dzshoppingguide.product.detail.spi.detailimage.ImageTextDetailContent;
import com.sankuai.dzshoppingguide.product.detail.spi.detailimage.ImageTextDetailVO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DealImageTextDetailModuleBuilderTest {

    @Spy
    private TestDealImageTextDetailModuleBuilder builder;

    static class TestDealImageTextDetailModuleBuilder extends DealImageTextDetailModuleBuilder {

        private ProductOriginDetail mockProductOriginDetail;

        public void setMockProductOriginDetail(ProductOriginDetail detail) {
            this.mockProductOriginDetail = detail;
        }

        @Override
        protected ProductOriginDetail getDependencyResult(Class dependencyFetcherClass) {
            return mockProductOriginDetail;
        }
    }

    /**
     * 测试边界场景 - productIntroduction为null
     */
    @Test
    public void testDoBuildWithNullProductIntroduction() throws Throwable {
        // arrange
        ProductOriginDetail mockDetail = mock(ProductOriginDetail.class);
        when(mockDetail.getProductIntroduction()).thenReturn(null);
        builder.setMockProductOriginDetail(mockDetail);
        // act
        ImageTextDetailVO result = builder.doBuild();
        // assert
        assertNull(result);
    }

    /**
     * 测试边界场景 - productIntroduction.content为空列表
     */
    @Test
    public void testDoBuildWithEmptyContentList() throws Throwable {
        // arrange
        ProductOriginDetail mockDetail = mock(ProductOriginDetail.class);
        ProductIntroductionDTO mockIntro = new ProductIntroductionDTO();
        mockIntro.setContent(Collections.emptyList());
        when(mockDetail.getProductIntroduction()).thenReturn(mockIntro);
        builder.setMockProductOriginDetail(mockDetail);
        // act
        ImageTextDetailVO result = builder.doBuild();
        // assert
        assertNull(result);
    }

    /**
     * 测试异常场景 - 依赖结果获取失败
     */
    @Test
    public void testDoBuildWhenDependencyThrowsException() throws Throwable {
        // arrange
        builder.setMockProductOriginDetail(null);
        doThrow(new RuntimeException("Dependency error")).when(builder).getDependencyResult(any());
        // act & assert
        assertThrows(RuntimeException.class, () -> builder.doBuild());
    }
}
