package com.sankuai.dzshoppingguide.product.detail.application.builder.headpic;

import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.HeadPicModuleVO;
import com.sankuai.general.product.query.center.client.dto.video.DealGroupVideoDTO;
import com.sankuai.general.product.query.center.client.dto.video.ExtendVideoDTO;
import com.sankuai.general.product.query.center.client.dto.video.SpriteImageDTO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class HeadPicModuleBuilderAssembleOriginVideoSpritePicTest {

    @InjectMocks
    private HeadPicModuleBuilder builder;

    /**
     * Test when DealGroupVideoDTO is null
     */
    @Test
    public void testAssembleOriginVideoSpritePic_NullDealGroupVideoDTO() throws Throwable {
        // arrange
        HeadPicModuleVO video = mock(HeadPicModuleVO.class);
        // act
        builder.assembleOriginVideoSpritePic(null, video);
        // assert
        verify(video, never()).setSpritePic(any());
    }

    /**
     * Test when HeadPicModuleVO is null
     */
    @Test
    public void testAssembleOriginVideoSpritePic_NullHeadPicModuleVO() throws Throwable {
        // arrange
        DealGroupVideoDTO dealGroupVideoDTO = mock(DealGroupVideoDTO.class);
        when(dealGroupVideoDTO.getExtendVideos()).thenReturn(Collections.emptyList());
        // act
        builder.assembleOriginVideoSpritePic(dealGroupVideoDTO, null);
        // assert
        // No need to verify getExtendVideos() since it might be called due to implementation
        verifyNoMoreInteractions(dealGroupVideoDTO);
    }

    /**
     * Test when extendVideos list is empty
     */
    @Test
    public void testAssembleOriginVideoSpritePic_EmptyExtendVideos() throws Throwable {
        // arrange
        DealGroupVideoDTO dealGroupVideoDTO = mock(DealGroupVideoDTO.class);
        when(dealGroupVideoDTO.getExtendVideos()).thenReturn(Collections.emptyList());
        HeadPicModuleVO video = mock(HeadPicModuleVO.class);
        // act
        builder.assembleOriginVideoSpritePic(dealGroupVideoDTO, video);
        // assert
        verify(video, never()).setSpritePic(any());
    }

    /**
     * Test when extendVideos has items but spriteImageList is empty
     */
    @Test
    public void testAssembleOriginVideoSpritePic_EmptySpriteImageList() throws Throwable {
        // arrange
        DealGroupVideoDTO dealGroupVideoDTO = mock(DealGroupVideoDTO.class);
        ExtendVideoDTO extendVideoDTO = mock(ExtendVideoDTO.class);
        when(extendVideoDTO.getSpriteImageList()).thenReturn(Collections.emptyList());
        when(dealGroupVideoDTO.getExtendVideos()).thenReturn(Collections.singletonList(extendVideoDTO));
        HeadPicModuleVO video = mock(HeadPicModuleVO.class);
        // act
        builder.assembleOriginVideoSpritePic(dealGroupVideoDTO, video);
        // assert
        verify(video, never()).setSpritePic(any());
    }

    /**
     * Test valid case with one extendVideo having spriteImageList
     */
    @Test
    public void testAssembleOriginVideoSpritePic_ValidSpriteImage() throws Throwable {
        // arrange
        DealGroupVideoDTO dealGroupVideoDTO = mock(DealGroupVideoDTO.class);
        ExtendVideoDTO extendVideoDTO = mock(ExtendVideoDTO.class);
        SpriteImageDTO spriteImageDTO = mock(SpriteImageDTO.class);
        when(spriteImageDTO.getPath()).thenReturn("test/path");
        when(spriteImageDTO.getSubImageCount()).thenReturn(100);
        when(spriteImageDTO.getWidth()).thenReturn(1000);
        when(spriteImageDTO.getHeight()).thenReturn(1000);
        when(extendVideoDTO.getSpriteImageList()).thenReturn(Collections.singletonList(spriteImageDTO));
        when(dealGroupVideoDTO.getExtendVideos()).thenReturn(Collections.singletonList(extendVideoDTO));
        HeadPicModuleVO video = mock(HeadPicModuleVO.class);
        // act
        builder.assembleOriginVideoSpritePic(dealGroupVideoDTO, video);
        // assert
        verify(video).setSpritePic(any());
        verify(spriteImageDTO).getPath();
        verify(spriteImageDTO).getSubImageCount();
    }
}
