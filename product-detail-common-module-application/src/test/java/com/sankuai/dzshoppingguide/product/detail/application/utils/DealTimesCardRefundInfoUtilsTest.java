package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnPurchaseNoteDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnStandardDisplayItemDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PnStandardDisplayValueDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct.PurchaseNoteModuleDTO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

/**
 * Test class for DealTimesCardRefundInfoUtils
 */
class DealTimesCardRefundInfoUtilsTest {

    /**
     * Test case when both purchaseNoteDTO and ctx are null
     */
    @Test
    public void testHandleDealTimesCardRefundDetailNullInputs() throws Throwable {
        // arrange
        PnPurchaseNoteDTO purchaseNoteDTO = null;
        DealCtx ctx = null;
        // act & assert
        // Verify no NPE occurs and method returns early
        assertDoesNotThrow(() -> DealTimesCardRefundInfoUtils.handleDealTimesCardRefundDetail(purchaseNoteDTO, ctx));
    }

    /**
     * Test case when ctx is not a deal times card
     */
    @Test
    public void testHandleDealTimesCardRefundDetailNotDealTimesCard() throws Throwable {
        // arrange
        PnPurchaseNoteDTO purchaseNoteDTO = mock(PnPurchaseNoteDTO.class);
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isDealTimesCard()).thenReturn(false);
        // act
        DealTimesCardRefundInfoUtils.handleDealTimesCardRefundDetail(purchaseNoteDTO, ctx);
        // assert
        verify(ctx).isDealTimesCard();
        verifyNoMoreInteractions(purchaseNoteDTO, ctx);
    }

    /**
     * Test case for white list customer
     */
    @Test
    public void testHandleDealTimesCardRefundDetailWhiteListCustomer() throws Throwable {
        // arrange
        PnPurchaseNoteDTO purchaseNoteDTO = mock(PnPurchaseNoteDTO.class);
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isDealTimesCard()).thenReturn(true);
        when(ctx.isMiniProgram()).thenReturn(false);
        PurchaseNoteModuleDTO module = mock(PurchaseNoteModuleDTO.class);
        PnStandardDisplayItemDTO item = mock(PnStandardDisplayItemDTO.class);
        PnStandardDisplayValueDTO value = new PnStandardDisplayValueDTO(1, "有效期内可申请全额退款");
        when(purchaseNoteDTO.getPnModules()).thenReturn(Collections.singletonList(module));
        when(module.getPnItems()).thenReturn(Collections.singletonList(item));
        when(item.getPnItemValues()).thenReturn(Collections.singletonList(value));
        // act
        DealTimesCardRefundInfoUtils.handleDealTimesCardRefundDetail(purchaseNoteDTO, ctx);
        // assert
        verify(ctx).isDealTimesCard();
        verify(ctx).isMiniProgram();
        verifyNoMoreInteractions(ctx);
    }

    /**
     * Test case for mini program customer
     */
    @Test
    public void testHandleDealTimesCardRefundDetailMiniProgramCustomer() throws Throwable {
        // arrange
        PnPurchaseNoteDTO purchaseNoteDTO = mock(PnPurchaseNoteDTO.class);
        PurchaseNoteModuleDTO module = mock(PurchaseNoteModuleDTO.class);
        PnStandardDisplayItemDTO item = mock(PnStandardDisplayItemDTO.class);
        PnStandardDisplayValueDTO value = new PnStandardDisplayValueDTO(1, "有效期内可申请全额退款");
        when(purchaseNoteDTO.getPnModules()).thenReturn(Collections.singletonList(module));
        when(module.getPnItems()).thenReturn(Collections.singletonList(item));
        when(item.getPnItemValues()).thenReturn(Collections.singletonList(value));
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isDealTimesCard()).thenReturn(true);
        when(ctx.isMiniProgram()).thenReturn(true);
        when(ctx.isHasPurchaseNoteTable()).thenReturn(true);
        // act
        DealTimesCardRefundInfoUtils.handleDealTimesCardRefundDetail(purchaseNoteDTO, ctx);
        // assert
        verify(ctx).isDealTimesCard();
        verify(ctx).isMiniProgram();
        verify(ctx).isHasPurchaseNoteTable();
        verify(purchaseNoteDTO, atLeastOnce()).getPnModules();
    }

    /**
     * Test case when customer is not in white list and not mini program
     */
    @Test
    public void testHandleDealTimesCardRefundDetailRegularCustomer() throws Throwable {
        // arrange
        PnPurchaseNoteDTO purchaseNoteDTO = mock(PnPurchaseNoteDTO.class);
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isDealTimesCard()).thenReturn(true);
        when(ctx.isMiniProgram()).thenReturn(false);
        // act
        DealTimesCardRefundInfoUtils.handleDealTimesCardRefundDetail(purchaseNoteDTO, ctx);
        // assert
        verify(ctx).isDealTimesCard();
        verify(ctx).isMiniProgram();
        verifyNoMoreInteractions(ctx);
        verifyZeroInteractions(purchaseNoteDTO);
    }

    /**
     * Test case when purchase note table is not available
     */
    @Test
    public void testHandleDealTimesCardRefundDetailNoPurchaseNoteTable() throws Throwable {
        // arrange
        PnPurchaseNoteDTO purchaseNoteDTO = mock(PnPurchaseNoteDTO.class);
        DealCtx ctx = mock(DealCtx.class);
        when(ctx.isDealTimesCard()).thenReturn(true);
        when(ctx.isMiniProgram()).thenReturn(true);
        when(ctx.isHasPurchaseNoteTable()).thenReturn(false);
        // act
        DealTimesCardRefundInfoUtils.handleDealTimesCardRefundDetail(purchaseNoteDTO, ctx);
        // assert
        verify(ctx).isDealTimesCard();
        verify(ctx).isMiniProgram();
        verify(ctx).isHasPurchaseNoteTable();
        verifyNoMoreInteractions(ctx);
        verifyZeroInteractions(purchaseNoteDTO);
    }
}
