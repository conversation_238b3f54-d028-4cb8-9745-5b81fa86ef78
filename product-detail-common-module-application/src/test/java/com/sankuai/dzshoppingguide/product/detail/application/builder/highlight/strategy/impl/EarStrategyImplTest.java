package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class EarStrategyImplTest {

    private EarStrategyImpl earStrategy;

    private List<ServiceProjectAttrDTO> serviceProjectAttrs;

    @BeforeEach
    void setUp() {
        earStrategy = new EarStrategyImpl();
        serviceProjectAttrs = new ArrayList<>();
    }

    /**
     * Test when input list is empty
     * Should return null
     */
    @Test
    public void testGetToolValue_WhenInputListIsEmpty() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> emptyList = new ArrayList<>();
        // act
        String result = earStrategy.getToolValue(emptyList);
        // assert
        assertNull(result);
    }

    /**
     * Test when disposable material contains ONE_TIME_TOOL
     * Should return ONE_TIME_TOOL as highest priority
     */
    @Test
    public void testGetToolValue_WhenDisposableMaterialContainsOneTimeTool() throws Throwable {
        // arrange
        ServiceProjectAttrDTO dto = mock(ServiceProjectAttrDTO.class);
        when(dto.getAttrName()).thenReturn("disposableMaterial");
        when(dto.getAttrValue()).thenReturn("一次性工具、其他工具");
        serviceProjectAttrs.add(dto);
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("一次性工具", result);
    }

    /**
     * Test when earpicking tool exists but no one time tool
     * Should return SPECIAL_ERA_TOOL as second priority
     */
    @Test
    public void testGetToolValue_WhenEarpickingToolExists() throws Throwable {
        // arrange
        ServiceProjectAttrDTO dto = mock(ServiceProjectAttrDTO.class);
        when(dto.getAttrName()).thenReturn("earpickingTool");
        when(dto.getAttrValue()).thenReturn("特色采耳工具");
        serviceProjectAttrs.add(dto);
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("特色采耳工具", result);
    }

    /**
     * Test when hotpack tool exists but higher priority tools are missing
     * Should return hotpack tool as third priority
     */
    @Test
    public void testGetToolValue_WhenHotpackToolExists() throws Throwable {
        // arrange
        ServiceProjectAttrDTO dto = mock(ServiceProjectAttrDTO.class);
        when(dto.getAttrName()).thenReturn("hotpackTool");
        when(dto.getAttrValue()).thenReturn("热敷工具");
        serviceProjectAttrs.add(dto);
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("热敷工具", result);
    }

    /**
     * Test when massage tool exists but higher priority tools are missing
     * Should return massage tool as fourth priority
     */
    @Test
    public void testGetToolValue_WhenMassageToolExists() throws Throwable {
        // arrange
        ServiceProjectAttrDTO dto = mock(ServiceProjectAttrDTO.class);
        when(dto.getAttrName()).thenReturn("massageTool");
        when(dto.getAttrValue()).thenReturn("按摩工具");
        serviceProjectAttrs.add(dto);
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("按摩工具", result);
    }

    /**
     * Test when mixed tools exist and contains priority items
     * Should return highest priority item from DISPOSABLE_PRIORITY_LIST
     */
    @Test
    public void testGetToolValue_WhenMixedToolsWithPriorityItems() throws Throwable {
        // arrange
        ServiceProjectAttrDTO disposableDto = mock(ServiceProjectAttrDTO.class);
        when(disposableDto.getAttrName()).thenReturn("disposableMaterial");
        when(disposableDto.getAttrValue()).thenReturn("耳内镜、一次性床单");
        serviceProjectAttrs.add(disposableDto);
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("耳内镜", result);
    }

    /**
     * Test when mixed tools contain multiple items with different priorities
     * Should return the highest priority item according to DISPOSABLE_PRIORITY_LIST
     */
    @Test
    public void testGetToolValue_WhenMixedToolsWithMultiplePriorityItems() throws Throwable {
        // arrange
        ServiceProjectAttrDTO disposableDto = mock(ServiceProjectAttrDTO.class);
        when(disposableDto.getAttrName()).thenReturn("disposableMaterial");
        when(disposableDto.getAttrValue()).thenReturn("电动按摩床、耳内镜、一次性床单");
        serviceProjectAttrs.add(disposableDto);
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("耳内镜", result);
    }

    /**
     * Test when mixed tools are empty after removing ONE_TIME_TOOL
     * Should return null
     */
    @Test
    public void testGetToolValue_WhenMixedToolsEmptyAfterProcessing() throws Throwable {
        // arrange
        ServiceProjectAttrDTO disposableDto = mock(ServiceProjectAttrDTO.class);
        when(disposableDto.getAttrName()).thenReturn("unclassifiedTools");
        when(disposableDto.getAttrValue()).thenReturn("一次性工具");
        serviceProjectAttrs.add(disposableDto);
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertNull(result);
    }

    /**
     * Test when mixed tools contain items from both disposable and unclassified
     * Should return highest priority item from combined list
     */
    @Test
    public void testGetToolValue_WhenMixedToolsFromBothSources() throws Throwable {
        // arrange
        ServiceProjectAttrDTO disposableDto = mock(ServiceProjectAttrDTO.class);
        when(disposableDto.getAttrName()).thenReturn("disposableMaterial");
        when(disposableDto.getAttrValue()).thenReturn("一次性床单、香薰");
        ServiceProjectAttrDTO unclassifiedDto = mock(ServiceProjectAttrDTO.class);
        when(unclassifiedDto.getAttrName()).thenReturn("unclassifiedTools");
        when(unclassifiedDto.getAttrValue()).thenReturn("耳内镜、眼罩");
        serviceProjectAttrs.add(disposableDto);
        serviceProjectAttrs.add(unclassifiedDto);
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("耳内镜", result);
    }

    /**
     * Test when mixed tools contain non-priority items
     * Should return the first item from the list
     */
    @Test
    public void testGetToolValue_WhenMixedToolsContainNonPriorityItems() throws Throwable {
        // arrange
        ServiceProjectAttrDTO disposableDto = mock(ServiceProjectAttrDTO.class);
        when(disposableDto.getAttrName()).thenReturn("disposableMaterial");
        when(disposableDto.getAttrValue()).thenReturn("非优先工具A、非优先工具B");
        serviceProjectAttrs.add(disposableDto);
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("非优先工具A", result);
    }

    /**
     * Test when all higher priority tools exist
     * Should return ONE_TIME_TOOL as highest priority
     */
    @Test
    public void testGetToolValue_WhenAllToolsExist() throws Throwable {
        // arrange
        ServiceProjectAttrDTO disposableDto = mock(ServiceProjectAttrDTO.class);
        when(disposableDto.getAttrName()).thenReturn("disposableMaterial");
        when(disposableDto.getAttrValue()).thenReturn("一次性工具、耳内镜");
        ServiceProjectAttrDTO earPickingDto = mock(ServiceProjectAttrDTO.class);
        when(earPickingDto.getAttrName()).thenReturn("earpickingTool");
        when(earPickingDto.getAttrValue()).thenReturn("特色采耳工具");
        ServiceProjectAttrDTO hotpackDto = mock(ServiceProjectAttrDTO.class);
        when(hotpackDto.getAttrName()).thenReturn("hotpackTool");
        when(hotpackDto.getAttrValue()).thenReturn("热敷工具");
        ServiceProjectAttrDTO massageDto = mock(ServiceProjectAttrDTO.class);
        when(massageDto.getAttrName()).thenReturn("massageTool");
        when(massageDto.getAttrValue()).thenReturn("按摩工具");
        serviceProjectAttrs.addAll(Lists.newArrayList(disposableDto, earPickingDto, hotpackDto, massageDto));
        // act
        String result = earStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("一次性工具", result);
    }
}
