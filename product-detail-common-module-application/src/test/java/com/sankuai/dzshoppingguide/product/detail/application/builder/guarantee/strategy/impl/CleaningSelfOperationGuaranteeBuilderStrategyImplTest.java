package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.strategy.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.GuaranteeParam;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.dtos.ProductGuaranteeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.enums.LEInsuranceAgreementEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;
import com.sankuai.zdc.tag.apply.dto.DisplayTagDto;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CleaningSelfOperationGuaranteeBuilderStrategyImplTest {

    @Spy
    private CleaningSelfOperationGuaranteeBuilderStrategyImpl strategy;

    @Mock
    private GuaranteeParam mockParam;

    /**
     * Test when param is null
     */
    @Test
    public void testBuildWhenParamIsNull() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> strategy.build(null));
    }

    /**
     * Test when shopDisplayTagList is null
     */
    @Test
    public void testBuildWhenShopDisplayTagListIsNull() throws Throwable {
        // arrange
        when(mockParam.getShopDisplayTagList()).thenReturn(null);
        doReturn(Collections.emptyList()).when(strategy).buildContents(any());
        // act
        ProductGuaranteeDTO result = strategy.build(mockParam);
        // assert
        assertNull(result);
        verify(mockParam).getShopDisplayTagList();
    }

    /**
     * Test when LEInsuranceAgreementEnum is CLEANING_SELF_OWN_PRODUCT
     */
    @Test
    public void testBuildWhenLEInsuranceAgreementIsCleaningSelfOwnProduct() throws Throwable {
        // arrange
        DisplayTagDto tag = new DisplayTagDto();
        tag.setTagId(LEInsuranceAgreementEnum.CLEANING_SELF_OWN_PRODUCT.getTagId());
        when(mockParam.getShopDisplayTagList()).thenReturn(Collections.singletonList(tag));
        doReturn(Collections.emptyList()).when(strategy).buildContents(any());
        // act
        ProductGuaranteeDTO result = strategy.build(mockParam);
        // assert
        assertNull(result);
        verify(mockParam).getShopDisplayTagList();
        verify(strategy).buildContents(mockParam);
    }

    /**
     * Test when LEInsuranceAgreementEnum is other valid value
     */
    @Test
    public void testBuildWhenLEInsuranceAgreementIsOtherValue() throws Throwable {
        // arrange
        DisplayTagDto tag = new DisplayTagDto();
        tag.setTagId(LEInsuranceAgreementEnum.SAFE_WASHING.getTagId());
        when(mockParam.getShopDisplayTagList()).thenReturn(Collections.singletonList(tag));
        doReturn(Collections.emptyList()).when(strategy).buildContents(any());
        // act
        ProductGuaranteeDTO result = strategy.build(mockParam);
        // assert
        assertNull(result);
        verify(mockParam).getShopDisplayTagList();
        verify(strategy).buildContents(mockParam);
    }

    /**
     * Test when LEInsuranceAgreementEnum is null
     */
    @Test
    public void testBuildWhenLEInsuranceAgreementIsNull() throws Throwable {
        // arrange
        DisplayTagDto tag = new DisplayTagDto();
        // non-matching tag
        tag.setTagId(9999L);
        when(mockParam.getShopDisplayTagList()).thenReturn(Collections.singletonList(tag));
        doReturn(Collections.emptyList()).when(strategy).buildContents(any());
        // act
        ProductGuaranteeDTO result = strategy.build(mockParam);
        // assert
        assertNull(result);
        verify(mockParam).getShopDisplayTagList();
        verify(strategy).buildContents(mockParam);
    }

    /**
     * Test when buildContents returns empty list
     */
    @Test
    public void testBuildWhenBuildContentsReturnsEmpty() throws Throwable {
        // arrange
        when(mockParam.getShopDisplayTagList()).thenReturn(Collections.emptyList());
        doReturn(Collections.emptyList()).when(strategy).buildContents(any());
        // act
        ProductGuaranteeDTO result = strategy.build(mockParam);
        // assert
        assertNull(result);
        verify(mockParam).getShopDisplayTagList();
        verify(strategy).buildContents(mockParam);
    }

    /**
     * Test when buildContents returns non-empty list
     */
    @Test
    public void testBuildWhenBuildContentsReturnsNonEmpty() throws Throwable {
        // arrange
        when(mockParam.getShopDisplayTagList()).thenReturn(Collections.emptyList());
        List<GuaranteeInstructionsContentVO> contents = Arrays.asList(new GuaranteeInstructionsContentVO("Test Content"));
        doReturn(contents).when(strategy).buildContents(any());
        // act
        ProductGuaranteeDTO result = strategy.build(mockParam);
        // assert
        assertNull(result);
        verify(mockParam).getShopDisplayTagList();
        verify(strategy).buildContents(mockParam);
    }

    /**
     * Test when both LEInsuranceAgreement and buildContents return contents
     */
    @Test
    public void testBuildWhenBothHaveContents() throws Throwable {
        // arrange
        DisplayTagDto tag = new DisplayTagDto();
        tag.setTagId(LEInsuranceAgreementEnum.SAFE_WASHING.getTagId());
        when(mockParam.getShopDisplayTagList()).thenReturn(Collections.singletonList(tag));
        List<GuaranteeInstructionsContentVO> contents = Arrays.asList(new GuaranteeInstructionsContentVO("Test Content"));
        doReturn(contents).when(strategy).buildContents(any());
        // act
        ProductGuaranteeDTO result = strategy.build(mockParam);
        // assert
        assertNull(result);
        verify(mockParam).getShopDisplayTagList();
        verify(strategy).buildContents(mockParam);
    }
}
