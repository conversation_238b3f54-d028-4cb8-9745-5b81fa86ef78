package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Calendar;

/**
 * 用于测试的Calendar持有类
 */
class TestCalendarHolder {

    private static Calendar testCalendar;

    public static void setTestCalendar(Calendar calendar) {
        testCalendar = calendar;
    }

    public static Calendar getInstance() {
        return testCalendar != null ? testCalendar : Calendar.getInstance();
    }

    public static void resetCalendar() {
        testCalendar = null;
    }
}
