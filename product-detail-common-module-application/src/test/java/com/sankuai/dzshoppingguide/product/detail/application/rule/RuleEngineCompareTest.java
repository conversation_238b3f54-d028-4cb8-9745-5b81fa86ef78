package com.sankuai.dzshoppingguide.product.detail.application.rule;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.page.low.code.engine.factory.AviatorRuleEngine;
import com.sankuai.dz.product.detail.page.low.code.engine.factory.GroovyRuleEngine;
import com.sankuai.dzshoppingguide.product.detail.application.utils.FileUtil;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

public class RuleEngineCompareTest extends RuleEngineBaseTest {

    @Test
    public void test() throws Exception {

        // 加载测试数据
        String s = FileUtil.file2str("product-massage2.json");
        String string = JSON.parseObject(s).getJSONObject("data").getString("list");
        DealGroupDTO dealGroupDTO = JSON.parseArray(string, DealGroupDTO.class).get(0);

        // 准备Aviator表达式
        String aviatorExpression =
                "let listValue = getProductAttrJSONValue(#product.standardServiceProject.mustGroups[0].serviceProjectItems[0].standardAttribute, 'serviceBodyRange');\n" +
                        "let bodyRegion = getProductAttribute(#product.standardServiceProject.mustGroups[0].serviceProjectItems[0].standardAttribute, 'bodyRegion');\n" +
                        "let result = '';\n" +
                        "let size = count(listValue);\n" +
                        "for i, v in listValue {\n" +
                        "  result = result + v;\n" +
                        "  if (i < size - 1) {\n" +
                        "    result = result + '、';\n" +
                        "  }\n" +
                        "}\n" +
                        "if (bodyRegion == '全身') {\n" +
                        "  return bodyRegion + '（' + result + '）';\n" +
                        "} else {\n" +
                        "  return result;\n" +
                        "}";

        // 加载Groovy脚本
        String groovyFileContext = FileUtils.readFileToString(FileUtil.getFile("ProductTest1.groovy"), "UTF-8");

        // 准备环境变量
        Map<String, Object> env = new HashMap<>();
        env.put("product", dealGroupDTO);

        // 注册自定义函数
        AviatorRuleEngine aviatorRuleEngine = getAviatorRuleEngine();
        GroovyRuleEngine groovyRuleEngine = GroovyRuleEngine.getInstance();

        // 验证两种方法的结果是否一致
        Object aviatorResult = aviatorRuleEngine.execute(aviatorExpression, env);
        Object groovyResult = groovyRuleEngine.execute(groovyFileContext, env);

        System.out.println("Aviator结果: " + aviatorResult);
        System.out.println("Groovy结果: " + groovyResult);
        System.out.println("结果一致: " + aviatorResult.equals(groovyResult));
        System.out.println();
    }

}