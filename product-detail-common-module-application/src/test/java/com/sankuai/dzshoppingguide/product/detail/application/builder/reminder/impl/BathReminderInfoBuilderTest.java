package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AvailableTimeHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.EffectiveDateHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BathReminderInfoBuilderTest {

    @Spy
    @InjectMocks
    private BathReminderInfoBuilder bathReminderInfoBuilder;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private ProductBaseInfo productBaseInfo;

    /**
     * Test when base reminder info is null
     */
    @Test
    public void testPreBuildWhenBaseReminderInfoIsNull() throws Throwable {
        // arrange
        doReturn(null).when(bathReminderInfoBuilder).getBaseReminderInfo();
        // act
        ProductDetailReminderVO result = bathReminderInfoBuilder.preBuild();
        // assert
        assertNull(result);
    }

    /**
     * Test when base reminder info has empty contents
     */
    @Test
    public void testPreBuildWhenContentsIsEmpty() throws Throwable {
        // arrange
        ProductDetailReminderVO baseReminder = new ProductDetailReminderVO();
        baseReminder.setContents(Collections.emptyList());
        doReturn(baseReminder).when(bathReminderInfoBuilder).getBaseReminderInfo();
        // act
        ProductDetailReminderVO result = bathReminderInfoBuilder.preBuild();
        // assert
        assertNull(result);
    }
}
