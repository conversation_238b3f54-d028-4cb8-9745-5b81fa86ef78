package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.Icon;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for StandardGuaranteeModuleBuilder.addRightArrow method
 */
@ExtendWith(MockitoExtension.class)
public class StandardGuaranteeModuleBuilderAddRightArrowTest {

    @Spy
    private GuaranteeInstructionsContentVO content;

    private TestableStandardGuaranteeModuleBuilder builder;

    @BeforeEach
    void setUp() {
        builder = new TestableStandardGuaranteeModuleBuilder();
    }

    /**
     * Test case: Successfully add right arrow icon to content
     * Expected: Content should have suffix icon with correct URL
     */
    @Test
    public void testAddRightArrow_ShouldSetSuffixIcon() {
        // arrange
        String expectedIconUrl = "https://p0.meituan.net/ingee/5bdf750a730fd2999c49e6a32a4753f0424.png";
        // act
        builder.addRightArrow(content);
        // assert
        verify(content, times(1)).setSuffixIcon(any(Icon.class));
        Icon actualIcon = content.getSuffixIcon();
        assertNotNull(actualIcon, "Suffix icon should not be null");
        assertEquals(expectedIconUrl, actualIcon.getIcon(), "Icon URL should match expected value");
    }

    /**
     * Test case: Add right arrow with null content
     * Expected: Should throw NullPointerException
     */
    @Test
    public void testAddRightArrow_WithNullContent_ShouldThrowException() {
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            builder.addRightArrow(null);
        }, "Should throw NullPointerException when content is null");
    }

    /**
     * Test case: Verify icon object creation and properties
     * Expected: Icon should be created with correct URL and set as suffix icon
     */
    @Test
    public void testAddRightArrow_VerifyIconProperties() {
        // arrange
        String expectedIconUrl = "https://p0.meituan.net/ingee/5bdf750a730fd2999c49e6a32a4753f0424.png";
        // act
        builder.addRightArrow(content);
        // assert
        verify(content).setSuffixIcon(argThat(icon -> {
            assertNotNull(icon, "Icon should not be null");
            assertEquals(expectedIconUrl, icon.getIcon(), "Icon URL should match expected value");
            return true;
        }));
    }

    /**
     * Testable implementation of StandardGuaranteeModuleBuilder for testing
     */
    private static class TestableStandardGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {

        @Override
        public ProductDetailGuaranteeVO doBuild() {
            return null;
        }
    }
}
