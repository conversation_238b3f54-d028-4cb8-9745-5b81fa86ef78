package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.components.facility;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.enums.ServiceFacilityEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.base.DetailComponentParam;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ServiceFacilityComponentTest {

    @InjectMocks
    private ServiceFacilityComponent serviceFacilityComponent;

    @Mock
    private ProductAttr productAttr;

    /**
     * Test build method when serviceFacility list is not empty
     * Scenario: buildServiceFacility returns a non-empty list with material and tool
     * Expected: resultList should contain the service facility
     */
    @Test
    public void testBuildWhenServiceFacilityNotEmpty() throws Throwable {
        // arrange
        DetailComponentParam param = DetailComponentParam.builder().productAttr(productAttr).build();
        // Mock behavior for material and tool
        when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn("tool1");
        // act
        List<DealDetailStructuredDetailVO> result = serviceFacilityComponent.build(param);
        // assert
        assertEquals(1, result.size());
        assertEquals(ServiceFacilityEnum.SERVICE_MATERIAL_AND_TOOL.getServiceFacility(), result.get(0).getTitle());
    }

    /**
     * Test build method with multiple service facilities
     * Scenario: buildServiceFacility returns list with material and tool facility
     * Expected: resultList should contain the service facility
     */
    @Test
    public void testBuildWithMultipleServiceFacilities() throws Throwable {
        // arrange
        DetailComponentParam param = DetailComponentParam.builder().productAttr(productAttr).build();
        // Mock behavior for material and tool
        when(productAttr.getSkuAttrFirstValue(anyString())).thenReturn("tool1");
        // act
        List<DealDetailStructuredDetailVO> result = serviceFacilityComponent.build(param);
        // assert
        assertEquals(1, result.size());
        assertEquals(ServiceFacilityEnum.SERVICE_MATERIAL_AND_TOOL.getServiceFacility(), result.get(0).getTitle());
    }
}
