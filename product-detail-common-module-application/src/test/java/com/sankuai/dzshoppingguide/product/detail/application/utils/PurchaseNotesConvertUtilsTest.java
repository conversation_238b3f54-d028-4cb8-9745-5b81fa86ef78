package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.purchase.note.ProductPurchaseNote;
import com.sankuai.general.product.query.center.client.dto.PurchaseNoteDTO;
import com.sankuai.general.product.query.center.client.enums.PurchaseNoteSceneEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayModuleDTO;
import com.sankuai.general.product.query.center.client.dto.StandardDisplayValueDTO;
import java.util.Arrays;
import org.junit.jupiter.api.DisplayName;

@ExtendWith(MockitoExtension.class)
class PurchaseNotesConvertUtilsTest {

    /**
     * 测试当输入data为null时，返回null
     */
    @Test
    void testGetPurchaseByEnum_WhenDataIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        ProductPurchaseNote data = null;
        PurchaseNoteSceneEnum type = PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE;
        // act
        PurchaseNoteDTO result = PurchaseNotesConvertUtils.getPurchaseByEnum(data, type);
        // assert
        assertNull(result);
    }

    /**
     * 测试当purchaseNotes为空时，返回null
     */
    @Test
    void testGetPurchaseByEnum_WhenPurchaseNotesIsEmpty_ShouldReturnNull() throws Throwable {
        // arrange
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(Collections.emptyList());
        PurchaseNoteSceneEnum type = PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE;
        // act
        PurchaseNoteDTO result = PurchaseNotesConvertUtils.getPurchaseByEnum(data, type);
        // assert
        assertNull(result);
    }

    /**
     * 测试当存在匹配的PurchaseNoteDTO时，返回匹配项
     */
    @Test
    void testGetPurchaseByEnum_WhenMatchFound_ShouldReturnMatchedNote() throws Throwable {
        // arrange
        PurchaseNoteDTO matchedNote = mock(PurchaseNoteDTO.class);
        when(matchedNote.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE);
        PurchaseNoteDTO otherNote = mock(PurchaseNoteDTO.class);
        when(otherNote.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.ORDER_REFUND_CHARGE_POLICY);
        List<PurchaseNoteDTO> notes = new ArrayList<>();
        notes.add(otherNote);
        notes.add(matchedNote);
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(notes);
        PurchaseNoteSceneEnum type = PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE;
        // act
        PurchaseNoteDTO result = PurchaseNotesConvertUtils.getPurchaseByEnum(data, type);
        // assert
        assertNotNull(result);
        assertEquals(matchedNote, result);
    }

    /**
     * 测试当没有匹配的PurchaseNoteDTO时，返回null
     */
    @Test
    void testGetPurchaseByEnum_WhenNoMatchFound_ShouldReturnNull() throws Throwable {
        // arrange
        PurchaseNoteDTO note1 = mock(PurchaseNoteDTO.class);
        when(note1.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.ORDER_REFUND_CHARGE_POLICY);
        PurchaseNoteDTO note2 = mock(PurchaseNoteDTO.class);
        when(note2.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.ORDER_DETAIL_REFUND_CHARGE_POLICY);
        List<PurchaseNoteDTO> notes = new ArrayList<>();
        notes.add(note1);
        notes.add(note2);
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(notes);
        PurchaseNoteSceneEnum type = PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE;
        // act
        PurchaseNoteDTO result = PurchaseNotesConvertUtils.getPurchaseByEnum(data, type);
        // assert
        assertNull(result);
    }

    @Test
    @DisplayName("Test processPurchaseNote with null PurchaseNoteDTO")
    public void testProcessPurchaseNoteWithNullDTO() throws Throwable {
        // arrange
        List<String> reminderInfo = new ArrayList<>();
        // act & assert
        assertThrows(NullPointerException.class, () -> PurchaseNotesConvertUtils.processPurchaseNote(null, reminderInfo));
    }

    @Test
    @DisplayName("Test processPurchaseNote with null modules")
    public void testProcessPurchaseNoteWithNullModules() throws Throwable {
        // arrange
        PurchaseNoteDTO purchaseNote = new PurchaseNoteDTO();
        purchaseNote.setModules(null);
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.processPurchaseNote(purchaseNote, reminderInfo);
        // assert
        assertTrue(reminderInfo.isEmpty());
    }

    @Test
    @DisplayName("Test processPurchaseNote with empty modules")
    public void testProcessPurchaseNoteWithEmptyModules() throws Throwable {
        // arrange
        PurchaseNoteDTO purchaseNote = new PurchaseNoteDTO();
        purchaseNote.setModules(Collections.emptyList());
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.processPurchaseNote(purchaseNote, reminderInfo);
        // assert
        assertTrue(reminderInfo.isEmpty());
    }

    @Test
    @DisplayName("Test processPurchaseNote with single valid module")
    public void testProcessPurchaseNoteWithSingleValidModule() throws Throwable {
        // arrange
        StandardDisplayValueDTO value = mock(StandardDisplayValueDTO.class);
        when(value.getValue()).thenReturn("TestValue");
        StandardDisplayItemDTO item = mock(StandardDisplayItemDTO.class);
        when(item.getItemValues()).thenReturn(Collections.singletonList(value));
        StandardDisplayModuleDTO module = mock(StandardDisplayModuleDTO.class);
        when(module.getItems()).thenReturn(Collections.singletonList(item));
        PurchaseNoteDTO purchaseNote = new PurchaseNoteDTO();
        purchaseNote.setModules(Collections.singletonList(module));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.processPurchaseNote(purchaseNote, reminderInfo);
        // assert
        assertEquals(1, reminderInfo.size());
        assertEquals("TestValue", reminderInfo.get(0));
        verify(item, times(2)).getItemValues();
        verify(value).getValue();
    }

    @Test
    @DisplayName("Test processPurchaseNote with multiple values")
    public void testProcessPurchaseNoteWithMultipleValues() throws Throwable {
        // arrange
        StandardDisplayValueDTO value1 = mock(StandardDisplayValueDTO.class);
        StandardDisplayValueDTO value2 = mock(StandardDisplayValueDTO.class);
        when(value1.getValue()).thenReturn("Value1");
        when(value2.getValue()).thenReturn("Value2");
        StandardDisplayItemDTO item = mock(StandardDisplayItemDTO.class);
        when(item.getItemValues()).thenReturn(Arrays.asList(value1, value2));
        StandardDisplayModuleDTO module = mock(StandardDisplayModuleDTO.class);
        when(module.getItems()).thenReturn(Collections.singletonList(item));
        PurchaseNoteDTO purchaseNote = new PurchaseNoteDTO();
        purchaseNote.setModules(Collections.singletonList(module));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.processPurchaseNote(purchaseNote, reminderInfo);
        // assert
        assertEquals(1, reminderInfo.size());
        assertEquals("Value1Value2", reminderInfo.get(0));
        verify(item, times(2)).getItemValues();
        verify(value1).getValue();
        verify(value2).getValue();
    }

    @Test
    @DisplayName("Test processPurchaseNote with null values")
    public void testProcessPurchaseNoteWithNullValues() throws Throwable {
        // arrange
        StandardDisplayValueDTO value = mock(StandardDisplayValueDTO.class);
        when(value.getValue()).thenReturn("ValidValue");
        StandardDisplayItemDTO item = mock(StandardDisplayItemDTO.class);
        when(item.getItemValues()).thenReturn(Arrays.asList(null, value));
        StandardDisplayModuleDTO module = mock(StandardDisplayModuleDTO.class);
        when(module.getItems()).thenReturn(Collections.singletonList(item));
        PurchaseNoteDTO purchaseNote = new PurchaseNoteDTO();
        purchaseNote.setModules(Collections.singletonList(module));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.processPurchaseNote(purchaseNote, reminderInfo);
        // assert
        assertEquals(1, reminderInfo.size());
        assertEquals("ValidValue", reminderInfo.get(0));
        verify(item, times(2)).getItemValues();
        verify(value).getValue();
    }

    @Test
    @DisplayName("Test processPurchaseNote with null items")
    public void testProcessPurchaseNoteWithNullItems() throws Throwable {
        // arrange
        StandardDisplayModuleDTO module = mock(StandardDisplayModuleDTO.class);
        when(module.getItems()).thenReturn(Collections.singletonList(null));
        PurchaseNoteDTO purchaseNote = new PurchaseNoteDTO();
        purchaseNote.setModules(Collections.singletonList(module));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.processPurchaseNote(purchaseNote, reminderInfo);
        // assert
        assertTrue(reminderInfo.isEmpty());
        verify(module).getItems();
    }

    @Test
    @DisplayName("Test processPurchaseNote with multiple modules")
    public void testProcessPurchaseNoteWithMultipleModules() throws Throwable {
        // arrange
        StandardDisplayValueDTO value1 = mock(StandardDisplayValueDTO.class);
        StandardDisplayValueDTO value2 = mock(StandardDisplayValueDTO.class);
        when(value1.getValue()).thenReturn("Module1Value");
        when(value2.getValue()).thenReturn("Module2Value");
        StandardDisplayItemDTO item1 = mock(StandardDisplayItemDTO.class);
        StandardDisplayItemDTO item2 = mock(StandardDisplayItemDTO.class);
        when(item1.getItemValues()).thenReturn(Collections.singletonList(value1));
        when(item2.getItemValues()).thenReturn(Collections.singletonList(value2));
        StandardDisplayModuleDTO module1 = mock(StandardDisplayModuleDTO.class);
        StandardDisplayModuleDTO module2 = mock(StandardDisplayModuleDTO.class);
        when(module1.getItems()).thenReturn(Collections.singletonList(item1));
        when(module2.getItems()).thenReturn(Collections.singletonList(item2));
        PurchaseNoteDTO purchaseNote = new PurchaseNoteDTO();
        purchaseNote.setModules(Arrays.asList(module1, module2));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.processPurchaseNote(purchaseNote, reminderInfo);
        // assert
        assertEquals(2, reminderInfo.size());
        assertEquals("Module1Value", reminderInfo.get(0));
        assertEquals("Module2Value", reminderInfo.get(1));
        verify(item1, times(2)).getItemValues();
        verify(item2, times(2)).getItemValues();
        verify(value1).getValue();
        verify(value2).getValue();
    }

    @Test
    @DisplayName("Should not modify reminderInfo when data is null")
    void testBuildReminderInfoWhenDataIsNull() throws Throwable {
        // arrange
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.buildReminderInfo(null, PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE, reminderInfo);
        // assert
        assertTrue(reminderInfo.isEmpty());
    }

    @Test
    @DisplayName("Should not modify reminderInfo when purchaseNotes is empty")
    void testBuildReminderInfoWhenPurchaseNotesIsEmpty() throws Throwable {
        // arrange
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(Collections.emptyList());
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.buildReminderInfo(data, PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE, reminderInfo);
        // assert
        assertTrue(reminderInfo.isEmpty());
        verify(data, times(1)).getPurchaseNotes();
    }

    @Test
    @DisplayName("Should not modify reminderInfo when type does not match")
    void testBuildReminderInfoWhenTypeNotMatch() throws Throwable {
        // arrange
        PurchaseNoteDTO note = mock(PurchaseNoteDTO.class);
        when(note.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.PRODUCT_DETAIL_BOOK_INFORMATION);
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(Collections.singletonList(note));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.buildReminderInfo(data, PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE, reminderInfo);
        // assert
        assertTrue(reminderInfo.isEmpty());
        verify(data, times(2)).getPurchaseNotes();
        verify(note).getSceneEnum();
    }

    @Test
    @DisplayName("Should not modify reminderInfo when modules is empty")
    void testBuildReminderInfoWhenModulesIsEmpty() throws Throwable {
        // arrange
        PurchaseNoteDTO note = mock(PurchaseNoteDTO.class);
        when(note.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE);
        when(note.getModules()).thenReturn(Collections.emptyList());
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(Collections.singletonList(note));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.buildReminderInfo(data, PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE, reminderInfo);
        // assert
        assertTrue(reminderInfo.isEmpty());
        verify(data, times(2)).getPurchaseNotes();
        verify(note).getSceneEnum();
        verify(note).getModules();
    }

    @Test
    @DisplayName("Should add value to reminderInfo when module is valid")
    void testBuildReminderInfoWithValidModule() throws Throwable {
        // arrange
        StandardDisplayValueDTO value = mock(StandardDisplayValueDTO.class);
        when(value.getValue()).thenReturn("test value");
        StandardDisplayItemDTO item = mock(StandardDisplayItemDTO.class);
        when(item.getItemValues()).thenReturn(Collections.singletonList(value));
        StandardDisplayModuleDTO module = mock(StandardDisplayModuleDTO.class);
        when(module.getItems()).thenReturn(Collections.singletonList(item));
        PurchaseNoteDTO note = mock(PurchaseNoteDTO.class);
        when(note.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE);
        when(note.getModules()).thenReturn(Collections.singletonList(module));
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(Collections.singletonList(note));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.buildReminderInfo(data, PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE, reminderInfo);
        // assert
        assertEquals(1, reminderInfo.size());
        assertEquals("test value", reminderInfo.get(0));
        verify(data, times(2)).getPurchaseNotes();
        verify(note).getSceneEnum();
        verify(note).getModules();
        verify(module).getItems();
        verify(item, times(2)).getItemValues();
        verify(value).getValue();
    }

    @Test
    @DisplayName("Should skip null items in module")
    void testBuildReminderInfoWithNullItems() throws Throwable {
        // arrange
        StandardDisplayValueDTO value = mock(StandardDisplayValueDTO.class);
        when(value.getValue()).thenReturn("test value");
        StandardDisplayItemDTO item = mock(StandardDisplayItemDTO.class);
        when(item.getItemValues()).thenReturn(Collections.singletonList(value));
        StandardDisplayModuleDTO module = mock(StandardDisplayModuleDTO.class);
        when(module.getItems()).thenReturn(Arrays.asList(null, item));
        PurchaseNoteDTO note = mock(PurchaseNoteDTO.class);
        when(note.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE);
        when(note.getModules()).thenReturn(Collections.singletonList(module));
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(Collections.singletonList(note));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.buildReminderInfo(data, PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE, reminderInfo);
        // assert
        assertEquals(1, reminderInfo.size());
        assertEquals("test value", reminderInfo.get(0));
        verify(data, times(2)).getPurchaseNotes();
        verify(note).getSceneEnum();
        verify(note).getModules();
        verify(module).getItems();
        verify(item, times(2)).getItemValues();
        verify(value).getValue();
    }

    @Test
    @DisplayName("Should skip null values in item")
    void testBuildReminderInfoWithNullValues() throws Throwable {
        // arrange
        StandardDisplayValueDTO value = mock(StandardDisplayValueDTO.class);
        when(value.getValue()).thenReturn("test value");
        StandardDisplayItemDTO item = mock(StandardDisplayItemDTO.class);
        when(item.getItemValues()).thenReturn(Arrays.asList(null, value));
        StandardDisplayModuleDTO module = mock(StandardDisplayModuleDTO.class);
        when(module.getItems()).thenReturn(Collections.singletonList(item));
        PurchaseNoteDTO note = mock(PurchaseNoteDTO.class);
        when(note.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE);
        when(note.getModules()).thenReturn(Collections.singletonList(module));
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(Collections.singletonList(note));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.buildReminderInfo(data, PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE, reminderInfo);
        // assert
        assertEquals(1, reminderInfo.size());
        assertEquals("test value", reminderInfo.get(0));
        verify(data, times(2)).getPurchaseNotes();
        verify(note).getSceneEnum();
        verify(note).getModules();
        verify(module).getItems();
        verify(item, times(2)).getItemValues();
        verify(value).getValue();
    }

    @Test
    @DisplayName("Should concatenate multiple values")
    void testBuildReminderInfoWithMultipleValues() throws Throwable {
        // arrange
        StandardDisplayValueDTO value1 = mock(StandardDisplayValueDTO.class);
        when(value1.getValue()).thenReturn("value1");
        StandardDisplayValueDTO value2 = mock(StandardDisplayValueDTO.class);
        when(value2.getValue()).thenReturn("value2");
        StandardDisplayItemDTO item = mock(StandardDisplayItemDTO.class);
        when(item.getItemValues()).thenReturn(Arrays.asList(value1, value2));
        StandardDisplayModuleDTO module = mock(StandardDisplayModuleDTO.class);
        when(module.getItems()).thenReturn(Collections.singletonList(item));
        PurchaseNoteDTO note = mock(PurchaseNoteDTO.class);
        when(note.getSceneEnum()).thenReturn(PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE);
        when(note.getModules()).thenReturn(Collections.singletonList(module));
        ProductPurchaseNote data = mock(ProductPurchaseNote.class);
        when(data.getPurchaseNotes()).thenReturn(Collections.singletonList(note));
        List<String> reminderInfo = new ArrayList<>();
        // act
        PurchaseNotesConvertUtils.buildReminderInfo(data, PurchaseNoteSceneEnum.PRODUCT_PURCHASE_NOTE, reminderInfo);
        // assert
        assertEquals(1, reminderInfo.size());
        assertEquals("value1value2", reminderInfo.get(0));
        verify(data, times(2)).getPurchaseNotes();
        verify(note).getSceneEnum();
        verify(note).getModules();
        verify(module).getItems();
        verify(item, times(2)).getItemValues();
        verify(value1).getValue();
        verify(value2).getValue();
    }
}
