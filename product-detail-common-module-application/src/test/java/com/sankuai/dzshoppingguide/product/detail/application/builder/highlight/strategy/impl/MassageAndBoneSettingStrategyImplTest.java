package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class MassageAndBoneSettingStrategyImplTest {

    private MassageAndBoneSettingStrategyImpl strategy;

    private List<ServiceProjectAttrDTO> mockAttrs;

    @BeforeEach
    void setUp() {
        strategy = new MassageAndBoneSettingStrategyImpl();
        mockAttrs = new ArrayList<>();
    }

    private ServiceProjectAttrDTO createMockAttr(String attrName, String attrValue) {
        ServiceProjectAttrDTO mock = Mockito.mock(ServiceProjectAttrDTO.class);
        when(mock.getAttrName()).thenReturn(attrName);
        when(mock.getAttrValue()).thenReturn(attrValue);
        return mock;
    }

    /**
     * Test when HOTPACK_TOOL exists should return its value
     */
    @Test
    public void testGetToolValueWhenHotpackToolExists() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("hotpackTool", "热敷毛巾"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("热敷毛巾", result);
    }

    /**
     * Test when MASSAGE_TOOL exists (and no HOTPACK_TOOL) should return massage tool value
     */
    @Test
    public void testGetToolValueWhenMassageToolExists() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("massageTool", "按摩油"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("按摩油", result);
    }

    /**
     * Test when only DISPOSABLE_MATERIAL exists should return highest priority item
     */
    @Test
    public void testGetToolValueWhenOnlyDisposableMaterialExists() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("disposableMaterial", "一次性床单、眼罩、一次性拖鞋"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("一次性床单", result);
    }

    /**
     * Test when only UNCLASSIFIED_TOOLS exists should return first item (no priority)
     */
    @Test
    public void testGetToolValueWhenOnlyUnclassifiedToolsExists() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("unclassifiedTools", "毛巾、枕头"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("毛巾", result);
    }

    /**
     * Test when no tools exist should return null
     */
    @Test
    public void testGetToolValueWhenNoToolsExist() throws Throwable {
        // arrange
        // empty mockAttrs
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertNull(result);
    }

    /**
     * Test when mixed tools contain items not in priority list should return first item
     */
    @Test
    public void testGetToolValueWithItemsNotInPriorityList() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("disposableMaterial", "unknownItem、anotherItem"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("unknownItem", result);
    }

    /**
     * Test priority sorting of mixed tools
     */
    @Test
    public void testGetToolValuePrioritySorting() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("disposableMaterial", "一次性拖鞋、香薰、眼罩"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        // "香薰" has higher priority than others in the list
        assertEquals("香薰", result);
    }

    /**
     * Test when DISPOSABLE_MATERIAL has empty value should skip it
     */
    @Test
    public void testGetToolValueWhenDisposableMaterialEmpty() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("disposableMaterial", ""));
        mockAttrs.add(createMockAttr("unclassifiedTools", "毛巾"));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("毛巾", result);
    }

    /**
     * Test when UNCLASSIFIED_TOOLS has empty value should skip it
     */
    @Test
    public void testGetToolValueWhenUnclassifiedToolsEmpty() throws Throwable {
        // arrange
        mockAttrs.add(createMockAttr("disposableMaterial", "眼罩"));
        mockAttrs.add(createMockAttr("unclassifiedTools", ""));
        // act
        String result = strategy.getToolValue(mockAttrs);
        // assert
        assertEquals("眼罩", result);
    }
}
