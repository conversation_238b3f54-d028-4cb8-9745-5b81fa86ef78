package com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.ReceiptEffectiveDateDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class EduOnlineReminderInfoBuilderTest {

    /**
     * Test when baseInfo is null
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_NullBaseInfo() throws Throwable {
        // arrange
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(null);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when receiptEffectiveDateDTO is null
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_NullReceiptEffectiveDate() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        when(baseInfo.getRule()).thenReturn(null);
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when receiptDateType is 0 with valid dates
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_DateRangeTypeWithValidDates() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(0);
        when(receiptDate.getReceiptBeginDate()).thenReturn("2023-01-01 00:00:00");
        when(receiptDate.getReceiptEndDate()).thenReturn("2023-12-31 23:59:59");
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("有效期至2023-12-31", result);
    }

    /**
     * Test when receiptDateType is 0 but begin date is null
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_DateRangeTypeWithNullBeginDate() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(0);
        when(receiptDate.getReceiptBeginDate()).thenReturn(null);
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when receiptDateType is 0 but end date is null
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_DateRangeTypeWithNullEndDate() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(0);
        when(receiptDate.getReceiptBeginDate()).thenReturn("2023-01-01 00:00:00");
        when(receiptDate.getReceiptEndDate()).thenReturn(null);
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when receiptDateType is 1 with valid days
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_ValidDaysTypeWithValidDays() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(1);
        when(receiptDate.getReceiptValidDays()).thenReturn(30);
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("开课后30天内有效", result);
    }

    /**
     * Test when receiptDateType is 1 but valid days is null
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_ValidDaysTypeWithNullDays() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(1);
        when(receiptDate.getReceiptValidDays()).thenReturn(null);
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when receiptDateType is null
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_NullReceiptDateType() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(null);
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when receiptDateType is neither 0 nor 1
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_InvalidReceiptDateType() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(2);
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when begin date format is invalid
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_InvalidBeginDateFormat() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(0);
        when(receiptDate.getReceiptBeginDate()).thenReturn("invalid-date");
        when(receiptDate.getReceiptEndDate()).thenReturn("2023-12-31 23:59:59");
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("有效期至2023-12-31", result);
    }

    /**
     * Test when end date format is invalid
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_InvalidEndDateFormat() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(0);
        when(receiptDate.getReceiptBeginDate()).thenReturn("2023-01-01 00:00:00");
        when(receiptDate.getReceiptEndDate()).thenReturn("invalid-date");
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("有效期至null", result);
    }

    /**
     * Test when both begin and end dates are null for date range type
     */
    @Test
    public void testGetEffectiveDateForOnlineCourse_DateRangeTypeWithBothDatesNull() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        ReceiptEffectiveDateDTO receiptDate = mock(ReceiptEffectiveDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getReceiptEffectiveDate()).thenReturn(receiptDate);
        when(receiptDate.getReceiptDateType()).thenReturn(0);
        when(receiptDate.getReceiptBeginDate()).thenReturn(null);
        EduOnlineReminderInfoBuilder builder = new EduOnlineReminderInfoBuilder();
        // act
        String result = builder.getEffectiveDateForOnlineCourse(baseInfo);
        // assert
        assertEquals("", result);
    }
}
