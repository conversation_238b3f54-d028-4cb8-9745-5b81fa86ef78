package com.sankuai.dzshoppingguide.product.detail.application.spi;

import static org.junit.jupiter.api.Assertions.*;

import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.ProductDetailPageResponse;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import java.util.HashSet;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
class ProductDetailPageCommonModuleSpiImplTest {

    @Mock
    private Logger log;

    @InjectMocks
    private ProductDetailPageCommonModuleSpiImpl spi;

    private ProductDetailPageRequest request;

    private ShepherdGatewayParam shepherdGatewayParam;

    @BeforeEach
    void setUp() {
        // Initialize ShepherdGatewayParam
        shepherdGatewayParam = new ShepherdGatewayParam();
        shepherdGatewayParam.setDpUserId(123L);
        shepherdGatewayParam.setMtUserId(456L);
        shepherdGatewayParam.setDeviceId("test-device");
        shepherdGatewayParam.setUnionid("test-unionid");
        shepherdGatewayParam.setMobileOSType("ios");
        // Initialize ProductDetailPageRequest
        request = new ProductDetailPageRequest();
        request.setModuleKeys(new HashSet<>());
        request.setProductId(123L);
        request.setProductType(1);
        request.setClientType(100);
        request.setGpsCoordinateType(1);
        request.setShepherdGatewayParam(shepherdGatewayParam);
    }

    /**
     * Test successful query with logging enabled
     */
    @Test
    void testQuerySuccessWithLogging() throws Throwable {
        // arrange
        request.getModuleKeys().add("TEST_MODULE");
        // act
        ProductDetailPageResponse response = spi.query(request);
        // assert
        assertNotNull(response);
        assertTrue(request.getModuleKeys().contains(ModuleKeyConstants.COMMON_DATA));
    }

    /**
     * Test successful query with logging disabled
     */
    @Test
    void testQuerySuccessWithoutLogging() throws Throwable {
        // arrange
        request.getModuleKeys().add("TEST_MODULE");
        // act
        ProductDetailPageResponse response = spi.query(request);
        // assert
        assertNotNull(response);
        assertTrue(request.getModuleKeys().contains(ModuleKeyConstants.COMMON_DATA));
    }

    /**
     * Test query when framework runner throws exception
     */
    @Test
    void testQueryWhenFrameworkRunnerFails() throws Throwable {
        // arrange
        request.getModuleKeys().add("TEST_MODULE");
        // act
        ProductDetailPageResponse response = spi.query(request);
        // assert
        assertNotNull(response);
        assertNotNull(response.getMsg());
    }

    /**
     * Test query when logging throws exception
     */
    @Test
    void testQueryWhenLoggingFails() throws Throwable {
        // arrange
        request.getModuleKeys().add("TEST_MODULE");
        // act
        ProductDetailPageResponse response = spi.query(request);
        // assert
        assertNotNull(response);
    }
}
