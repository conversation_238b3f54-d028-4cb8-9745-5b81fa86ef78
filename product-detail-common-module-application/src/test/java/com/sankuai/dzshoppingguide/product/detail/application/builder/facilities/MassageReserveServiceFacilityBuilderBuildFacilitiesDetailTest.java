package com.sankuai.dzshoppingguide.product.detail.application.builder.facilities;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.config.AttrConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.facilities.config.Configs;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.FacilitiesVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

class MassageReserveServiceFacilityBuilderBuildFacilitiesDetailTest {

    private MassageReserveServiceFacilityBuilder builder;

    private List<FacilitiesVO> detailServiceFacilities;

    private ProductAttr productAttr;

    private Configs configs;

    private AttrConfig upperAttr;

    private List<AttrConfig> configsList;

    @BeforeEach
    void setUp() {
        builder = spy(new MassageReserveServiceFacilityBuilder());
        detailServiceFacilities = new ArrayList<>();
        productAttr = mock(ProductAttr.class);
        // Setup configs
        configs = mock(Configs.class);
        upperAttr = mock(AttrConfig.class);
        when(upperAttr.getDisplayName()).thenReturn("Test Display Name");
        // Setup configsList
        AttrConfig config1 = mock(AttrConfig.class);
        when(config1.getAttrName()).thenReturn("testAttr1");
        when(config1.getDisplayName()).thenReturn("Test Display 1");
        configsList = Arrays.asList(config1);
        when(configs.getUpperAttr()).thenReturn(upperAttr);
        when(configs.getConfigs()).thenReturn(configsList);
        // Set static configs
        MassageReserveServiceFacilityBuilder.configs = configs;
    }

    /**
     * Test when productAttr is null, should not add any facilities
     */
    @Test
    public void testBuildFacilitiesDetailWhenProductAttrIsNull() throws Throwable {
        // arrange
        ProductAttr nullProductAttr = null;
        // act
        builder.buildFacilitiesDetail(nullProductAttr, detailServiceFacilities);
        // assert
        assertTrue(detailServiceFacilities.isEmpty());
    }

    /**
     * Test when serviceMaterialAndTool attribute is blank, should not add any facilities
     */
    @Test
    public void testBuildFacilitiesDetailWhenServiceMaterialAndToolIsBlank() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("serviceMaterialAndTool")).thenReturn("");
        // act
        builder.buildFacilitiesDetail(productAttr, detailServiceFacilities);
        // assert
        assertTrue(detailServiceFacilities.isEmpty());
        verify(productAttr).getSkuAttrFirstValue("serviceMaterialAndTool");
    }

    /**
     * Test when serviceMaterialAndTool attribute exists and is not blank, should add facilities
     */
    @Test
    public void testBuildFacilitiesDetailWhenServiceMaterialAndToolExists() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("serviceMaterialAndTool")).thenReturn("someValue");
        when(productAttr.getSkuAttrFirstValue("testAttr1")).thenReturn("testValue1");
        // act
        builder.buildFacilitiesDetail(productAttr, detailServiceFacilities);
        // assert
        verify(productAttr).getSkuAttrFirstValue("serviceMaterialAndTool");
        verify(builder).buildFacilities(eq(productAttr), eq(detailServiceFacilities), any());
    }

    /**
     * Test when configs is null, should not add facilities
     */
    @Test
    public void testBuildFacilitiesDetailWhenConfigsIsNull() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("serviceMaterialAndTool")).thenReturn("someValue");
        MassageReserveServiceFacilityBuilder.configs = null;
        try {
            // act
            builder.buildFacilitiesDetail(productAttr, detailServiceFacilities);
        } catch (NullPointerException e) {
            // expected
        }
        // assert
        assertTrue(detailServiceFacilities.isEmpty());
        verify(productAttr).getSkuAttrFirstValue("serviceMaterialAndTool");
    }

    /**
     * Test when buildFacilities throws exception, should propagate exception
     */
    @Test
    public void testBuildFacilitiesDetailWhenBuildFacilitiesThrowsException() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrFirstValue("serviceMaterialAndTool")).thenReturn("someValue");
        // Stub buildFacilities to throw exception
        doThrow(new RuntimeException("Test exception")).when(builder).buildFacilities(any(), any(), any());
        try {
            // act
            builder.buildFacilitiesDetail(productAttr, detailServiceFacilities);
        } catch (RuntimeException e) {
            // expected
            assertEquals("Test exception", e.getMessage());
        }
        // assert
        assertTrue(detailServiceFacilities.isEmpty());
        verify(productAttr).getSkuAttrFirstValue("serviceMaterialAndTool");
        verify(builder).buildFacilities(eq(productAttr), eq(detailServiceFacilities), any());
    }

    @Override
    protected void finalize() {
        // Reset static field after tests
        MassageReserveServiceFacilityBuilder.configs = null;
    }
}
