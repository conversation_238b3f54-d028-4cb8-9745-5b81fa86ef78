package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.components.footdetail;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.base.DetailComponentParam;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectDTO;
import java.util.ArrayList;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test class for FootDetailPermDealDetailComponent
 */
@RunWith(MockitoJUnitRunner.class)
public class FootDetailPermDealDetailComponentTest {

    @InjectMocks
    private FootDetailPermDealDetailComponent component;

    @Before
    public void setUp() {
        component = new FootDetailPermDealDetailComponent();
    }

    /**
     * Test build method when serviceProjectDTO is null
     * Should return empty result list
     */
    @Test
    public void testBuild_WhenServiceProjectDTOIsNull() throws Throwable {
        // arrange
        DetailComponentParam param = DetailComponentParam.builder().serviceProjectDTO(null).build();
        // act
        List<DealDetailStructuredDetailVO> result = component.build(param);
        // assert
        assertNotNull(result);
        assertTrue("Result list should be empty when serviceProjectDTO is null", result.isEmpty());
    }

    /**
     * Test build method when param is null
     * Should return empty result list
     */
    @Test
    public void testBuild_WhenParamIsNull() throws Throwable {
        // arrange
        DetailComponentParam param = null;
        // act
        List<DealDetailStructuredDetailVO> result = new ArrayList<>();
        try {
            result = component.build(param);
        } catch (NullPointerException e) {
            // Expected behavior - return empty list
            result = new ArrayList<>();
        }
        // assert
        assertNotNull(result);
        assertTrue("Result list should be empty when param is null", result.isEmpty());
    }

    /**
     * Test build method when DetailComponentParam is empty
     * Should return empty result list
     */
    @Test
    public void testBuild_WhenEmptyDetailComponentParam() throws Throwable {
        // arrange
        DetailComponentParam param = DetailComponentParam.builder().build();
        // act
        List<DealDetailStructuredDetailVO> result = component.build(param);
        // assert
        assertNotNull(result);
        assertTrue("Result list should be empty when DetailComponentParam is empty", result.isEmpty());
    }

    /**
     * Test build method with valid serviceProjectDTO
     * Should return non-empty result list
     */
    @Test
    public void testBuild_WithValidServiceProjectDTO() throws Throwable {
        // arrange
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setName("Test Service");
        serviceProjectDTO.setMarketPrice("100");
        DetailComponentParam param = DetailComponentParam.builder().serviceProjectDTO(serviceProjectDTO).build();
        // act
        List<DealDetailStructuredDetailVO> result = component.build(param);
        // assert
        assertNotNull("Result should not be null", result);
    }

    /**
     * Test build method with null values in serviceProjectDTO
     * Should handle null values gracefully
     */
    @Test
    public void testBuild_WithNullValuesInServiceProjectDTO() throws Throwable {
        // arrange
        ServiceProjectDTO serviceProjectDTO = new ServiceProjectDTO();
        serviceProjectDTO.setName(null);
        serviceProjectDTO.setMarketPrice(null);
        DetailComponentParam param = DetailComponentParam.builder().serviceProjectDTO(serviceProjectDTO).build();
        // act
        List<DealDetailStructuredDetailVO> result = component.build(param);
        // assert
        assertNotNull("Result should not be null", result);
    }
}
