package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.LayerConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import lombok.experimental.var;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for StandardGuaranteeModuleBuilder.getLayerConfig method
 */
@ExtendWith(MockitoExtension.class)
class StandardGuaranteeModuleBuilderGetLayerConfigTest {

    private TestStandardGuaranteeModuleBuilder builder;

    @BeforeEach
    void setUp() {
        builder = new TestStandardGuaranteeModuleBuilder();
    }

    /**
     * Test getting layer config with valid key
     */
    @Test
    void testGetLayerConfig_WithValidKey() throws Throwable {
        // arrange
        String validKey = "testKey";
        LayerConfig expectedConfig = new LayerConfig();
        expectedConfig.setTitle("Test Title");
        Map<String, LayerConfig> configMap = new HashMap<>();
        configMap.put(validKey, expectedConfig);
        builder.setTestConfigMap(configMap);
        // act
        LayerConfig result = builder.getLayerConfig(validKey);
        // assert
        assertNotNull(result);
        assertEquals(expectedConfig.getTitle(), result.getTitle());
    }

    /**
     * Test getting layer config with invalid key
     */
    @Test
    void testGetLayerConfig_WithInvalidKey() throws Throwable {
        // arrange
        String invalidKey = "nonExistentKey";
        builder.setTestConfigMap(Collections.emptyMap());
        // act
        LayerConfig result = builder.getLayerConfig(invalidKey);
        // assert
        assertNull(result);
    }

    /**
     * Test getting layer config when config map is null
     */
    @Test
    void testGetLayerConfig_WithNullConfigMap() throws Throwable {
        // arrange
        String key = "testKey";
        builder.setTestConfigMap(null);
        // act
        LayerConfig result = builder.getLayerConfig(key);
        // assert
        assertNull(result);
    }

    /**
     * Test getting layer config with null key
     */
    @Test
    void testGetLayerConfig_WithNullKey() throws Throwable {
        // arrange
        Map<String, LayerConfig> configMap = new HashMap<>();
        configMap.put("someKey", new LayerConfig());
        builder.setTestConfigMap(configMap);
        // act
        LayerConfig result = builder.getLayerConfig(null);
        // assert
        assertNull(result);
    }

    /**
     * Test implementation of StandardGuaranteeModuleBuilder for testing
     */
    private static class TestStandardGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {

        private Map<String, LayerConfig> testConfigMap;

        @Override
        public ProductDetailGuaranteeVO doBuild() {
            return null;
        }

        @Override
        protected LayerConfig getLayerConfig(String layerConfigKey) {
            return testConfigMap != null ? testConfigMap.get(layerConfigKey) : null;
        }

        public void setTestConfigMap(Map<String, LayerConfig> testConfigMap) {
            this.testConfigMap = testConfigMap;
        }
    }
}
