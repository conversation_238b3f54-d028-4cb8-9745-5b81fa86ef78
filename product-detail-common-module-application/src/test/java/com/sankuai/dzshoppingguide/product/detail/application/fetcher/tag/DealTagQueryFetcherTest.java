package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.deal.tag.dto.MultiSubjectTagBatchJudgeRequest;
import com.dianping.deal.tag.dto.MultiSubjectTagBatchJudgeResponse;
import com.dianping.deal.tag.dto.MultiSubjectTagRelationJudge;
import com.dianping.deal.tag.enums.SubjectTagRelation;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DealTagQueryFetcherTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    @InjectMocks
    private DealTagQueryFetcher dealTagQueryFetcher;

    /**
     * Helper method to set protected request field using reflection
     */
    private void setInternalRequest(ProductDetailPageRequest request) {
        try {
            java.lang.reflect.Field requestField = BaseFetcherContext.class.getDeclaredField("request");
            requestField.setAccessible(true);
            requestField.set(dealTagQueryFetcher, request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set request field", e);
        }
    }

    /**
     * Test when product type is not DEAL, should return null
     */
    @Test
    public void testDoFetchWhenProductTypeNotDeal() throws Throwable {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        when(mockRequest.getProductTypeEnum()).thenReturn(ProductTypeEnum.RESERVE);
        setInternalRequest(mockRequest);
        // act
        CompletableFuture<DealTagQueryResult> result = dealTagQueryFetcher.doFetch();
        // assert
        assertNull(result.get());
    }

    /**
     * Test when service returns null response, should return hasTag=false
     */
    @Test
    public void testDoFetchWhenServiceReturnsNull() throws Throwable {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        when(mockRequest.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        when(mockRequest.getProductId()).thenReturn(123L);
        setInternalRequest(mockRequest);
        when(compositeAtomService.queryTagIdByProductId(any())).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<DealTagQueryResult> result = dealTagQueryFetcher.doFetch();
        // assert
        assertFalse(result.get().getHasTag());
    }

    /**
     * Test when service returns unsuccessful response, should return hasTag=false
     */
    @Test
    public void testDoFetchWhenServiceReturnsUnsuccessful() throws Throwable {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        when(mockRequest.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        when(mockRequest.getProductId()).thenReturn(123L);
        setInternalRequest(mockRequest);
        MultiSubjectTagBatchJudgeResponse response = new MultiSubjectTagBatchJudgeResponse();
        response.setSuccess(false);
        when(compositeAtomService.queryTagIdByProductId(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<DealTagQueryResult> result = dealTagQueryFetcher.doFetch();
        // assert
        assertFalse(result.get().getHasTag());
    }

    /**
     * Test when service returns empty judge result, should return hasTag=false
     */
    @Test
    public void testDoFetchWhenServiceReturnsEmptyJudgeResult() throws Throwable {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        when(mockRequest.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        when(mockRequest.getProductId()).thenReturn(123L);
        setInternalRequest(mockRequest);
        MultiSubjectTagBatchJudgeResponse response = new MultiSubjectTagBatchJudgeResponse();
        response.setSuccess(true);
        response.setJudgeResult(Collections.emptyList());
        when(compositeAtomService.queryTagIdByProductId(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<DealTagQueryResult> result = dealTagQueryFetcher.doFetch();
        // assert
        assertFalse(result.get().getHasTag());
    }

    /**
     * Test when service returns results but no matching label ID, should return hasTag=false
     */
    @Test
    public void testDoFetchWhenNoMatchingLabelId() throws Throwable {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        when(mockRequest.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        when(mockRequest.getProductId()).thenReturn(123L);
        setInternalRequest(mockRequest);
        MultiSubjectTagRelationJudge judge = new MultiSubjectTagRelationJudge();
        // different from LABEL_ID
        judge.setTagId(999L);
        judge.setJudgeResult(true);
        MultiSubjectTagBatchJudgeResponse response = new MultiSubjectTagBatchJudgeResponse();
        response.setSuccess(true);
        ArrayList<MultiSubjectTagRelationJudge> judgeList = new ArrayList<>();
        judgeList.add(judge);
        response.setJudgeResult(judgeList);
        when(compositeAtomService.queryTagIdByProductId(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<DealTagQueryResult> result = dealTagQueryFetcher.doFetch();
        // assert
        assertFalse(result.get().getHasTag());
    }

    /**
     * Test when service returns matching label ID with true result, should return hasTag=true
     */
    @Test
    public void testDoFetchWhenMatchingLabelIdWithTrueResult() throws Throwable {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        when(mockRequest.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        when(mockRequest.getProductId()).thenReturn(123L);
        setInternalRequest(mockRequest);
        MultiSubjectTagRelationJudge judge = new MultiSubjectTagRelationJudge();
        judge.setTagId(DealTagQueryFetcher.LABEL_ID);
        judge.setJudgeResult(true);
        MultiSubjectTagBatchJudgeResponse response = new MultiSubjectTagBatchJudgeResponse();
        response.setSuccess(true);
        ArrayList<MultiSubjectTagRelationJudge> judgeList = new ArrayList<>();
        judgeList.add(judge);
        response.setJudgeResult(judgeList);
        when(compositeAtomService.queryTagIdByProductId(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<DealTagQueryResult> result = dealTagQueryFetcher.doFetch();
        // assert
        assertTrue(result.get().getHasTag());
    }

    /**
     * Test when service returns matching label ID with false result, should return hasTag=false
     */
    @Test
    public void testDoFetchWhenMatchingLabelIdWithFalseResult() throws Throwable {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        when(mockRequest.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        when(mockRequest.getProductId()).thenReturn(123L);
        setInternalRequest(mockRequest);
        MultiSubjectTagRelationJudge judge = new MultiSubjectTagRelationJudge();
        judge.setTagId(DealTagQueryFetcher.LABEL_ID);
        judge.setJudgeResult(false);
        MultiSubjectTagBatchJudgeResponse response = new MultiSubjectTagBatchJudgeResponse();
        response.setSuccess(true);
        ArrayList<MultiSubjectTagRelationJudge> judgeList = new ArrayList<>();
        judgeList.add(judge);
        response.setJudgeResult(judgeList);
        when(compositeAtomService.queryTagIdByProductId(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<DealTagQueryResult> result = dealTagQueryFetcher.doFetch();
        // assert
        assertFalse(result.get().getHasTag());
    }

    /**
     * Test when exception occurs, should return null and log error
     */
    @Test
    public void testDoFetchWhenExceptionOccurs() throws Throwable {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        when(mockRequest.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        when(mockRequest.getProductId()).thenReturn(123L);
        setInternalRequest(mockRequest);
        when(compositeAtomService.queryTagIdByProductId(any())).thenThrow(new RuntimeException("Test exception"));
        // act
        CompletableFuture<DealTagQueryResult> result = dealTagQueryFetcher.doFetch();
        // assert
        assertNull(result.get());
    }
}
