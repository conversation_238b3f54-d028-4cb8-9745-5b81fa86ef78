package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.account.result.Result;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.refund.DealGroupRefundRuleDTO;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StandardGuaranteeModuleBuilderBuildStandardGuaranteeContentsTest {

    @Mock
    private ProductCategory productCategory;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private ProductBaseInfo productBaseInfo;

    @Mock
    private DealGroupRuleDTO ruleDTO;

    @Mock
    private DealGroupRefundRuleDTO refundRuleDTO;

    private TestableStandardGuaranteeModuleBuilder builder;

    private static class TestableStandardGuaranteeModuleBuilder extends StandardGuaranteeModuleBuilder {

        private ProductCategory productCategory;

        private ProductAttr productAttr;

        private ProductBaseInfo productBaseInfo;

        public void setDependencies(ProductCategory productCategory, ProductAttr productAttr, ProductBaseInfo productBaseInfo) {
            this.productCategory = productCategory;
            this.productAttr = productAttr;
            this.productBaseInfo = productBaseInfo;
        }

        @Override
        protected <T extends FetcherReturnValueDTO> T getDependencyResult(Class<? extends BaseFetcherContext> dependencyFetcherClass) {
            if (dependencyFetcherClass == ProductCategoryFetcher.class) {
                return (T) productCategory;
            } else if (dependencyFetcherClass == ProductAttrFetcher.class) {
                return (T) productAttr;
            } else if (dependencyFetcherClass == ProductBaseInfoFetcher.class) {
                return (T) productBaseInfo;
            }
            return null;
        }

        @Override
        public ProductDetailGuaranteeVO doBuild() {
            return null;
        }

        // Override the checkRefundByProduct method to avoid static method call
        @Override
        protected boolean checkRefundByProduct(ProductCategory productCategory, ProductAttr productAttr) {
            return productCategory.getProductSecondCategoryId() == 506 && "预约成功后不可退改".equals(productAttr.getSkuAttrFirstValue("reservation_policy"));
        }
    }

    @BeforeEach
    void setUp() {
        builder = new TestableStandardGuaranteeModuleBuilder();
        builder.setDependencies(productCategory, productAttr, productBaseInfo);
    }

    /**
     * Test when all conditions are false, should return empty list
     */
    @Test
    void testBuildStandardGuaranteeContents_AllFalse() throws Throwable {
        // arrange
        when(productCategory.getProductSecondCategoryId()).thenReturn(0);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getRefundRule()).thenReturn(refundRuleDTO);
        when(refundRuleDTO.getSupportRefundType()).thenReturn(0);
        when(refundRuleDTO.isSupportOverdueAutoRefund()).thenReturn(false);
        // act
        List<GuaranteeInstructionsContentVO> result = builder.buildStandardGuaranteeContents();
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when checkRefundByProduct returns true
     */
    @Test
    void testBuildStandardGuaranteeContents_RefundByProduct() throws Throwable {
        // arrange
        when(productCategory.getProductSecondCategoryId()).thenReturn(506);
        when(productAttr.getSkuAttrFirstValue("reservation_policy")).thenReturn("预约成功后不可退改");
        // act
        List<GuaranteeInstructionsContentVO> result = builder.buildStandardGuaranteeContents();
        // assert
        assertEquals(1, result.size());
        assertEquals("未预约可退", result.get(0).getText());
        assertEquals("12", result.get(0).getFontSize());
        assertEquals("default", result.get(0).getType());
    }

    /**
     * Test when checkAutoRefundSwitch returns true
     */
    @Test
    void testBuildStandardGuaranteeContents_AutoRefund() throws Throwable {
        // arrange
        when(productCategory.getProductSecondCategoryId()).thenReturn(0);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getRefundRule()).thenReturn(refundRuleDTO);
        when(refundRuleDTO.getSupportRefundType()).thenReturn(1);
        // act
        List<GuaranteeInstructionsContentVO> result = builder.buildStandardGuaranteeContents();
        // assert
        assertEquals(1, result.size());
        assertEquals("随时退", result.get(0).getText());
        assertEquals("12", result.get(0).getFontSize());
        assertEquals("default", result.get(0).getType());
    }

    /**
     * Test when checkOverdueAutoRefund returns true
     */
    @Test
    void testBuildStandardGuaranteeContents_OverdueRefund() throws Throwable {
        // arrange
        when(productCategory.getProductSecondCategoryId()).thenReturn(0);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getRefundRule()).thenReturn(refundRuleDTO);
        when(refundRuleDTO.getSupportRefundType()).thenReturn(0);
        when(refundRuleDTO.isSupportOverdueAutoRefund()).thenReturn(true);
        // act
        List<GuaranteeInstructionsContentVO> result = builder.buildStandardGuaranteeContents();
        // assert
        assertEquals(1, result.size());
        assertEquals("过期退", result.get(0).getText());
        assertEquals("12", result.get(0).getFontSize());
        assertEquals("default", result.get(0).getType());
    }

    /**
     * Test when both autoRefund and overdueRefund are true
     */
    @Test
    void testBuildStandardGuaranteeContents_BothRefunds() throws Throwable {
        // arrange
        when(productCategory.getProductSecondCategoryId()).thenReturn(0);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getRefundRule()).thenReturn(refundRuleDTO);
        when(refundRuleDTO.getSupportRefundType()).thenReturn(1);
        when(refundRuleDTO.isSupportOverdueAutoRefund()).thenReturn(true);
        // act
        List<GuaranteeInstructionsContentVO> result = builder.buildStandardGuaranteeContents();
        // assert
        assertEquals(2, result.size());
        assertEquals("随时退", result.get(0).getText());
        assertEquals("过期退", result.get(1).getText());
    }

    /**
     * Test when productBaseInfo is null
     */
    @Test
    void testBuildStandardGuaranteeContents_NullProductBaseInfo() throws Throwable {
        // arrange
        when(productCategory.getProductSecondCategoryId()).thenReturn(0);
        builder.setDependencies(productCategory, productAttr, null);
        // act
        List<GuaranteeInstructionsContentVO> result = builder.buildStandardGuaranteeContents();
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when refundRule is null
     */
    @Test
    void testBuildStandardGuaranteeContents_NullRefundRule() throws Throwable {
        // arrange
        when(productCategory.getProductSecondCategoryId()).thenReturn(0);
        when(productBaseInfo.getRule()).thenReturn(ruleDTO);
        when(ruleDTO.getRefundRule()).thenReturn(null);
        // act
        List<GuaranteeInstructionsContentVO> result = builder.buildStandardGuaranteeContents();
        // assert
        assertTrue(result.isEmpty());
    }
}
