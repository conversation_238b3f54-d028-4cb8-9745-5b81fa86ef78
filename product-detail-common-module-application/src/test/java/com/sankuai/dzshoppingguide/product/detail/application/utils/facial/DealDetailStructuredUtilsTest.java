package com.sankuai.dzshoppingguide.product.detail.application.utils.facial;

import static org.junit.jupiter.api.Assertions.*;

import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import org.mockito.Mock;
import java.util.List;

import static org.mockito.Mockito.mock;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

@ExtendWith(MockitoExtension.class)
@DisplayName("DealDetailStructuredUtils.buildLensContentJson方法测试")
class DealDetailStructuredUtilsTest {

    @Mock
    private ProductAttr productAttr;

    private static final String TEST_KEY = "testKey";

    private static final String TEST_TITLE = "testTitle";

    /**
     * 测试当content为null时返回Optional.empty()
     */
    @Test
    void testBuildSubProcessInfo_WhenContentIsNull_ShouldReturnEmptyOptional() {
        // arrange
        String content = null;
        String popupData = "anyPopupData";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildSubProcessInfo(content, popupData);
        // assert
        assertFalse(result.isPresent(), "Should return empty Optional when content is null");
    }

    /**
     * 测试当content为空字符串时返回Optional.empty()
     */
    @Test
    void testBuildSubProcessInfo_WhenContentIsEmpty_ShouldReturnEmptyOptional() {
        // arrange
        String content = "";
        String popupData = "anyPopupData";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildSubProcessInfo(content, popupData);
        // assert
        assertFalse(result.isPresent(), "Should return empty Optional when content is empty");
    }

    /**
     * 测试当content为空白字符串时返回Optional.empty()
     */
    @Test
    void testBuildSubProcessInfo_WhenContentIsBlank_ShouldReturnEmptyOptional() {
        // arrange
        String content = "   ";
        String popupData = "anyPopupData";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildSubProcessInfo(content, popupData);
        // assert
        assertFalse(result.isPresent(), "Should return empty Optional when content is blank");
    }

    /**
     * 测试当content有效但popupData为null时返回包含正确构建的VO
     */
    @Test
    void testBuildSubProcessInfo_WhenContentValidAndPopupDataNull_ShouldReturnValidVOWithoutPopupData() {
        // arrange
        String content = "valid content";
        String popupData = null;
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildSubProcessInfo(content, popupData);
        // assert
        assertTrue(result.isPresent(), "Should return non-empty Optional when content is valid");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(content, vo.getContent(), "Content should match input");
        assertEquals(ViewComponentTypeEnum.SUB_TEXT.getType(), vo.getType(), "Type should be SUB_TEXT");
        assertNull(vo.getPopupData(), "PopupData should be null when input is null");
    }

    /**
     * 测试当content和popupData都有效时返回包含正确构建的VO
     */
    @Test
    void testBuildSubProcessInfo_WhenContentAndPopupDataValid_ShouldReturnValidVOWithAllFields() {
        // arrange
        String content = "valid content";
        String popupData = "valid popup data";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildSubProcessInfo(content, popupData);
        // assert
        assertTrue(result.isPresent(), "Should return non-empty Optional when inputs are valid");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(content, vo.getContent(), "Content should match input");
        assertEquals(ViewComponentTypeEnum.SUB_TEXT.getType(), vo.getType(), "Type should be SUB_TEXT");
        assertEquals(popupData, vo.getPopupData(), "PopupData should match input");
    }

    /**
     * 测试当content有效但popupData为空字符串时返回的VO不包含popupData
     */
    @Test
    void testBuildSubProcessInfo_WhenContentValidAndPopupDataEmpty_ShouldReturnVOWithoutPopupData() {
        // arrange
        String content = "valid content";
        String popupData = "";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildSubProcessInfo(content, popupData);
        // assert
        assertTrue(result.isPresent(), "Should return non-empty Optional when content is valid");
        assertNull(result.get().getPopupData(), "PopupData should be null when input is empty string");
    }

    /**
     * 测试当content有效但popupData为空白字符串时返回的VO不包含popupData
     */
    @Test
    void testBuildSubProcessInfo_WhenContentValidAndPopupDataBlank_ShouldReturnVOWithoutPopupData() {
        // arrange
        String content = "valid content";
        String popupData = "   ";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildSubProcessInfo(content, popupData);
        // assert
        assertTrue(result.isPresent(), "Should return non-empty Optional when content is valid");
        assertNull(result.get().getPopupData(), "PopupData should be null when input is blank string");
    }

    @Test
    void testBuildCurrentFoldLimiter_ReturnsValidVO() {
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildCurrentFoldLimiter();
        // assert
        assertNotNull(result, "Returned VO should not be null");
        assertEquals("showAll", result.getContent(), "Content should be 'showAll'");
        assertEquals(ViewComponentTypeEnum.DELIMITER.getType(), result.getType(), "Type should match DELIMITER type");
    }

    @Test
    void testBuildCurrentFoldLimiter_ReturnsNewInstanceEachTime() {
        // act
        DealDetailStructuredDetailVO firstCall = DealDetailStructuredUtils.buildCurrentFoldLimiter();
        DealDetailStructuredDetailVO secondCall = DealDetailStructuredUtils.buildCurrentFoldLimiter();
        // assert
        assertNotSame(firstCall, secondCall, "Each call should return a new instance");
    }

    @Test
    void testBuildCurrentFoldLimiter_OnlySetsRequiredFields() {
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildCurrentFoldLimiter();
        // assert
        // Verify content and type are set
        assertEquals("showAll", result.getContent());
        assertEquals(ViewComponentTypeEnum.DELIMITER.getType(), result.getType());
        // Verify all other fields are null/default
        assertNull(result.getItemId(), "itemId should be null");
        assertNull(result.getTitle(), "title should be null");
        assertNull(result.getContentColor(), "contentColor should be null");
        assertNull(result.getPrefix(), "prefix should be null");
        assertNull(result.getJumpUrl(), "jumpUrl should be null");
        assertNull(result.getPopupData(), "popupData should be null");
        assertNull(result.getEndBackgroundColor(), "endBackgroundColor should be null");
        assertNull(result.getOrder(), "order should be null");
        assertNull(result.getSubContent(), "subContent should be null");
        assertNull(result.getIcon(), "icon should be null");
        assertNull(result.getBackgroundColor(), "backgroundColor should be null");
        assertNull(result.getDetail(), "detail should be null");
        assertNull(result.getUnit(), "unit should be null");
    }

    @Test
    public void testBuildContentFromAttrsWhenSkuAttrValuesEmpty() throws Throwable {
        // arrange
        String key = "color";
        String title = "颜色";
        when(productAttr.getSkuAttrValues(key)).thenReturn(Collections.emptyList());
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrs(key, title, productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildContentFromAttrsWhenSkuAttrValuesContainsBlank() throws Throwable {
        // arrange
        String key = "size";
        String title = "尺寸";
        when(productAttr.getSkuAttrValues(key)).thenReturn(Arrays.asList("S", "", "M", "  ", "L"));
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrs(key, title, productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("尺寸", result.get().getTitle());
        assertEquals("S、M、L", result.get().getContent());
    }

    @Test
    public void testBuildContentFromAttrsWhenSkuAttrValuesAllValid() throws Throwable {
        // arrange
        String key = "material";
        String title = "材质";
        when(productAttr.getSkuAttrValues(key)).thenReturn(Arrays.asList("棉", "涤纶", "丝绸"));
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrs(key, title, productAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("材质", result.get().getTitle());
        assertEquals("棉、涤纶、丝绸", result.get().getContent());
    }

    @Test
    public void testBuildContentFromAttrsWhenKeyIsNull() throws Throwable {
        // arrange
        String title = "测试";
        when(productAttr.getSkuAttrValues(null)).thenReturn(Collections.emptyList());
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrs(null, title, productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildContentFromAttrsWhenTitleIsNull() throws Throwable {
        // arrange
        String key = "weight";
        when(productAttr.getSkuAttrValues(key)).thenReturn(Arrays.asList("1kg", "2kg"));
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrs(key, null, productAttr);
        // assert
        assertTrue(result.isPresent());
        assertNull(result.get().getTitle());
        assertEquals("1kg、2kg", result.get().getContent());
    }

    @Test
    public void testBuildContentFromAttrsWhenProductAttrIsNull() throws Throwable {
        // arrange
        String key = "test";
        String title = "测试";
        // act & assert
        assertThrows(NullPointerException.class, () -> DealDetailStructuredUtils.buildContentFromAttrs(key, title, null));
    }

    @Test
    public void testBuildContentFromAttrsWhenKeyIsEmpty() throws Throwable {
        // arrange
        String title = "测试";
        when(productAttr.getSkuAttrValues("")).thenReturn(Collections.emptyList());
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrs("", title, productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildContentFromAttrsWithRealProductAttr() throws Throwable {
        // arrange
        String key = "color";
        String title = "颜色";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Arrays.asList("红色", "蓝色"));
        attrMap.put(key, attrDTO);
        ProductAttr realProductAttr = new ProductAttr(attrMap);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrs(key, title, realProductAttr);
        // assert
        assertTrue(result.isPresent());
        assertEquals("颜色", result.get().getTitle());
        assertEquals("红色、蓝色", result.get().getContent());
    }

    @Test
    public void testBuildContentFromAttr_Success() {
        // arrange
        String key = "testKey";
        String title = "Test Title";
        String attrValue = "Test Value";
        when(productAttr.getSkuAttrFirstValue(key)).thenReturn(attrValue);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttr(key, title, productAttr);
        // assert
        assertTrue(result.isPresent());
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(title, vo.getTitle());
        assertEquals(attrValue, vo.getContent());
    }

    @Test
    public void testBuildContentFromAttr_NullTitle() {
        // arrange
        String key = "testKey";
        String title = null;
        String attrValue = "Test Value";
        when(productAttr.getSkuAttrFirstValue(key)).thenReturn(attrValue);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttr(key, title, productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildContentFromAttr_EmptyTitle() {
        // arrange
        String key = "testKey";
        String title = "";
        String attrValue = "Test Value";
        when(productAttr.getSkuAttrFirstValue(key)).thenReturn(attrValue);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttr(key, title, productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildContentFromAttr_NullResult() {
        // arrange
        String key = "testKey";
        String title = "Test Title";
        when(productAttr.getSkuAttrFirstValue(key)).thenReturn(null);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttr(key, title, productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildContentFromAttr_EmptyResult() {
        // arrange
        String key = "testKey";
        String title = "Test Title";
        when(productAttr.getSkuAttrFirstValue(key)).thenReturn("");
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttr(key, title, productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildContentFromAttr_NullProductAttr() {
        // arrange
        String key = "testKey";
        String title = "Test Title";
        // act & assert
        assertThrows(NullPointerException.class, () -> DealDetailStructuredUtils.buildContentFromAttr(key, title, null));
    }

    @Test
    public void testBuildLensTitleVOWithValidTitle() throws Throwable {
        // arrange
        String expectedTitle = "Valid Title";
        int expectedType = ViewComponentTypeEnum.NORMAL_TEXT.getType();
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildLensTitleVO(expectedTitle);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals(expectedType, result.getType(), "类型应设置为NORMAL_TEXT");
        assertEquals(expectedTitle, result.getTitle(), "标题应正确设置");
    }

    @Test
    public void testBuildLensTitleVOWithEmptyTitle() throws Throwable {
        // arrange
        String expectedTitle = "";
        int expectedType = ViewComponentTypeEnum.NORMAL_TEXT.getType();
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildLensTitleVO(expectedTitle);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals(expectedType, result.getType(), "类型应设置为NORMAL_TEXT");
        assertEquals(expectedTitle, result.getTitle(), "标题应为空字符串");
    }

    @Test
    public void testBuildLensTitleVOWithNullTitle() throws Throwable {
        // arrange
        String expectedTitle = null;
        int expectedType = ViewComponentTypeEnum.NORMAL_TEXT.getType();
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildLensTitleVO(expectedTitle);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals(expectedType, result.getType(), "类型应设置为NORMAL_TEXT");
        assertNull(result.getTitle(), "标题应为null");
    }

    @Test
    public void testBuildLensTitleVOBuilderInvocation() throws Throwable {
        // arrange
        String expectedTitle = "Test Title";
        int expectedType = ViewComponentTypeEnum.NORMAL_TEXT.getType();
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildLensTitleVO(expectedTitle);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals(expectedType, result.getType(), "类型应设置为NORMAL_TEXT");
        assertEquals(expectedTitle, result.getTitle(), "标题应正确设置");
        // 验证其他属性为默认值
        assertNull(result.getItemId(), "itemId应为null");
        assertNull(result.getContentColor(), "contentColor应为null");
        assertNull(result.getPrefix(), "prefix应为null");
        assertNull(result.getJumpUrl(), "jumpUrl应为null");
        assertNull(result.getPopupData(), "popupData应为null");
        assertNull(result.getEndBackgroundColor(), "endBackgroundColor应为null");
        assertNull(result.getOrder(), "order应为null");
        assertNull(result.getSubContent(), "subContent应为null");
        assertNull(result.getIcon(), "icon应为null");
        assertNull(result.getBackgroundColor(), "backgroundColor应为null");
        assertNull(result.getDetail(), "detail应为null");
        assertNull(result.getContent(), "content应为null");
    }

    @Test
    public void testBuildContentFromAttrsWithLimit_EmptySkuAttrValues() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrValues(TEST_KEY)).thenReturn(Collections.emptyList());
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrsWithLimit(TEST_KEY, TEST_TITLE, productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildContentFromAttrsWithLimit_NullSkuAttrValues() throws Throwable {
        // arrange
        when(productAttr.getSkuAttrValues(TEST_KEY)).thenReturn(null);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrsWithLimit(TEST_KEY, TEST_TITLE, productAttr);
        // assert
        assertFalse(result.isPresent());
    }

    @Test
    public void testBuildContentFromAttrsWithLimit_SingleValidValue() throws Throwable {
        // arrange
        List<String> values = Collections.singletonList("value1");
        when(productAttr.getSkuAttrValues(TEST_KEY)).thenReturn(values);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrsWithLimit(TEST_KEY, TEST_TITLE, productAttr);
        // assert
        assertTrue(result.isPresent());
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(TEST_TITLE, vo.getTitle());
        assertEquals("value1", vo.getContent());
    }

    @Test
    public void testBuildContentFromAttrsWithLimit_ThreeValidValues() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("value1", "value2", "value3");
        when(productAttr.getSkuAttrValues(TEST_KEY)).thenReturn(values);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrsWithLimit(TEST_KEY, TEST_TITLE, productAttr);
        // assert
        assertTrue(result.isPresent());
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(TEST_TITLE, vo.getTitle());
        assertEquals("value1、value2、value3", vo.getContent());
    }

    @Test
    public void testBuildContentFromAttrsWithLimit_MoreThanThreeValues() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("value1", "value2", "value3", "value4", "value5");
        when(productAttr.getSkuAttrValues(TEST_KEY)).thenReturn(values);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrsWithLimit(TEST_KEY, TEST_TITLE, productAttr);
        // assert
        assertTrue(result.isPresent());
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(TEST_TITLE, vo.getTitle());
        assertEquals("value1、value2、value3", vo.getContent());
    }

    @Test
    public void testBuildContentFromAttrsWithLimit_FilterEmptyValues() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("value1", "", null, "value2", "   ", "value3");
        when(productAttr.getSkuAttrValues(TEST_KEY)).thenReturn(values);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrsWithLimit(TEST_KEY, TEST_TITLE, productAttr);
        // assert
        assertTrue(result.isPresent());
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(TEST_TITLE, vo.getTitle());
        assertEquals("value1、value2、value3", vo.getContent());
    }

    @Test
    public void testBuildContentFromAttrsWithLimit_AllEmptyValues() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("", null, "   ");
        when(productAttr.getSkuAttrValues(TEST_KEY)).thenReturn(values);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrsWithLimit(TEST_KEY, TEST_TITLE, productAttr);
        // assert
        assertTrue(result.isPresent());
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(TEST_TITLE, vo.getTitle());
        assertEquals("", vo.getContent());
    }

    @Test
    public void testBuildContentFromAttrsWithLimit_EmptyTitle() throws Throwable {
        // arrange
        List<String> values = Collections.singletonList("value1");
        when(productAttr.getSkuAttrValues(TEST_KEY)).thenReturn(values);
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildContentFromAttrsWithLimit(TEST_KEY, "", productAttr);
        // assert
        assertTrue(result.isPresent());
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals("", vo.getTitle());
        assertEquals("value1", vo.getContent());
    }

    @Test
    void testBuildProcessInfo_WhenTitleIsNull_ShouldReturnEmptyOptional() {
        // arrange
        String title = null;
        String duration = "10";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildProcessInfo(title, duration);
        // assert
        assertFalse(result.isPresent(), "当title为null时应返回空的Optional");
    }

    @Test
    void testBuildProcessInfo_WhenTitleIsEmpty_ShouldReturnEmptyOptional() {
        // arrange
        String title = "";
        String duration = "10";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildProcessInfo(title, duration);
        // assert
        assertFalse(result.isPresent(), "当title为空字符串时应返回空的Optional");
    }

    @Test
    void testBuildProcessInfo_WhenTitleIsBlank_ShouldReturnEmptyOptional() {
        // arrange
        String title = "   ";
        String duration = "10";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildProcessInfo(title, duration);
        // assert
        assertFalse(result.isPresent(), "当title为空白字符串时应返回空的Optional");
    }

    @Test
    void testBuildProcessInfo_WhenTitleValidAndDurationNull_ShouldBuildWithoutDetail() {
        // arrange
        String title = "有效标题";
        String duration = null;
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildProcessInfo(title, duration);
        // assert
        assertTrue(result.isPresent(), "当title有效时应返回非空Optional");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(title, vo.getTitle(), "标题应正确设置");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "类型应设置为NORMAL_TEXT");
        assertNull(vo.getDetail(), "当duration为null时detail应为null");
    }

    @Test
    void testBuildProcessInfo_WhenTitleValidAndDurationZero_ShouldBuildWithoutDetail() {
        // arrange
        String title = "有效标题";
        String duration = "0";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildProcessInfo(title, duration);
        // assert
        assertTrue(result.isPresent(), "当title有效时应返回非空Optional");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(title, vo.getTitle(), "标题应正确设置");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "类型应设置为NORMAL_TEXT");
        assertNull(vo.getDetail(), "当duration为'0'时detail应为null");
    }

    @Test
    void testBuildProcessInfo_WhenTitleAndDurationValid_ShouldBuildAllFields() {
        // arrange
        String title = "有效标题";
        String duration = "30";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildProcessInfo(title, duration);
        // assert
        assertTrue(result.isPresent(), "当title和duration都有效时应返回非空Optional");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(title, vo.getTitle(), "标题应正确设置");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "类型应设置为NORMAL_TEXT");
        assertEquals(duration + "分钟", vo.getDetail(), "详情应正确设置并附加'分钟'后缀");
    }

    @Test
    void testBuildProcessInfo_WhenDurationIsBlank_ShouldTreatAsInvalid() {
        // arrange
        String title = "有效标题";
        String duration = "   ";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildProcessInfo(title, duration);
        // assert
        assertTrue(result.isPresent(), "当title有效时应返回非空Optional");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(title, vo.getTitle(), "标题应正确设置");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "类型应设置为NORMAL_TEXT");
        assertNull(vo.getDetail(), "当duration为空白字符串时detail应为null");
    }

    @Test
    void testBuildLimiter_ShouldReturnVOWithCorrectType() throws Throwable {
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildLimiter();
        // assert
        assertNotNull(result, "返回的对象不应为null");
        assertEquals(ViewComponentTypeEnum.DELIMITER.getType(), result.getType(), "返回对象的type应该等于DELIMITER的类型值");
    }

    @Test
    void testBuildLimiter_ShouldReturnNewInstanceEachTime() throws Throwable {
        // act
        DealDetailStructuredDetailVO result1 = DealDetailStructuredUtils.buildLimiter();
        DealDetailStructuredDetailVO result2 = DealDetailStructuredUtils.buildLimiter();
        // assert
        assertNotSame(result1, result2, "每次调用应该返回新的实例");
    }

    @Test
    public void testBuildTitleAndContentWithValidInputs() {
        // arrange
        String expectedTitle = "Product Title";
        String expectedContent = "Product Description";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContent(expectedTitle, expectedContent);
        // assert
        assertTrue(result.isPresent(), "Result should be present");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(expectedTitle, vo.getTitle(), "Title should match input");
        assertEquals(expectedContent, vo.getContent(), "Content should match input");
    }

    @Test
    public void testBuildTitleAndContentWithNullTitle() {
        // arrange
        String expectedContent = "Product Description";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContent(null, expectedContent);
        // assert
        assertTrue(result.isPresent(), "Result should be present even with null title");
        DealDetailStructuredDetailVO vo = result.get();
        assertNull(vo.getTitle(), "Title should be null");
        assertEquals(expectedContent, vo.getContent(), "Content should match input");
    }

    @Test
    public void testBuildTitleAndContentWithNullContent() {
        // arrange
        String expectedTitle = "Product Title";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContent(expectedTitle, null);
        // assert
        assertTrue(result.isPresent(), "Result should be present even with null content");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(expectedTitle, vo.getTitle(), "Title should match input");
        assertNull(vo.getContent(), "Content should be null");
    }

    @Test
    public void testBuildTitleAndContentWithBothNull() {
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContent(null, null);
        // assert
        assertTrue(result.isPresent(), "Result should be present even with both null inputs");
        DealDetailStructuredDetailVO vo = result.get();
        assertNull(vo.getTitle(), "Title should be null");
        assertNull(vo.getContent(), "Content should be null");
    }

    @Test
    public void testBuildTitleAndContentWithEmptyStrings() {
        // arrange
        String expectedTitle = "";
        String expectedContent = "";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContent(expectedTitle, expectedContent);
        // assert
        assertTrue(result.isPresent(), "Result should be present with empty strings");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(expectedTitle, vo.getTitle(), "Title should be empty string");
        assertEquals(expectedContent, vo.getContent(), "Content should be empty string");
    }

    @Test
    public void testBuildAnnotation_ReturnsExpectedContent() {
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildAnnotation();
        // assert
        assertAll(() -> assertTrue(result.isPresent(), "Optional应该包含值"), () -> assertEquals(ViewComponentTypeEnum.ANNOTATION.getType(), result.get().getType(), "类型应该匹配ANNOTATION"), () -> assertEquals("注：服务时长仅供参考，由于个体差异，实际时长请以到店服务为准", result.get().getContent(), "内容应该匹配预期文本"));
    }

    @Test
    public void testBuildAnnotation_OtherFieldsAreNull() {
        // act
        DealDetailStructuredDetailVO vo = DealDetailStructuredUtils.buildAnnotation().get();
        // assert
        assertAll(() -> assertNull(vo.getItemId(), "itemId应该为null"), () -> assertNull(vo.getTitle(), "title应该为null"), () -> assertNull(vo.getJumpUrl(), "jumpUrl应该为null"));
    }

    @Test
    void testBuildTitleAndContentWithPrefix_WhenTitleAndContentProvided_ShouldReturnValidVO() throws Throwable {
        // arrange
        String title = "Test Title";
        String content = "Test Content";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContentWithPrefix(title, content);
        // assert
        assertTrue(result.isPresent(), "Result should be present");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(title, vo.getTitle(), "Title should match input");
        assertEquals(content, vo.getContent(), "Content should match input");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "Type should be NORMAL_TEXT");
        assertEquals(ViewComponentTypeEnum.PREFIX_DOT.getType(), vo.getPrefix(), "Prefix should be PREFIX_DOT");
    }

    @Test
    void testBuildTitleAndContentWithPrefix_WhenTitleIsNull_ShouldReturnVOWithNullTitle() throws Throwable {
        // arrange
        String title = null;
        String content = "Test Content";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContentWithPrefix(title, content);
        // assert
        assertTrue(result.isPresent(), "Result should be present");
        DealDetailStructuredDetailVO vo = result.get();
        assertNull(vo.getTitle(), "Title should be null");
        assertEquals(content, vo.getContent(), "Content should match input");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "Type should be NORMAL_TEXT");
        assertEquals(ViewComponentTypeEnum.PREFIX_DOT.getType(), vo.getPrefix(), "Prefix should be PREFIX_DOT");
    }

    @Test
    void testBuildTitleAndContentWithPrefix_WhenContentIsNull_ShouldReturnVOWithNullContent() throws Throwable {
        // arrange
        String title = "Test Title";
        String content = null;
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContentWithPrefix(title, content);
        // assert
        assertTrue(result.isPresent(), "Result should be present");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(title, vo.getTitle(), "Title should match input");
        assertNull(vo.getContent(), "Content should be null");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "Type should be NORMAL_TEXT");
        assertEquals(ViewComponentTypeEnum.PREFIX_DOT.getType(), vo.getPrefix(), "Prefix should be PREFIX_DOT");
    }

    @Test
    void testBuildTitleAndContentWithPrefix_WhenTitleAndContentAreNull_ShouldReturnVOWithNullFields() throws Throwable {
        // arrange
        String title = null;
        String content = null;
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContentWithPrefix(title, content);
        // assert
        assertTrue(result.isPresent(), "Result should be present");
        DealDetailStructuredDetailVO vo = result.get();
        assertNull(vo.getTitle(), "Title should be null");
        assertNull(vo.getContent(), "Content should be null");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "Type should be NORMAL_TEXT");
        assertEquals(ViewComponentTypeEnum.PREFIX_DOT.getType(), vo.getPrefix(), "Prefix should be PREFIX_DOT");
    }

    @Test
    void testBuildTitleAndContentWithPrefix_WhenTitleAndContentAreEmpty_ShouldReturnVOWithEmptyFields() throws Throwable {
        // arrange
        String title = "";
        String content = "";
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContentWithPrefix(title, content);
        // assert
        assertTrue(result.isPresent(), "Result should be present");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(title, vo.getTitle(), "Title should be empty");
        assertEquals(content, vo.getContent(), "Content should be empty");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "Type should be NORMAL_TEXT");
        assertEquals(ViewComponentTypeEnum.PREFIX_DOT.getType(), vo.getPrefix(), "Prefix should be PREFIX_DOT");
    }

    @Test
    void testBuildTitleAndContentWithPrefix_WhenTitleAndContentAreVeryLong_ShouldReturnVOWithFullContent() throws Throwable {
        // arrange
        StringBuilder longStringBuilder = new StringBuilder();
        for (int i = 0; i < 100; i++) {
            longStringBuilder.append("This is a very long string ");
        }
        String title = longStringBuilder.toString();
        String content = longStringBuilder.toString();
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildTitleAndContentWithPrefix(title, content);
        // assert
        assertTrue(result.isPresent(), "Result should be present");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals(title, vo.getTitle(), "Title should match long input");
        assertEquals(content, vo.getContent(), "Content should match long input");
        assertEquals(ViewComponentTypeEnum.NORMAL_TEXT.getType(), vo.getType(), "Type should be NORMAL_TEXT");
        assertEquals(ViewComponentTypeEnum.PREFIX_DOT.getType(), vo.getPrefix(), "Prefix should be PREFIX_DOT");
    }

    @Test
    @DisplayName("当传入非空内容时，应正确构建VO对象")
    void testBuildLensContentJson_WhenContentIsValid_ShouldReturnCorrectVO() {
        // arrange
        final String expectedContent = "valid content";
        final int expectedType = ViewComponentTypeEnum.EXCEL.getType();
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildLensContentJson(expectedContent);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals(expectedContent, result.getContent(), "内容字段应匹配输入值");
        assertEquals(expectedType, result.getType(), "类型字段应为EXCEL类型");
    }

    @ParameterizedTest
    @NullAndEmptySource
    @DisplayName("当传入null或空内容时，应返回内容为空的VO对象")
    void testBuildLensContentJson_WhenContentIsNullOrEmpty_ShouldReturnVOWithEmptyContent(String content) {
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildLensContentJson(content);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals(content, result.getContent(), "内容字段应与输入一致");
        assertEquals(ViewComponentTypeEnum.EXCEL.getType(), result.getType(), "类型字段应为EXCEL类型");
    }

    @ParameterizedTest
    @ValueSource(strings = { "content1", "", "another content", " " })
    @DisplayName("无论传入什么内容，类型字段都应设置为EXCEL类型")
    void testBuildLensContentJson_TypeShouldAlwaysBeExcel(String content) {
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildLensContentJson(content);
        // assert
        assertEquals(ViewComponentTypeEnum.EXCEL.getType(), result.getType(), "类型字段应始终为EXCEL类型");
    }

    @Test
    void testBuildServiceProcessTitle_WhenPositiveDuration_ShouldReturnWithContent() {
        // arrange
        int duration = 30;
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildServiceProcessTitle(duration);
        // assert
        assertTrue(result.isPresent(), "结果应该存在");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals("服务流程", vo.getTitle(), "标题应该匹配");
        assertEquals("共30分钟", vo.getContent(), "内容应该包含正确的时长");
        assertEquals(ViewComponentTypeEnum.TITLE.getType(), vo.getType(), "类型应该匹配");
    }

    @Test
    void testBuildServiceProcessTitle_WhenZeroDuration_ShouldReturnWithoutContent() {
        // arrange
        int duration = 0;
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildServiceProcessTitle(duration);
        // assert
        assertTrue(result.isPresent(), "结果应该存在");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals("服务流程", vo.getTitle(), "标题应该匹配");
        assertNull(vo.getContent(), "内容应该为null");
        assertEquals(ViewComponentTypeEnum.TITLE.getType(), vo.getType(), "类型应该匹配");
    }

    @Test
    void testBuildServiceProcessTitle_WhenNegativeDuration_ShouldReturnWithoutContent() {
        // arrange
        int duration = -5;
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildServiceProcessTitle(duration);
        // assert
        assertTrue(result.isPresent(), "结果应该存在");
        DealDetailStructuredDetailVO vo = result.get();
        assertEquals("服务流程", vo.getTitle(), "标题应该匹配");
        assertNull(vo.getContent(), "内容应该为null");
        assertEquals(ViewComponentTypeEnum.TITLE.getType(), vo.getType(), "类型应该匹配");
    }

    @Test
    void testBuildServiceProcessTitle_VerifyBuilderPattern() {
        // arrange
        int duration = 15;
        DealDetailStructuredDetailVO mockVO = mock(DealDetailStructuredDetailVO.class);
        DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder mockBuilder = mock(DealDetailStructuredDetailVO.DealDetailStructuredDetailVOBuilder.class);
        // 这里我们只是演示如何验证builder模式调用，实际测试中不需要这样mock
        // 因为这会过度测试实现细节
        // act
        Optional<DealDetailStructuredDetailVO> result = DealDetailStructuredUtils.buildServiceProcessTitle(duration);
        // assert
        assertTrue(result.isPresent());
        // 实际测试中应该验证结果而不是实现细节
    }

    @Test
    public void testBuildCurrentAllLimiterReturnsNonNull() {
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildCurrentAllLimiter();
        // assert
        assertNotNull(result, "返回的DealDetailStructuredDetailVO对象不应为null");
    }

    @Test
    public void testBuildCurrentAllLimiterReturnsCorrectContent() {
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildCurrentAllLimiter();
        // assert
        assertEquals("showFold", result.getContent(), "content字段应为'showFold'");
    }

    @Test
    public void testBuildCurrentAllLimiterReturnsCorrectType() {
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildCurrentAllLimiter();
        // assert
        assertEquals(ViewComponentTypeEnum.DELIMITER.getType(), result.getType(), "type字段应为DetailModuleType.DELIMITER的类型值");
    }

    @Test
    public void testBuildCurrentAllLimiterBuilderPattern() {
        // arrange
        DealDetailStructuredDetailVO expected = DealDetailStructuredDetailVO.builder().content("showFold").type(ViewComponentTypeEnum.DELIMITER.getType()).build();
        // act
        DealDetailStructuredDetailVO actual = DealDetailStructuredUtils.buildCurrentAllLimiter();
        // assert
        assertEquals(expected.getContent(), actual.getContent(), "content字段应匹配");
        assertEquals(expected.getType(), actual.getType(), "type字段应匹配");
    }

    @Test
    public void testBuildCurrentAllLimiterOtherFields() {
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildCurrentAllLimiter();
        // assert
        assertNull(result.getItemId(), "itemId应为null");
        assertNull(result.getTitle(), "title应为null");
        assertNull(result.getJumpUrl(), "jumpUrl应为null");
        // 可以继续添加其他字段的断言
    }

    @Test
    void testBuildContentFromAttr_WithValidContent_ShouldReturnCorrectVO() throws Throwable {
        // arrange
        final String testContent = "Test Content";
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildContentFromAttr(testContent);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals(testContent, result.getContent(), "content字段应与输入一致");
        assertEquals(ViewComponentTypeEnum.KEY_INFO.getType(), result.getType(), "type字段应设置为KEY_INFO类型");
        // 验证其他字段为默认值
        assertNull(result.getTitle(), "title字段应为null");
        assertNull(result.getItemId(), "itemId字段应为null");
        assertNull(result.getJumpUrl(), "jumpUrl字段应为null");
    }

    @Test
    void testBuildContentFromAttr_WithEmptyContent_ShouldReturnVOWithEmptyContent() throws Throwable {
        // arrange
        final String emptyContent = "";
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildContentFromAttr(emptyContent);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals("", result.getContent(), "content字段应为空字符串");
        assertEquals(ViewComponentTypeEnum.KEY_INFO.getType(), result.getType(), "type字段应设置为KEY_INFO类型");
    }

    @Test
    void testBuildContentFromAttr_WithNullContent_ShouldReturnVOWithNullContent() throws Throwable {
        // arrange
        final String nullContent = null;
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildContentFromAttr(nullContent);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertNull(result.getContent(), "content字段应为null");
        assertEquals(ViewComponentTypeEnum.KEY_INFO.getType(), result.getType(), "type字段应设置为KEY_INFO类型");
    }

    @Test
    void testBuildContentFromAttr_ShouldReturnCompleteVO() throws Throwable {
        // arrange
        final String testContent = "Test Content";
        // act
        DealDetailStructuredDetailVO result = DealDetailStructuredUtils.buildContentFromAttr(testContent);
        // assert
        assertNotNull(result, "返回的VO对象不应为null");
        assertEquals(testContent, result.getContent(), "content字段应与输入一致");
        assertEquals(ViewComponentTypeEnum.KEY_INFO.getType(), result.getType(), "type字段应设置为KEY_INFO类型");
        // 验证其他字段的默认值
        assertNull(result.getContentColor(), "contentColor字段应为null");
        assertNull(result.getPrefix(), "prefix字段应为null");
        assertNull(result.getPopupData(), "popupData字段应为null");
        assertNull(result.getEndBackgroundColor(), "endBackgroundColor字段应为null");
        assertNull(result.getOrder(), "order字段应为null");
        assertNull(result.getSubContent(), "subContent字段应为null");
        assertNull(result.getIcon(), "icon字段应为null");
        assertNull(result.getBackgroundColor(), "backgroundColor字段应为null");
        assertNull(result.getDetail(), "detail字段应为null");
    }
}
