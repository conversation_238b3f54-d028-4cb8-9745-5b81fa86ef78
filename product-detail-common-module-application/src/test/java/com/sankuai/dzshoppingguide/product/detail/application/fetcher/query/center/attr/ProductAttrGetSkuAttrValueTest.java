package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for ProductAttr
 */
@ExtendWith(MockitoExtension.class)
class ProductAttrGetSkuAttrValueTest {

    private ProductAttr productAttr;

    private Map<String, AttrDTO> skuAttr;

    @BeforeEach
    void setUp() {
        skuAttr = new HashMap<>();
        productAttr = new ProductAttr(skuAttr);
    }

    /**
     * Test case: When attribute name does not exist in skuAttr map
     * Expected: Should return null
     */
    @Test
    void testGetSkuAttrValueWhenAttrNotExists() throws Throwable {
        // arrange
        String nonExistingAttr = "nonExistingAttr";
        // act
        List<String> result = productAttr.getSkuAttrValue(nonExistingAttr);
        // assert
        assertNull(result);
    }

    /**
     * Test case: When attribute exists with single value
     * Expected: Should return list with single value
     */
    @Test
    void testGetSkuAttrValueWithSingleValue() throws Throwable {
        // arrange
        String attrName = "testAttr";
        String expectedValue = "value1";
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList(expectedValue));
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(expectedValue, result.get(0));
    }

    /**
     * Test case: When attribute exists with multiple values
     * Expected: Should return list with all values
     */
    @Test
    void testGetSkuAttrValueWithMultipleValues() throws Throwable {
        // arrange
        String attrName = "testAttr";
        List<String> expectedValues = Arrays.asList("value1", "value2", "value3");
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(expectedValues);
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNotNull(result);
        assertEquals(expectedValues.size(), result.size());
        assertEquals(expectedValues, result);
    }

    /**
     * Test case: When attribute exists but has empty value list
     * Expected: Should return empty list
     */
    @Test
    void testGetSkuAttrValueWithEmptyList() throws Throwable {
        // arrange
        String attrName = "testAttr";
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.emptyList());
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: When attribute exists with null value list
     * Expected: Should return null
     */
    @Test
    void testGetSkuAttrValueWithNullValueList() throws Throwable {
        // arrange
        String attrName = "testAttr";
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(null);
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNull(result);
    }

    /**
     * Test case: When attribute exists with array-formatted string
     * Expected: Should return list with the array-formatted string
     */
    @Test
    void testGetSkuAttrValueWithArrayFormattedString() throws Throwable {
        // arrange
        String attrName = "testAttr";
        String arrayFormattedValue = "[\"value1\",\"value2\"]";
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList(arrayFormattedValue));
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(arrayFormattedValue, result.get(0));
    }

    /**
     * Test case when attribute value is an empty list
     */
    @Test
    public void testGetSkuAttrValueWhenValueIsEmpty() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.emptyList());
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        List<String> result = productAttr.getSkuAttrValue("testAttr");
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithValidValue() throws Throwable {
        // arrange
        String testKey = "validKey";
        String expectedValue = "testValue";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList(expectedValue));
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(expectedValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithEmptyValue() throws Throwable {
        // arrange
        String testKey = "emptyKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList(""));
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithNullValue() throws Throwable {
        // arrange
        String testKey = "nullKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList(null));
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithEmptyList() throws Throwable {
        // arrange
        String testKey = "emptyListKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Collections.emptyList());
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithNullList() throws Throwable {
        // arrange
        String testKey = "nullListKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(null);
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyDoesNotExist() throws Throwable {
        // arrange
        String testKey = "nonExistentKey";
        String defaultValue = "default";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    @Test
    public void testGetSafeStringWhenKeyIsNull() throws Throwable {
        // arrange
        String defaultValue = "default";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(null, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    @Test
    public void testGetSafeStringWhenDefaultValueIsNull() throws Throwable {
        // arrange
        String testKey = "anyKey";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, null);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetSafeStringWhenMultipleValuesFirstEmpty() throws Throwable {
        // arrange
        String testKey = "multiValueKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Arrays.asList("", "secondValue"));
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }
}
