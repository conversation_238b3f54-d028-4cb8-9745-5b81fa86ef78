package com.sankuai.dzshoppingguide.product.detail.application.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.JSONSerializer;
import com.alibaba.fastjson.serializer.PropertyPreFilter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dz.product.detail.page.low.code.engine.factory.GroovyRuleEngine;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinitionStorage;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.facial.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductStandardServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.FileUtil;
import com.sankuai.dzshoppingguide.product.detail.application.utils.Func1;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import groovy.lang.Binding;
import groovy.lang.GroovyShell;
import groovy.lang.Script;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.FileUtils;
import org.codehaus.groovy.control.CompilerConfiguration;
import org.jetbrains.annotations.NotNull;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import static org.mockito.Mockito.mockStatic;

@RunWith(MockitoJUnitRunner.class)
public class RuleEngineLocalTest extends RuleEngineBaseTest {

    private MockedStatic<ComponentDefinitionStorage> componentDefinitionStorageMockedStatic;
    // 创建 Groovy Shell
    private static final GroovyShell groovyShell = new GroovyShell(new CompilerConfiguration());

    @Before
    public void setUp() {
        // 初始化为null，在每个测试中按需初始化
        componentDefinitionStorageMockedStatic = null;
    }

    @Ignore
    @Test
    public void test_batchStyleGroovyConfig() throws Exception {
        try (MockedStatic<ComponentDefinitionStorage> mocked = mockStatic(ComponentDefinitionStorage.class)) {
            componentDefinitionStorageMockedStatic = mocked;
            componentDefinitionStorageMockedStatic.when(() -> ComponentDefinitionStorage.getComponentMetadata(Mockito.anyString())).thenReturn(null);

            DealDetailBuildContext context = initContext();
            Map<String, Object> env = new HashMap<>();
            env.put("context", context);
            // 加载Groovy脚本
            String groovyFileContext = FileUtils.readFileToString(FileUtil.getFile("BatchStyleGroovyConfig.groovy"), "UTF-8");

            // 注册自定义函数

            GroovyRuleEngine groovyRuleEngine = GroovyRuleEngine.getInstance();

            // 验证两种方法的结果是否一致
            Object groovyResult = groovyRuleEngine.execute(groovyFileContext, env);

            System.out.println("Groovy结果: " +  toJSONString(groovyResult));
        }
    }


    @Ignore
    @Test
    public void test_batchStyleGroovyConfig_file() throws Exception {

        try (MockedStatic<ComponentDefinitionStorage> mocked = mockStatic(ComponentDefinitionStorage.class)) {
            componentDefinitionStorageMockedStatic = mocked;
            componentDefinitionStorageMockedStatic.when(() -> ComponentDefinitionStorage.getComponentMetadata(Mockito.anyString())).thenReturn(null);

            // 加载测试数据
            DealDetailBuildContext context = initContext();
            Map<String, Object> env = new HashMap<>();
            env.put("context", context);

            // 加载Groovy脚本
            File groovyFile = FileUtil.getFile("BatchStyleGroovyConfig.groovy");
            Script groovyScript = groovyShell.parse(groovyFile);
            // 准备Groovy绑定
            Binding binding = new Binding(env);
            groovyScript.setBinding(binding);

            Object groovyResult = groovyScript.run();

            System.out.println("Groovy结果: " +  toJSONString(groovyResult));
        }
    }

    @Ignore
    @Test
    public void test_batchStyleGroovyAllConfig() throws Exception {

        try (MockedStatic<ComponentDefinitionStorage> mocked = mockStatic(ComponentDefinitionStorage.class)) {
            componentDefinitionStorageMockedStatic = mocked;
            componentDefinitionStorageMockedStatic.when(() -> ComponentDefinitionStorage.getComponentMetadata(Mockito.anyString())).thenReturn(null);

            DealDetailBuildContext context = initContext();
            Map<String, Object> env = new HashMap<>();
            env.put("context", context);
            // 加载Groovy脚本
            String groovyFileContext = FileUtils.readFileToString(FileUtil.getFile("BatchStyleGroovyAllConfig.groovy"), "UTF-8");

            // 注册自定义函数

            GroovyRuleEngine groovyRuleEngine = GroovyRuleEngine.getInstance();

            // 验证两种方法的结果是否一致
            Object groovyResult = groovyRuleEngine.execute(groovyFileContext, env);

            System.out.println("Groovy结果: " +  toJSONString(groovyResult));
        }
    }

    @Ignore
    @Test
    public void test_batchStyleGroovyAllConfig_file() throws Exception {

        try (MockedStatic<ComponentDefinitionStorage> mocked = mockStatic(ComponentDefinitionStorage.class)) {
            componentDefinitionStorageMockedStatic = mocked;
            componentDefinitionStorageMockedStatic.when(() -> ComponentDefinitionStorage.getComponentMetadata(Mockito.anyString())).thenReturn(null);

            // 加载测试数据
            DealDetailBuildContext context = initContext();
            Map<String, Object> env = new HashMap<>();
            env.put("context", context);

            // 加载Groovy脚本
            File groovyFile = FileUtil.getFile("BatchStyleGroovyAllConfig.groovy");
            Script groovyScript = groovyShell.parse(groovyFile);
            // 准备Groovy绑定
            Binding binding = new Binding(env);
            groovyScript.setBinding(binding);

            Object groovyResult = groovyScript.run();
            System.out.println("Groovy结果: " +  toJSONString(groovyResult));
        }
    }

    private static DealDetailBuildContext initContext() {
        String s = FileUtil.file2str("product-massage.json");
        String string = JSON.parseObject(s).getJSONObject("data").getString("list");
        DealGroupDTO dealGroupDTO = JSON.parseArray(string, DealGroupDTO.class).get(0);

        // 准备环境变量
        Map<String, AttrDTO> attrMap = Optional.ofNullable(dealGroupDTO)
                .map(DealGroupDTO::getAttrs)
                .orElse(new ArrayList<>()).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        AttrDTO::getName,
                        v -> v,
                        (v1, v2) -> v1
                ));
        ProductAttr productAttr = new ProductAttr(attrMap);
        ProductCategory productCategory = new ProductCategory(1, dealGroupDTO.getCategory().getCategoryId().intValue(), dealGroupDTO.getCategory().getServiceTypeId().intValue());
        ProductServiceProject productServiceProject = new ProductServiceProject(dealGroupDTO.getServiceProject());
        ProductStandardServiceProject productStandardServiceProject = new ProductStandardServiceProject(dealGroupDTO.getStandardServiceProject());
        AdditionInfoResult additionInfoResult = null;
        Map<String, String> originDetails = dealGroupDTO.getOriginDetails();
        if (MapUtils.isNotEmpty(originDetails)) {
            String detailInfo = originDetails.get("detailInfo");
            additionInfoResult = new AdditionInfoResult(detailInfo);
        }

        DealDetailBuildContext context = DealDetailBuildContext.builder()
                .productAttr(productAttr)
                .productCategory(productCategory)
                .additionInfo(additionInfoResult)
                .productServiceProject(productServiceProject)
                .productStandardServiceProject(productStandardServiceProject)
                .build();
        return context;
    }

}