package com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.components.additioninfo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructContentModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.additional.UniformStructModel;
import com.sankuai.dzshoppingguide.product.detail.application.builder.structureddetail.standard.composite.base.DetailComponentParam;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.additioninfo.AdditionInfoResult;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.newstructureddetail.enums.ViewComponentTypeEnum;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test class for AdditionInfoComponet
 */
@RunWith(MockitoJUnitRunner.class)
public class AdditionInfoComponetTest {

    @InjectMocks
    private AdditionInfoComponet additionInfoComponet;

    /**
     * Test when param is null
     */
    @Test
    public void testBuildWhenParamIsNull() throws Throwable {
        // arrange
        DetailComponentParam param = DetailComponentParam.builder().build();
        // act
        List<DealDetailStructuredDetailVO> result = additionInfoComponet.build(param);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when AdditionInfo is null
     */
    @Test
    public void testBuildWhenAdditionInfoIsNull() throws Throwable {
        // arrange
        DetailComponentParam param = DetailComponentParam.builder().additionInfo(null).build();
        // act
        List<DealDetailStructuredDetailVO> result = additionInfoComponet.build(param);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when AdditionInfo.getAdditionInfo() returns empty string
     */
    @Test
    public void testBuildWhenAdditionInfoContentIsEmpty() throws Throwable {
        // arrange
        AdditionInfoResult additionInfo = new AdditionInfoResult();
        additionInfo.setAdditionInfo("");
        DetailComponentParam param = DetailComponentParam.builder().additionInfo(additionInfo).build();
        // act
        List<DealDetailStructuredDetailVO> result = additionInfoComponet.build(param);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when AdditionInfo.getAdditionInfo() returns whitespace
     */
    @Test
    public void testBuildWhenAdditionInfoContentIsWhitespace() throws Throwable {
        // arrange
        AdditionInfoResult additionInfo = new AdditionInfoResult();
        additionInfo.setAdditionInfo("   ");
        DetailComponentParam param = DetailComponentParam.builder().additionInfo(additionInfo).build();
        // act
        List<DealDetailStructuredDetailVO> result = additionInfoComponet.build(param);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when AdditionInfo.getAdditionInfo() returns null
     */
    @Test
    public void testBuildWhenAdditionInfoContentIsNull() throws Throwable {
        // arrange
        AdditionInfoResult additionInfo = new AdditionInfoResult();
        additionInfo.setAdditionInfo(null);
        DetailComponentParam param = DetailComponentParam.builder().additionInfo(additionInfo).build();
        // act
        List<DealDetailStructuredDetailVO> result = additionInfoComponet.build(param);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
