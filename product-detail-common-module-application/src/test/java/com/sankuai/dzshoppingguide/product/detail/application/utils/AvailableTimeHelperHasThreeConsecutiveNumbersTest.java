package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DealGroupUseRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DisableDateDTO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.junit.jupiter.api.Assertions.assertEquals;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.dto.FetcherResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.AvailableTimeStrategy;
import com.sankuai.dzshoppingguide.product.detail.application.builder.reminder.strategy.availabletime.AvailableTimeStrategyFactory;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import com.dianping.lion.client.Lion;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.CycleAvailableDateDTO;

@ExtendWith(MockitoExtension.class)
class AvailableTimeHelperHasThreeConsecutiveNumbersTest {

    @Mock
    private ProductBaseInfo baseInfo;

    @Mock
    private FetcherResultDTO fetcherResults;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private ApplicationContext mockApplicationContext;

    @Mock
    private AvailableTimeStrategyFactory mockStrategyFactory;

    private static final int TEST_CATEGORY_ID = 123;

    /**
     * 测试空列表情况，应返回false
     */
    @Test
    void testHasThreeConsecutiveNumbers_EmptyList_ReturnsFalse() throws Throwable {
        // arrange
        List<Integer> emptyList = Collections.emptyList();
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(emptyList);
        // assert
        assertFalse(result, "空列表应返回false");
    }

    /**
     * 测试元素少于3个的情况，应返回false
     */
    @Test
    void testHasThreeConsecutiveNumbers_LessThanThreeElements_ReturnsFalse() throws Throwable {
        // arrange
        List<Integer> shortList = Arrays.asList(1, 2);
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(shortList);
        // assert
        assertFalse(result, "元素少于3个应返回false");
    }

    /**
     * 测试有3个连续数字的情况，应返回true
     */
    @Test
    void testHasThreeConsecutiveNumbers_ExactlyThreeConsecutive_ReturnsTrue() throws Throwable {
        // arrange
        List<Integer> numbers = Arrays.asList(1, 2, 3, 5);
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(numbers);
        // assert
        assertTrue(result, "有3个连续数字应返回true");
    }

    /**
     * 测试有超过3个连续数字的情况，应返回true
     */
    @Test
    void testHasThreeConsecutiveNumbers_MoreThanThreeConsecutive_ReturnsTrue() throws Throwable {
        // arrange
        List<Integer> numbers = Arrays.asList(1, 2, 3, 4, 6);
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(numbers);
        // assert
        assertTrue(result, "有超过3个连续数字应返回true");
    }

    /**
     * 测试没有3个连续数字的情况，应返回false
     */
    @Test
    void testHasThreeConsecutiveNumbers_NoConsecutive_ReturnsFalse() throws Throwable {
        // arrange
        List<Integer> numbers = Arrays.asList(1, 3, 5, 7);
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(numbers);
        // assert
        assertFalse(result, "没有3个连续数字应返回false");
    }

    /**
     * 测试数字重复的情况，应正确处理
     */
    @Test
    void testHasThreeConsecutiveNumbers_WithDuplicates_ReturnsTrue() throws Throwable {
        // arrange
        List<Integer> numbers = Arrays.asList(1, 1, 2, 3);
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(numbers);
        // assert
        assertTrue(result, "重复数字但满足连续条件应返回true");
    }

    /**
     * 测试非连续但有部分连续的情况，应返回false
     */
    @Test
    void testHasThreeConsecutiveNumbers_PartialConsecutive_ReturnsFalse() throws Throwable {
        // arrange
        List<Integer> numbers = Arrays.asList(1, 2, 4, 5);
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(numbers);
        // assert
        assertFalse(result, "只有部分连续应返回false");
    }

    /**
     * 测试列表为null的情况，应返回false
     */
    @Test
    void testHasThreeConsecutiveNumbers_NullList_ReturnsFalse() throws Throwable {
        // arrange
        List<Integer> nullList = null;
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(nullList);
        // assert
        assertFalse(result, "null列表应返回false");
    }

    /**
     * 测试乱序数字的情况，应返回true
     */
    @Test
    void testHasThreeConsecutiveNumbers_UnorderedNumbers_ReturnsTrue() throws Throwable {
        // arrange
        List<Integer> numbers = Arrays.asList(5, 2, 4, 3);
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(numbers);
        // assert
        assertTrue(result, "乱序但包含连续数字应返回true");
    }

    /**
     * 测试使用ArrayList的情况
     */
    @Test
    void testHasThreeConsecutiveNumbers_ArrayList_ReturnsExpected() throws Throwable {
        // arrange
        List<Integer> numbers = new ArrayList<>(Arrays.asList(1, 2, 3, 5));
        // act
        boolean result = AvailableTimeHelper.hasThreeConsecutiveNumbers(numbers);
        // assert
        assertTrue(result, "ArrayList包含连续数字应返回true");
    }

    @Test
    public void testAllTimePeriodReminder_NullContents() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = null;
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        // act & assert
        assertThrows(NullPointerException.class, () -> AvailableTimeHelper.allTimePeriodReminder(contents, baseInfo));
    }

    @Test
    public void testAllTimePeriodReminder_NullBaseInfo() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        // act
        AvailableTimeHelper.allTimePeriodReminder(contents, null);
        // assert
        assertEquals(1, contents.size());
        assertEquals("周一至周日全天可用", contents.get(0).getText());
        assertEquals("12", contents.get(0).getFontSize());
        assertEquals("#555555", contents.get(0).getFontColor());
    }

    @Test
    public void testAllTimePeriodReminder_NoDisableDays() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO ruleDTO = new DealGroupRuleDTO();
        DealGroupUseRuleDTO useRuleDTO = new DealGroupUseRuleDTO();
        DisableDateDTO disableDateDTO = new DisableDateDTO();
        useRuleDTO.setDisableDate(disableDateDTO);
        ruleDTO.setUseRule(useRuleDTO);
        when(baseInfo.getRule()).thenReturn(ruleDTO);
        // act
        AvailableTimeHelper.allTimePeriodReminder(contents, baseInfo);
        // assert
        assertEquals(1, contents.size());
        assertEquals("周一至周日全天可用", contents.get(0).getText());
        assertEquals("12", contents.get(0).getFontSize());
        assertEquals("#555555", contents.get(0).getFontColor());
    }

    @Test
    public void testAllTimePeriodReminder_WithDisableDateRanges() throws Throwable {
        // arrange
        List<GuaranteeInstructionsContentVO> contents = new ArrayList<>();
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO ruleDTO = new DealGroupRuleDTO();
        DealGroupUseRuleDTO useRuleDTO = new DealGroupUseRuleDTO();
        DisableDateDTO disableDateDTO = new DisableDateDTO();
        List<DateRangeDTO> dateRanges = new ArrayList<>();
        DateRangeDTO dateRange = new DateRangeDTO();
        dateRange.setFrom("2023-01-01");
        dateRange.setTo("2023-01-02");
        dateRanges.add(dateRange);
        disableDateDTO.setDisableDateRangeDTOS(dateRanges);
        useRuleDTO.setDisableDate(disableDateDTO);
        ruleDTO.setUseRule(useRuleDTO);
        when(baseInfo.getRule()).thenReturn(ruleDTO);
        // act
        AvailableTimeHelper.allTimePeriodReminder(contents, baseInfo);
        // assert
        assertEquals(1, contents.size());
        assertEquals("部分日期全天可用", contents.get(0).getText());
        assertEquals("12", contents.get(0).getFontSize());
        assertEquals("#555555", contents.get(0).getFontColor());
    }

    @Test
    public void testHasDisableDaysWhenBaseInfoIsNull() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = null;
        // act
        boolean result = AvailableTimeHelper.hasDisableDays(baseInfo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHasDisableDaysWhenRuleIsNull() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        when(baseInfo.getRule()).thenReturn(null);
        // act
        boolean result = AvailableTimeHelper.hasDisableDays(baseInfo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHasDisableDaysWhenUseRuleIsNull() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(null);
        // act
        boolean result = AvailableTimeHelper.hasDisableDays(baseInfo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHasDisableDaysWhenDisableDateIsNull() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(null);
        // act
        boolean result = AvailableTimeHelper.hasDisableDays(baseInfo);
        // assert
        assertFalse(result);
    }
    @Test
    public void testHasDisableDaysWhenDisableDateRangeDTOSIsNotEmpty() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        DisableDateDTO disableDate = mock(DisableDateDTO.class);
        DateRangeDTO dateRange = mock(DateRangeDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        when(disableDate.getDisableDays()).thenReturn(Collections.emptyList());
        when(disableDate.getDisableDateRangeDTOS()).thenReturn(Arrays.asList(dateRange));
        // act
        boolean result = AvailableTimeHelper.hasDisableDays(baseInfo);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHasDisableDaysWhenBothListsAreNotEmpty() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        DisableDateDTO disableDate = mock(DisableDateDTO.class);
        DateRangeDTO dateRange = mock(DateRangeDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        when(disableDate.getDisableDays()).thenReturn(Arrays.asList(1, 2, 3));
        when(disableDate.getDisableDateRangeDTOS()).thenReturn(Arrays.asList(dateRange));
        // act
        boolean result = AvailableTimeHelper.hasDisableDays(baseInfo);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHasDisableDaysWhenBothListsAreEmpty() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        DisableDateDTO disableDate = mock(DisableDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        when(disableDate.getDisableDays()).thenReturn(Collections.emptyList());
        when(disableDate.getDisableDateRangeDTOS()).thenReturn(Collections.emptyList());
        // act
        boolean result = AvailableTimeHelper.hasDisableDays(baseInfo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testGetDisableDaysWhenBaseInfoIsNull() {
        // arrange
        ProductBaseInfo baseInfo = null;
        // act
        List<Integer> result = AvailableTimeHelper.getDisableDays(baseInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDisableDaysWhenRuleIsNull() {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        when(baseInfo.getRule()).thenReturn(null);
        // act
        List<Integer> result = AvailableTimeHelper.getDisableDays(baseInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDisableDaysWhenUseRuleIsNull() {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(null);
        // act
        List<Integer> result = AvailableTimeHelper.getDisableDays(baseInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDisableDaysWhenDisableDateIsNull() {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(null);
        // act
        List<Integer> result = AvailableTimeHelper.getDisableDays(baseInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDisableDaysWhenDisableDaysIsNull() {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        DisableDateDTO disableDate = mock(DisableDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        when(disableDate.getDisableDays()).thenReturn(null);
        // act
        List<Integer> result = AvailableTimeHelper.getDisableDays(baseInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDisableDaysWhenDisableDaysIsEmpty() {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        DisableDateDTO disableDate = mock(DisableDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        when(disableDate.getDisableDays()).thenReturn(Collections.emptyList());
        // act
        List<Integer> result = AvailableTimeHelper.getDisableDays(baseInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetDisableDaysWhenDisableDaysHasValues() {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        DisableDateDTO disableDate = mock(DisableDateDTO.class);
        List<Integer> expected = Arrays.asList(1, 2, 3);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        when(disableDate.getDisableDays()).thenReturn(expected);
        // act
        List<Integer> result = AvailableTimeHelper.getDisableDays(baseInfo);
        // assert
        assertNotNull(result);
        assertEquals(expected, result);
    }

    @Test
    public void testGetWeekDay_WhenInputIsEmpty_ThenReturnEmptyString() {
        // arrange
        String input = "";
        // act
        String result = AvailableTimeHelper.getWeekDay(input);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetWeekDay_WhenInputIsNotNumeric_ThenReturnEmptyString() {
        // arrange
        String input = "abc";
        // act
        String result = AvailableTimeHelper.getWeekDay(input);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetWeekDay_WhenInputIsLessThan1_ThenReturnEmptyString() {
        // arrange
        String input = "0";
        // act
        String result = AvailableTimeHelper.getWeekDay(input);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetWeekDay_WhenInputIsGreaterThan7_ThenReturnEmptyString() {
        // arrange
        String input = "8";
        // act
        String result = AvailableTimeHelper.getWeekDay(input);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetWeekDay_WhenInputIsValid_ThenReturnCorrectWeekday() {
        // arrange
        String input1 = "1";
        String input2 = "3";
        String input3 = "7";
        // act
        String result1 = AvailableTimeHelper.getWeekDay(input1);
        String result2 = AvailableTimeHelper.getWeekDay(input2);
        String result3 = AvailableTimeHelper.getWeekDay(input3);
        // assert
        assertEquals("周一", result1);
        assertEquals("周三", result2);
        assertEquals("周日", result3);
    }

    @Test
    public void testGetWeekDay_WhenInputHasWhitespace_ThenReturnCorrectWeekday() {
        // arrange
        String input = " 5 ";
        // act
        String result = AvailableTimeHelper.getWeekDay(input);
        // assert
        assertEquals("周五", result);
    }

    @Test
    void testGetWeekDayInt_EmptyList_ReturnsEmptyList() {
        // arrange
        List<String> input = Collections.emptyList();
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertTrue(result.isEmpty(), "空列表输入应返回空列表");
    }

    @Test
    void testGetWeekDayInt_NullInput_ReturnsEmptyList() {
        // arrange
        List<String> input = null;
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertTrue(result.isEmpty(), "null输入应返回空列表");
    }

    @Test
    void testGetWeekDayInt_WithBlankStrings_FiltersOutBlanks() {
        // arrange
        List<String> input = Arrays.asList("", " ", "  ", "1", "2");
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertEquals(2, result.size(), "应过滤掉空白字符串");
        assertIterableEquals(Arrays.asList(1, 2), result, "应只保留有效数字");
    }

    @Test
    void testGetWeekDayInt_WithNonNumericStrings_FiltersOutNonNumbers() {
        // arrange
        List<String> input = Arrays.asList("Monday", "Tuesday", "3", "4", "abc");
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertEquals(2, result.size(), "应过滤掉非数字字符串");
        assertIterableEquals(Arrays.asList(3, 4), result, "应只保留数字字符串");
    }

    @Test
    void testGetWeekDayInt_WithOutOfRangeNumbers_FiltersOutInvalidNumbers() {
        // arrange
        List<String> input = Arrays.asList("0", "1", "7", "8", "10");
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertEquals(2, result.size(), "应过滤掉超出1-7范围的数字");
        assertIterableEquals(Arrays.asList(1, 7), result, "应只保留1-7范围内的数字");
    }

    @Test
    void testGetWeekDayInt_WithAllValidNumbers_ReturnsAllNumbers() {
        // arrange
        List<String> input = Arrays.asList("1", "2", "3", "4", "5", "6", "7");
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertEquals(7, result.size(), "应保留所有有效数字");
        assertIterableEquals(Arrays.asList(1, 2, 3, 4, 5, 6, 7), result, "应包含所有1-7的数字");
    }

    @Test
    void testGetWeekDayInt_WithMixedInput_FiltersCorrectly() {
        // arrange
        List<String> input = Arrays.asList("", "1", "abc", "8", "3", " ", "7");
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertEquals(3, result.size(), "应正确过滤混合输入");
        assertIterableEquals(Arrays.asList(1, 3, 7), result, "应只保留有效的数字");
    }

    @Test
    void testGetWeekDayInt_WithConsecutiveNumbers_ReturnsCorrectOrder() {
        // arrange
        List<String> input = Arrays.asList("1", "2", "3", "4");
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertEquals(4, result.size(), "应保留所有连续数字");
        assertIterableEquals(Arrays.asList(1, 2, 3, 4), result, "应保持原始顺序");
    }

    @Test
    void testGetWeekDayInt_WithNonConsecutiveNumbers_ReturnsCorrectOrder() {
        // arrange
        List<String> input = Arrays.asList("1", "3", "5", "7");
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertEquals(4, result.size(), "应保留所有有效数字");
        assertIterableEquals(Arrays.asList(1, 3, 5, 7), result, "应保持原始顺序");
    }

    @Test
    void testGetWeekDayInt_WithTrimmedNumbers_ReturnsCorrectNumbers() {
        // arrange
        List<String> input = Arrays.asList(" 1 ", " 2", "3 ", " 4 ");
        // act
        List<Integer> result = AvailableTimeHelper.getWeekDayInt(input);
        // assert
        assertEquals(4, result.size(), "应正确处理带空格的数字");
        assertIterableEquals(Arrays.asList(1, 2, 3, 4), result, "应正确去除空格");
    }

    @Test
    public void testGetTimeOfDayDocWhenProductAttrIsNull() throws Throwable {
        // arrange
        when(fetcherResults.getProductAttr()).thenReturn(null);
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(fetcherResults);
        // assert
        assertEquals(AvailableTimeHelper.ALL_DAY, result);
    }

    @Test
    public void testGetTimeOfDayDocWhenAvailableTimeIsEmpty() throws Throwable {
        // arrange
        when(fetcherResults.getProductAttr()).thenReturn(productAttr);
        when(productAttr.getSkuAttrList()).thenReturn(Collections.emptyList());
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(fetcherResults);
        // assert
        assertEquals(AvailableTimeHelper.ALL_DAY, result);
    }

    @Test
    public void testGetTimeOfDayDocWhenInvalidTimeFormat() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("available_time");
        attrDTO.setValue(Arrays.asList("invalid-time-format"));
        when(fetcherResults.getProductAttr()).thenReturn(productAttr);
        when(productAttr.getSkuAttrList()).thenReturn(Lists.newArrayList(attrDTO));
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(fetcherResults);
        // assert
        assertEquals(AvailableTimeHelper.PARTIAL_TIME, result);
    }

    @Test
    public void testGetTimeOfDayDocWhenValidTimeRanges() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("available_time");
        attrDTO.setValue(Arrays.asList("09:00-12:00", "11:00-18:00"));
        when(fetcherResults.getProductAttr()).thenReturn(productAttr);
        when(productAttr.getSkuAttrList()).thenReturn(Lists.newArrayList(attrDTO));
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(fetcherResults);
        // assert
        assertEquals("09:00-18:00", result);
    }

    @Test
    public void testGetTimeOfDayDocWhenFullDayTimeRange() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("available_time");
        attrDTO.setValue(Arrays.asList("00:00-23:59"));
        when(fetcherResults.getProductAttr()).thenReturn(productAttr);
        when(productAttr.getSkuAttrList()).thenReturn(Lists.newArrayList(attrDTO));
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(fetcherResults);
        // assert
        assertEquals(AvailableTimeHelper.ALL_DAY, result);
    }

    @Test
    public void testGetTimeOfDayDocWhenNonOverlappingTimeRanges() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("available_time");
        attrDTO.setValue(Arrays.asList("09:00-12:00", "14:00-18:00"));
        when(fetcherResults.getProductAttr()).thenReturn(productAttr);
        when(productAttr.getSkuAttrList()).thenReturn(Lists.newArrayList(attrDTO));
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(fetcherResults);
        // assert
        assertEquals(AvailableTimeHelper.PARTIAL_TIME, result);
    }

    @Test
    void testGetTimeDoc_WhenEmptyArray_ThenReturnPartialTime() throws Throwable {
        // arrange
        String[] input = new String[0];
        // act
        String result = AvailableTimeHelper.getTimeDoc(input);
        // assert
        assertEquals(AvailableTimeHelper.PARTIAL_TIME, result, "空数组应返回'部分时段'");
    }

    @Test
    void testGetTimeDoc_WhenAllDayTime_ThenReturnAllDay() throws Throwable {
        // arrange
        String[] input = { "00:00", "23:59" };
        // act
        String result = AvailableTimeHelper.getTimeDoc(input);
        // assert
        assertEquals(AvailableTimeHelper.ALL_DAY, result, "00:00-23:59应返回'全天'");
    }

    @Test
    void testGetTimeDoc_WhenPartialTime_ThenReturnFormattedTimeRange() throws Throwable {
        // arrange
        String[] input = { "09:00", "18:00" };
        String expected = "09:00-18:00";
        // act
        String result = AvailableTimeHelper.getTimeDoc(input);
        // assert
        assertEquals(expected, result, "应正确格式化时间范围");
    }

    @Test
    void testGetTimeDoc_WhenSingleElementArray_ThenThrowException() throws Throwable {
        // arrange
        String[] input = { "09:00" };
        // act & assert
        assertThrows(ArrayIndexOutOfBoundsException.class, () -> AvailableTimeHelper.getTimeDoc(input), "长度为1的数组应抛出ArrayIndexOutOfBoundsException");
    }

    @Test
    void testGetTimeDoc_WhenNullInput_ThenThrowNullPointerException() throws Throwable {
        // arrange
        String[] input = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> AvailableTimeHelper.getTimeDoc(input), "null输入应抛出NullPointerException");
    }

    @Test
    void testGetTimeDoc_WhenBoundaryButNotAllDay_ThenReturnFormattedTime() throws Throwable {
        // arrange
        String[] input1 = { "00:00", "18:00" };
        String[] input2 = { "09:00", "23:59" };
        // act & assert
        assertEquals("00:00-18:00", AvailableTimeHelper.getTimeDoc(input1), "应正确格式化00:00-18:00");
        assertEquals("09:00-23:59", AvailableTimeHelper.getTimeDoc(input2), "应正确格式化09:00-23:59");
    }

    @Test
    @DisplayName("Test standard time format")
    public void testCheckTimeOfDayFormatStandardFormat() throws Throwable {
        // arrange
        String timeStr = "09:30-18:00";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertTrue(result, "Standard time format should be accepted");
    }

    @Test
    @DisplayName("Test minimum time boundary")
    public void testCheckTimeOfDayFormatMinBoundary() throws Throwable {
        // arrange
        String timeStr = "00:00-00:00";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertTrue(result, "Minimum time boundary should be accepted");
    }

    @Test
    @DisplayName("Test maximum time boundary")
    public void testCheckTimeOfDayFormatMaxBoundary() throws Throwable {
        // arrange
        String timeStr = "23:59-23:59";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertTrue(result, "Maximum time boundary should be accepted");
    }

    @Test
    @DisplayName("Test hours out of range")
    public void testCheckTimeOfDayFormatInvalidHours() throws Throwable {
        // arrange
        String timeStr = "24:00-18:00";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertFalse(result, "Hours out of range should be rejected");
    }

    @Test
    @DisplayName("Test minutes out of range")
    public void testCheckTimeOfDayFormatInvalidMinutes() throws Throwable {
        // arrange
        String timeStr = "12:60-18:00";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertFalse(result, "Minutes out of range should be rejected");
    }

    @Test
    @DisplayName("Test invalid format")
    public void testCheckTimeOfDayFormatInvalidFormat() throws Throwable {
        // arrange
        String timeStr = "12:00_18:00";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertFalse(result, "Invalid format should be rejected");
    }

    @Test
    @DisplayName("Test empty string")
    public void testCheckTimeOfDayFormatEmptyString() throws Throwable {
        // arrange
        String timeStr = "";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertFalse(result, "Empty string should be rejected");
    }

    @Test
    @DisplayName("Test null input")
    public void testCheckTimeOfDayFormatNullInput() throws Throwable {
        // arrange
        String timeStr = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> AvailableTimeHelper.checkTimeOfDayFormat(timeStr), "Null input should throw NullPointerException");
    }

    @Test
    @DisplayName("Test time format with spaces")
    public void testCheckTimeOfDayFormatWithSpaces() throws Throwable {
        // arrange
        String timeStr = " 09:00-18:00 ";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertFalse(result, "Time format with spaces should be rejected");
    }

    @Test
    @DisplayName("Test incomplete time format")
    public void testCheckTimeOfDayFormatIncomplete() throws Throwable {
        // arrange
        String timeStr = "09:00-";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertFalse(result, "Incomplete time format should be rejected");
    }

    @Test
    @DisplayName("Test cross-day time format")
    public void testCheckTimeOfDayFormatCrossDay() throws Throwable {
        // arrange
        String timeStr = "23:00-01:00";
        // act
        boolean result = AvailableTimeHelper.checkTimeOfDayFormat(timeStr);
        // assert
        assertTrue(result, "Cross-day time format should be accepted");
    }

    @Test
    public void testGetTimeOfDayDoc_EmptyInput_ReturnsEmptyString() throws Throwable {
        // arrange
        String originTime = null;
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeOfDayDoc_InvalidFormat_ReturnsEmptyString() throws Throwable {
        // arrange
        String originTime = "12";
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeOfDayDoc_InvalidMinutes_ReturnsEmptyString() throws Throwable {
        // arrange
        String originTime = "12:60";
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeOfDayDoc_SameDayTime_ReturnsFormattedTime() throws Throwable {
        // arrange
        String originTime = "13:45";
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        assertEquals("13:45", result);
    }

    @Test
    public void testGetTimeOfDayDoc_NextDayTime_ReturnsNextDayFormattedTime() throws Throwable {
        // arrange
        String originTime = "25:30";
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        assertEquals("次日01:30", result);
    }

    @Test
    public void testGetTimeOfDayDoc_NumberFormatException_ReturnsEmptyString() throws Throwable {
        // arrange
        String originTime = "abc:def";
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetTimeOfDayDoc_24HoursBoundary_ReturnsMidnightTime() throws Throwable {
        // arrange
        String originTime = "24:00";
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        // 修正：24:00 应该显示为 "次日00:00"
        assertEquals("次日00:00", result);
    }

    @Test
    public void testGetTimeOfDayDoc_ZeroMinutes_ReturnsFormattedTime() throws Throwable {
        // arrange
        String originTime = "08:00";
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        assertEquals("08:00", result);
    }

    @Test
    public void testGetTimeOfDayDoc_NegativeHours_ReturnsFormattedTime() throws Throwable {
        // arrange
        String originTime = "-1:30";
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        // 修正：方法实际上会返回负数时间
        assertEquals("-1:30", result);
    }

    @Test
    public void testGetTimeOfDayDoc_EmptyString_ReturnsEmptyString() throws Throwable {
        // arrange
        String originTime = "";
        // act
        String result = AvailableTimeHelper.getTimeOfDayDoc(originTime);
        // assert
        assertEquals("", result);
    }

    @BeforeEach
    public void setUp() {
        ApplicationContextGetBeanHelper helper = new ApplicationContextGetBeanHelper();
        helper.setApplicationContext(mockApplicationContext);
    }

    @Test
    public void testGetCustomAvailableTimeOfDaysWithNullInput() throws Throwable {
        // act
        String result = AvailableTimeHelper.getCustomAvailableTimeOfDays(null);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testGetCustomAvailableTimeOfDaysWhenBeanCreationFails() throws Throwable {
        // arrange
        FetcherResultDTO fetcherResults = mock(FetcherResultDTO.class);
        when(fetcherResults.getProductSecondCategoryId()).thenReturn(TEST_CATEGORY_ID);
        when(mockApplicationContext.getBean(AvailableTimeStrategyFactory.class)).thenThrow(new RuntimeException("Bean creation failed"));
        // act
        String result = AvailableTimeHelper.getCustomAvailableTimeOfDays(fetcherResults);
        // assert
        assertEquals("", result);
        verify(fetcherResults).getProductSecondCategoryId();
    }

    @Test
    public void testGetCustomAvailableTimeOfDaysWhenStrategyIsNull() throws Throwable {
        // arrange
        FetcherResultDTO fetcherResults = mock(FetcherResultDTO.class);
        when(fetcherResults.getProductSecondCategoryId()).thenReturn(TEST_CATEGORY_ID);
        when(mockApplicationContext.getBean(AvailableTimeStrategyFactory.class)).thenReturn(mockStrategyFactory);
        when(mockStrategyFactory.getStrategy(TEST_CATEGORY_ID)).thenReturn(null);
        // act
        String result = AvailableTimeHelper.getCustomAvailableTimeOfDays(fetcherResults);
        // assert
        assertEquals("", result);
        verify(fetcherResults).getProductSecondCategoryId();
        verify(mockStrategyFactory).getStrategy(TEST_CATEGORY_ID);
    }

    @Test
    public void testGetCustomAvailableTimeOfDaysWhenStrategyThrowsException() throws Throwable {
        // arrange
        FetcherResultDTO fetcherResults = mock(FetcherResultDTO.class);
        AvailableTimeStrategy mockStrategy = mock(AvailableTimeStrategy.class);
        when(fetcherResults.getProductSecondCategoryId()).thenReturn(TEST_CATEGORY_ID);
        when(mockApplicationContext.getBean(AvailableTimeStrategyFactory.class)).thenReturn(mockStrategyFactory);
        when(mockStrategyFactory.getStrategy(TEST_CATEGORY_ID)).thenReturn(mockStrategy);
        when(mockStrategy.getAvailableTime(any())).thenThrow(new RuntimeException("Strategy execution failed"));
        // act
        String result = AvailableTimeHelper.getCustomAvailableTimeOfDays(fetcherResults);
        // assert
        assertEquals("", result);
        verify(fetcherResults).getProductSecondCategoryId();
        verify(mockStrategyFactory).getStrategy(TEST_CATEGORY_ID);
        verify(mockStrategy).getAvailableTime(fetcherResults);
    }

    @Test
    public void testGetCustomAvailableTimeOfDaysSuccess() throws Throwable {
        // arrange
        String expectedResult = "test result";
        FetcherResultDTO fetcherResults = mock(FetcherResultDTO.class);
        AvailableTimeStrategy mockStrategy = mock(AvailableTimeStrategy.class);
        when(fetcherResults.getProductSecondCategoryId()).thenReturn(TEST_CATEGORY_ID);
        when(mockApplicationContext.getBean(AvailableTimeStrategyFactory.class)).thenReturn(mockStrategyFactory);
        when(mockStrategyFactory.getStrategy(TEST_CATEGORY_ID)).thenReturn(mockStrategy);
        when(mockStrategy.getAvailableTime(any())).thenReturn(expectedResult);
        // act
        String result = AvailableTimeHelper.getCustomAvailableTimeOfDays(fetcherResults);
        // assert
        assertEquals(expectedResult, result);
        verify(fetcherResults).getProductSecondCategoryId();
        verify(mockStrategyFactory).getStrategy(TEST_CATEGORY_ID);
        verify(mockStrategy).getAvailableTime(fetcherResults);
    }

    @Test
    public void testGetDateDocWhenBaseInfoIsNull() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = null;
        int categoryId = 123;
        // act
        String result = AvailableTimeHelper.getDateDoc(baseInfo, categoryId);
        // assert
        assertEquals("周一至周日", result);
    }

    @Test
    public void testGetDateDocWithDIYDisableDate() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        DisableDateDTO disableDate = mock(DisableDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        when(disableDate.getDisableDateRangeDTOS()).thenReturn(Collections.singletonList(null));
        // act
        String result = AvailableTimeHelper.getDateDoc(baseInfo, 123);
        // assert
        assertEquals("部分日期", result);
    }

    @Test
    public void testGetDateDocWithDisableDays() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        DisableDateDTO disableDate = mock(DisableDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        when(disableDate.getDisableDateRangeDTOS()).thenReturn(null);
        when(disableDate.getDisableDays()).thenReturn(Arrays.asList(1, 2, 3));
        // act
        String result = AvailableTimeHelper.getDateDoc(baseInfo, 123);
        // assert
        assertEquals("周四至周日", result);
    }

    @Test
    public void testGetDateDocWithNoDisableOrAvailableDates() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        DisableDateDTO disableDate = mock(DisableDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getDisableDate()).thenReturn(disableDate);
        when(disableDate.getDisableDateRangeDTOS()).thenReturn(null);
        when(disableDate.getDisableDays()).thenReturn(Collections.emptyList());
        // act
        String result = AvailableTimeHelper.getDateDoc(baseInfo, 123);
        // assert
        assertEquals("周一至周日", result);
    }

    @Test
    public void testGetDateDocWithAllDaysAvailable() throws Throwable {
        // arrange
        ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
        DealGroupRuleDTO rule = mock(DealGroupRuleDTO.class);
        DealGroupUseRuleDTO useRule = mock(DealGroupUseRuleDTO.class);
        AvailableDateDTO availableDate = mock(AvailableDateDTO.class);
        CycleAvailableDateDTO cycleAvailableDate = mock(CycleAvailableDateDTO.class);
        when(baseInfo.getRule()).thenReturn(rule);
        when(rule.getUseRule()).thenReturn(useRule);
        when(useRule.getAvailableDate()).thenReturn(availableDate);
        when(availableDate.getCycleAvailableDateList()).thenReturn(Collections.singletonList(cycleAvailableDate));
        when(cycleAvailableDate.getAvailableDays()).thenReturn(Arrays.asList(1, 2, 3, 4, 5, 6, 7));
        // act
        String result = AvailableTimeHelper.getDateDoc(baseInfo, 123);
        // assert
        assertEquals("周一至周日", result);
    }
}
