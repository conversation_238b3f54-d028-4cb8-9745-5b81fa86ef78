package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

/**
 * Test class for SpaStrategyImpl
 */
class SpaStrategyImplTest {

    @Spy
    private SpaStrategyImpl spaStrategy;

    private List<ServiceProjectAttrDTO> serviceProjectAttrs;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        serviceProjectAttrs = new ArrayList<>();
    }

    private ServiceProjectAttrDTO createMockAttr(String attrName, String attrValue) {
        ServiceProjectAttrDTO mock = mock(ServiceProjectAttrDTO.class);
        when(mock.getAttrName()).thenReturn(attrName);
        when(mock.getAttrValue()).thenReturn(attrValue);
        return mock;
    }

    /**
     * Test case when input list is null
     */
    @Test
    public void testGetToolValueNullInput() throws Throwable {
        // arrange
        doReturn(null).when(spaStrategy).getAttrValue(null, "freeEssentialOil");
        doReturn(null).when(spaStrategy).getAttrValue(null, "hotpackTool");
        doReturn(new ArrayList<>()).when(spaStrategy).getMixedTools(null);
        // act
        String result = spaStrategy.getToolValue(null);
        // assert
        assertNull(result);
        verify(spaStrategy).getAttrValue(null, "freeEssentialOil");
    }

    /**
     * Test case when input list is empty
     */
    @Test
    public void testGetToolValueEmptyInput() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> emptyList = new ArrayList<>();
        // act
        String result = spaStrategy.getToolValue(emptyList);
        // assert
        assertNull(result);
    }

    /**
     * Test case when free essential oil has exactly 1 item
     */
    @Test
    public void testGetToolValueSingleFreeEssentialOil() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("freeEssentialOil", "薰衣草"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("薰衣草", result);
    }

    /**
     * Test case when free essential oil has exactly 2 items
     */
    @Test
    public void testGetToolValueTwoFreeEssentialOils() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("freeEssentialOil", "薰衣草、玫瑰"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("薰衣草、玫瑰", result);
    }

    /**
     * Test case when free essential oil has more than 2 items
     */
    @Test
    public void testGetToolValueMultipleFreeEssentialOils() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("freeEssentialOil", "薰衣草、玫瑰、柠檬"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("3种精油可选择", result);
    }

    /**
     * Test case when hotpack tool is present but no free essential oil
     */
    @Test
    public void testGetToolValueHotpackTool() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("hotpackTool", "热石"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("热石", result);
    }

    /**
     * Test case when only disposable materials are present
     */
    @Test
    public void testGetToolValueDisposableMaterialOnly() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("disposableMaterial", "一次性床单、一次性短裤"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("一次性床单", result);
    }

    /**
     * Test case when only unclassified tools are present
     */
    @Test
    public void testGetToolValueUnclassifiedToolsOnly() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("unclassifiedTools", "一次性床单、眼罩"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("一次性床单", result);
    }

    /**
     * Test case when free essential oil is empty string
     */
    @Test
    public void testGetToolValueEmptyFreeEssentialOil() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("freeEssentialOil", ""));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertNull(result);
    }

    /**
     * Test case when mixed tools are present but empty after splitting
     */
    @Test
    public void testGetToolValueEmptyMixedTools() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("disposableMaterial", ""));
        serviceProjectAttrs.add(createMockAttr("unclassifiedTools", ""));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertNull(result);
    }

    /**
     * Test case for priority order with multiple tools
     */
    @Test
    public void testGetToolValuePriorityOrder() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("freeEssentialOil", "薰衣草"));
        serviceProjectAttrs.add(createMockAttr("hotpackTool", "热石"));
        serviceProjectAttrs.add(createMockAttr("disposableMaterial", "一次性床单"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("薰衣草", result);
    }

    /**
     * Test case for mixed tools priority sorting
     */
    @Test
    public void testGetToolValueMixedToolsSorting() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("disposableMaterial", "一次性床单"));
        serviceProjectAttrs.add(createMockAttr("unclassifiedTools", "电动按摩床"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("一次性床单", result);
    }

    /**
     * Test case for mixed tools with multiple items priority sorting
     */
    @Test
    public void testGetToolValueMultipleMixedToolsSorting() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("disposableMaterial", "一次性床单、一次性短裤"));
        serviceProjectAttrs.add(createMockAttr("unclassifiedTools", "香薰、眼罩"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("一次性床单", result);
    }

    /**
     * Test case for mixed tools with specific priority order
     */
    @Test
    public void testGetToolValueSpecificPriorityOrder() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("disposableMaterial", "一次性拖鞋"));
        serviceProjectAttrs.add(createMockAttr("unclassifiedTools", "一次性床单"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("一次性床单", result);
    }

    /**
     * Test case for no matching attributes
     */
    @Test
    public void testGetToolValueNoMatchingAttributes() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("otherAttr", "someValue"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertNull(result);
    }

    /**
     * Test case for mixed tools with all priority items
     */
    @Test
    public void testGetToolValueAllPriorityItems() throws Throwable {
        // arrange
        serviceProjectAttrs.add(createMockAttr("disposableMaterial", "一次性床单、一次性短裤、一次性拖鞋、一次性按摩巾、消毒按摩服"));
        serviceProjectAttrs.add(createMockAttr("unclassifiedTools", "香薰、眼罩、电动按摩床"));
        // act
        String result = spaStrategy.getToolValue(serviceProjectAttrs);
        // assert
        assertEquals("一次性床单", result);
    }
}
