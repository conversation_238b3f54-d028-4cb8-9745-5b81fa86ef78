package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FootMassageStrategyImplTest {

    private final FootMassageStrategyImpl strategy = new FootMassageStrategyImpl();

    /**
     * 测试电动按摩洗脚桶存在的情况
     */
    @Test
    public void testGetToolValue_ElectricBucketExists() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr1 = mock(ServiceProjectAttrDTO.class);
        when(attr1.getAttrName()).thenReturn("footbathBucket");
        when(attr1.getAttrValue()).thenReturn("电动按摩洗脚桶");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr1);
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertEquals("电动按摩洗脚桶", result);
    }

    /**
     * 测试热敷工具存在的情况
     */
    @Test
    public void testGetToolValue_HotpackToolExists() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr1 = mock(ServiceProjectAttrDTO.class);
        when(attr1.getAttrName()).thenReturn("footbathBucket");
        when(attr1.getAttrValue()).thenReturn("普通木桶");
        ServiceProjectAttrDTO attr2 = mock(ServiceProjectAttrDTO.class);
        when(attr2.getAttrName()).thenReturn("hotpackTool");
        when(attr2.getAttrValue()).thenReturn("热敷毛巾");
        List<ServiceProjectAttrDTO> attrs = Arrays.asList(attr1, attr2);
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertEquals("热敷毛巾", result);
    }

    /**
     * 测试泡脚包存在且包含多个值的情况
     */
    @Test
    public void testGetToolValue_FootBathBagWithMultipleValues() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr1 = mock(ServiceProjectAttrDTO.class);
        when(attr1.getAttrName()).thenReturn("footbathBucket");
        when(attr1.getAttrValue()).thenReturn("普通木桶");
        ServiceProjectAttrDTO attr2 = mock(ServiceProjectAttrDTO.class);
        when(attr2.getAttrName()).thenReturn("footbathMaterial");
        when(attr2.getAttrValue()).thenReturn("牛奶、艾草、生姜");
        List<ServiceProjectAttrDTO> attrs = Arrays.asList(attr1, attr2);
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertEquals("牛奶包、艾草等3种任选", result);
    }

    /**
     * 测试只有木桶存在的情况
     */
    @Test
    public void testGetToolValue_OnlyBasinExists() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr1 = mock(ServiceProjectAttrDTO.class);
        when(attr1.getAttrName()).thenReturn("footbathBucket");
        when(attr1.getAttrValue()).thenReturn("普通木桶");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr1);
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertEquals("普通木桶", result);
    }

    /**
     * 测试空列表输入的情况
     */
    @Test
    public void testGetToolValue_EmptyInput() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = Collections.emptyList();
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertNull(result);
    }

    /**
     * 测试泡脚包包含牛奶需要替换的情况
     */
    @Test
    public void testGetToolValue_FootBathBagWithMilk() throws Throwable {
        // arrange
        ServiceProjectAttrDTO attr1 = mock(ServiceProjectAttrDTO.class);
        when(attr1.getAttrName()).thenReturn("footbathMaterial");
        when(attr1.getAttrValue()).thenReturn("牛奶");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(attr1);
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertEquals("牛奶包", result);
    }
}
