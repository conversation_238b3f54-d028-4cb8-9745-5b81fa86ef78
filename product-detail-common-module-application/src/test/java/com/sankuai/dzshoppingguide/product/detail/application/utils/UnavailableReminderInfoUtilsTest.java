package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsBarLayerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.GuaranteeInstructionsContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo.ProductDetailReminderVO;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.AvailableDurationDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.CycleAvailableDateDTO;
import com.sankuai.general.product.query.center.client.dto.rule.use.DateRangeDTO;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import java.time.format.DateTimeFormatter;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import java.time.DayOfWeek;
import java.lang.reflect.Field;
import org.mockito.Spy;
import com.sankuai.dzshoppingguide.product.detail.domain.cpv.unavailable.UnavailabelDate;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class UnavailableReminderInfoUtilsTest {

    private AvailableDateDTO availableDateDTO;

    private CycleAvailableDateDTO cycleAvailableDateDTO;

    private AvailableDurationDateDTO durationDateDTO;

    private DateRangeDTO dateRangeDTO;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");



    @BeforeEach
    void setUp() {
        availableDateDTO = new AvailableDateDTO();
        cycleAvailableDateDTO = new CycleAvailableDateDTO();
        durationDateDTO = new AvailableDurationDateDTO();
        dateRangeDTO = new DateRangeDTO();
    }

    @Test
    public void testHitAvailableTime_WhenInputIsNull() throws Throwable {
        // arrange
        availableDateDTO = null;
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTime_WhenAvailableTypeIsNull() throws Throwable {
        // arrange
        availableDateDTO.setAvailableType(null);
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTime_WhenType0AndEmptyCycleList() throws Throwable {
        // arrange
        availableDateDTO.setAvailableType(0);
        availableDateDTO.setCycleAvailableDateList(Collections.emptyList());
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTime_WhenType0AndDayAvailable() throws Throwable {
        // arrange
        availableDateDTO.setAvailableType(0);
        int currentDayOfWeek = LocalDate.now().getDayOfWeek().getValue();
        cycleAvailableDateDTO.setAvailableDays(Arrays.asList(currentDayOfWeek));
        availableDateDTO.setCycleAvailableDateList(Collections.singletonList(cycleAvailableDateDTO));
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTime_WhenType0AndDayNotAvailable() throws Throwable {
        // arrange
        availableDateDTO.setAvailableType(0);
        int currentDayOfWeek = LocalDate.now().getDayOfWeek().getValue();
        int unavailableDay = (currentDayOfWeek % 7) + 1;
        cycleAvailableDateDTO.setAvailableDays(Arrays.asList(unavailableDay));
        availableDateDTO.setCycleAvailableDateList(Collections.singletonList(cycleAvailableDateDTO));
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitAvailableTime_WhenType1AndEmptySpecifiedList() throws Throwable {
        // arrange
        availableDateDTO.setAvailableType(1);
        availableDateDTO.setSpecifiedDurationDateList(Collections.emptyList());
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTime_WhenType1AndValidDateRange() throws Throwable {
        // arrange
        availableDateDTO.setAvailableType(1);
        dateRangeDTO.setFrom(LocalDate.now().toString());
        dateRangeDTO.setTo(LocalDate.now().plusDays(1).toString());
        List<DateRangeDTO> dateRangeDTOS = new ArrayList<>();
        dateRangeDTOS.add(dateRangeDTO);
        durationDateDTO.setAvailableDateRangeDTOS(dateRangeDTOS);
        availableDateDTO.setSpecifiedDurationDateList(Collections.singletonList(durationDateDTO));
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTime_WhenType1AndInvalidDateRange() throws Throwable {
        // arrange
        availableDateDTO.setAvailableType(1);
        dateRangeDTO.setFrom(LocalDate.now().plusDays(1).toString());
        dateRangeDTO.setTo(LocalDate.now().plusDays(2).toString());
        List<DateRangeDTO> dateRangeDTOS = new ArrayList<>();
        dateRangeDTOS.add(dateRangeDTO);
        durationDateDTO.setAvailableDateRangeDTOS(dateRangeDTOS);
        availableDateDTO.setSpecifiedDurationDateList(Collections.singletonList(durationDateDTO));
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitAvailableTime_WhenTypeIsOther() throws Throwable {
        // arrange
        availableDateDTO.setAvailableType(2);
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTimeWhenAvailableDateDTOIsNull() throws Throwable {
        // arrange
        AvailableDateDTO availableDateDTO = null;
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTimeWhenAvailableTypeIsNull() throws Throwable {
        // arrange
        AvailableDateDTO availableDateDTO = new AvailableDateDTO();
        availableDateDTO.setAvailableType(null);
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTimeWhenType0AndCycleAvailableDateListIsEmpty() throws Throwable {
        // arrange
        AvailableDateDTO availableDateDTO = new AvailableDateDTO();
        availableDateDTO.setAvailableType(0);
        availableDateDTO.setCycleAvailableDateList(Collections.emptyList());
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTimeWhenType1AndSpecifiedDurationDateListIsEmpty() throws Throwable {
        // arrange
        AvailableDateDTO availableDateDTO = new AvailableDateDTO();
        availableDateDTO.setAvailableType(1);
        availableDateDTO.setSpecifiedDurationDateList(Collections.emptyList());
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitAvailableTimeWhenAvailableTypeIsOtherValue() throws Throwable {
        // arrange
        AvailableDateDTO availableDateDTO = new AvailableDateDTO();
        availableDateDTO.setAvailableType(2);
        // act
        boolean result = UnavailableReminderInfoUtils.hitAvailableTime(availableDateDTO);
        // assert
        assertTrue(result);
    }

    private DateRangeDTO createDateRange(LocalDate from, LocalDate to) {
        DateRangeDTO dateRange = new DateRangeDTO();
        dateRange.setFrom(from.atStartOfDay().format(DATE_FORMATTER));
        dateRange.setTo(to.atTime(23, 59, 59).format(DATE_FORMATTER));
        return dateRange;
    }

    @Test
    public void testValidSpecifiedDurationDateList_NullInput() throws Throwable {
        // arrange
        List<AvailableDurationDateDTO> input = null;
        // act
        boolean result = UnavailableReminderInfoUtils.validSpecifiedDurationDateList(input);
        // assert
        assertFalse(result);
    }

    @Test
    public void testValidSpecifiedDurationDateList_EmptyList() throws Throwable {
        // arrange
        List<AvailableDurationDateDTO> input = Collections.emptyList();
        // act
        boolean result = UnavailableReminderInfoUtils.validSpecifiedDurationDateList(input);
        // assert
        assertFalse(result);
    }

    @Test
    public void testValidSpecifiedDurationDateList_ContainsNullElement() throws Throwable {
        // arrange
        LocalDate today = LocalDate.now();
        AvailableDurationDateDTO dto1 = new AvailableDurationDateDTO();
        dto1.setAvailableDateRangeDTOS(Arrays.asList(createDateRange(today, today.plusDays(1))));
        List<AvailableDurationDateDTO> input = Arrays.asList(dto1, null);
        // act
        boolean result = UnavailableReminderInfoUtils.validSpecifiedDurationDateList(input);
        // assert
        assertTrue(result);
    }

    @Test
    public void testValidSpecifiedDurationDateList_EmptyAvailableDateRange() throws Throwable {
        // arrange
        AvailableDurationDateDTO dto1 = new AvailableDurationDateDTO();
        dto1.setAvailableDateRangeDTOS(Collections.emptyList());
        AvailableDurationDateDTO dto2 = new AvailableDurationDateDTO();
        dto2.setAvailableDateRangeDTOS(null);
        List<AvailableDurationDateDTO> input = Arrays.asList(dto1, dto2);
        // act
        boolean result = UnavailableReminderInfoUtils.validSpecifiedDurationDateList(input);
        // assert
        assertFalse(result);
    }

    @Test
    public void testValidSpecifiedDurationDateList_InvalidDateRange() throws Throwable {
        // arrange
        LocalDate pastDate = LocalDate.now().minusMonths(1);
        AvailableDurationDateDTO dto1 = new AvailableDurationDateDTO();
        dto1.setAvailableDateRangeDTOS(Arrays.asList(createDateRange(pastDate, pastDate.plusDays(1))));
        List<AvailableDurationDateDTO> input = Collections.singletonList(dto1);
        // act
        boolean result = UnavailableReminderInfoUtils.validSpecifiedDurationDateList(input);
        // assert
        assertFalse(result);
    }

    @Test
    public void testValidSpecifiedDurationDateList_ValidDateRange() throws Throwable {
        // arrange
        LocalDate today = LocalDate.now();
        AvailableDurationDateDTO dto1 = new AvailableDurationDateDTO();
        dto1.setAvailableDateRangeDTOS(Arrays.asList(createDateRange(today, today.plusDays(1))));
        List<AvailableDurationDateDTO> input = Collections.singletonList(dto1);
        // act
        boolean result = UnavailableReminderInfoUtils.validSpecifiedDurationDateList(input);
        // assert
        assertTrue(result);
    }

    @Test
    public void testValidSpecifiedDurationDateList_MixedElements() throws Throwable {
        // arrange
        LocalDate today = LocalDate.now();
        LocalDate pastDate = today.minusMonths(1);
        AvailableDurationDateDTO validDto = new AvailableDurationDateDTO();
        validDto.setAvailableDateRangeDTOS(Arrays.asList(createDateRange(today, today.plusDays(1))));
        AvailableDurationDateDTO invalidDto1 = new AvailableDurationDateDTO();
        invalidDto1.setAvailableDateRangeDTOS(null);
        AvailableDurationDateDTO invalidDto2 = new AvailableDurationDateDTO();
        invalidDto2.setAvailableDateRangeDTOS(Collections.emptyList());
        AvailableDurationDateDTO invalidDto3 = new AvailableDurationDateDTO();
        invalidDto3.setAvailableDateRangeDTOS(Arrays.asList(createDateRange(pastDate, pastDate.plusDays(1))));
        List<AvailableDurationDateDTO> input = Arrays.asList(invalidDto1, validDto, invalidDto2, invalidDto3);
        // act
        boolean result = UnavailableReminderInfoUtils.validSpecifiedDurationDateList(input);
        // assert
        assertTrue(result);
    }

    @Test
    public void testDayOfWeekReturnsCorrectValue() throws Throwable {
        // arrange
        int expected = LocalDate.now().getDayOfWeek().getValue();
        // act
        int result = UnavailableReminderInfoUtils.dayOfWeek();
        // assert
        assertEquals(expected, result);
    }

    @Test
    public void testDayOfWeekReturnsValidRange() throws Throwable {
        // act
        int result = UnavailableReminderInfoUtils.dayOfWeek();
        // assert
        assertTrue(result >= 1 && result <= 7, "Day of week should be between 1 (Monday) and 7 (Sunday)");
    }

    @Test
    public void testDayOfWeekMatchesJavaDayOfWeek() throws Throwable {
        // arrange
        int expected = LocalDate.now().getDayOfWeek().getValue();
        // act
        int result = UnavailableReminderInfoUtils.dayOfWeek();
        // assert
        assertEquals(expected, result, "Should match Java's DayOfWeek value");
    }

    @Test
    public void testHitWeekendUnavailableWhenInputIsNull() throws Throwable {
        // arrange
        String input = null;
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekendUnavailable(input);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekendUnavailableWhenInputIsEmpty() throws Throwable {
        // arrange
        String input = "";
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekendUnavailable(input);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekendUnavailableWhenInputIsBlank() throws Throwable {
        // arrange
        String input = "   ";
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekendUnavailable(input);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekendUnavailableWhenInputContainsCurrentDay() throws Throwable {
        // arrange
        int currentDay = UnavailableReminderInfoUtils.dayOfWeek();
        String input = String.valueOf(currentDay);
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekendUnavailable(input);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekendUnavailableWhenInputNotContainsCurrentDay() throws Throwable {
        // arrange
        int currentDay = UnavailableReminderInfoUtils.dayOfWeek();
        // 确保不是当前日期
        String input = String.valueOf((currentDay % 7) + 1);
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekendUnavailable(input);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitWeekendUnavailableWhenMultipleDigitsContainCurrentDay() throws Throwable {
        // arrange
        int currentDay = UnavailableReminderInfoUtils.dayOfWeek();
        String input = "1," + currentDay + ",3";
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekendUnavailable(input);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekendUnavailableWhenMultipleDigitsNotContainCurrentDay() throws Throwable {
        // arrange
        int currentDay = UnavailableReminderInfoUtils.dayOfWeek();
        String input = String.format("%d,%d,%d", (currentDay % 7) + 1, ((currentDay + 1) % 7) + 1, ((currentDay + 2) % 7) + 1);
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekendUnavailable(input);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitWeekendUnavailableWhenInputContainsNonDigitCharacters() throws Throwable {
        // arrange
        String input = "a,b,c";
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekendUnavailable(input);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitWeekAndHolidayUnavailableWhenDisableDaysIsEmpty() throws Throwable {
        // arrange
        List<Integer> disableDays = Collections.emptyList();
        Map<String, UnavailabelDate> unavailabelDateMap = new HashMap<>();
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekAndHolidayUnavailable(disableDays, unavailabelDateMap);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekAndHolidayUnavailableWhenCurrentDayInDisableDays() throws Throwable {
        // arrange
        int currentDayOfWeek = UnavailableReminderInfoUtils.dayOfWeek();
        List<Integer> disableDays = Arrays.asList(currentDayOfWeek);
        Map<String, UnavailabelDate> unavailabelDateMap = new HashMap<>();
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekAndHolidayUnavailable(disableDays, unavailabelDateMap);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitWeekAndHolidayUnavailableWhenCurrentDateInHolidayRange() throws Throwable {
        // arrange
        int currentDayOfWeek = UnavailableReminderInfoUtils.dayOfWeek();
        // 使用当前星期作为禁用日期
        List<Integer> disableDays = Arrays.asList(currentDayOfWeek);
        UnavailabelDate holidayDate = new UnavailabelDate();
        holidayDate.setFrom("2023-01-01 00:00:00");
        holidayDate.setTo("2024-12-31 23:59:59");
        Map<String, UnavailabelDate> unavailabelDateMap = new HashMap<>();
        // 使用当前星期作为key
        unavailabelDateMap.put(String.valueOf(currentDayOfWeek), holidayDate);
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekAndHolidayUnavailable(disableDays, unavailabelDateMap);
        // assert
        assertTrue(result);
    }

    @Test
    public void testHitWeekAndHolidayUnavailableWhenCurrentDateNotInAnyRange() throws Throwable {
        // arrange
        int currentDayOfWeek = UnavailableReminderInfoUtils.dayOfWeek();
        // 使用一个不包含当前星期的列表
        List<Integer> disableDays = Arrays.asList((currentDayOfWeek % 7) + 1);
        UnavailabelDate holidayDate = new UnavailabelDate();
        holidayDate.setFrom("2022-01-01 00:00:00");
        holidayDate.setTo("2022-01-01 23:59:59");
        Map<String, UnavailabelDate> unavailabelDateMap = new HashMap<>();
        unavailabelDateMap.put(String.valueOf(disableDays.get(0)), holidayDate);
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekAndHolidayUnavailable(disableDays, unavailabelDateMap);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekAndHolidayUnavailableWhenUnavailabelDateMapIsEmpty() throws Throwable {
        // arrange
        int currentDayOfWeek = UnavailableReminderInfoUtils.dayOfWeek();
        // 使用一个不包含当前星期的列表
        List<Integer> disableDays = Arrays.asList((currentDayOfWeek % 7) + 1);
        Map<String, UnavailabelDate> unavailabelDateMap = new HashMap<>();
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekAndHolidayUnavailable(disableDays, unavailabelDateMap);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHitWeekAndHolidayUnavailableWhenHolidayDateFormatInvalid() throws Throwable {
        // arrange
        int currentDayOfWeek = UnavailableReminderInfoUtils.dayOfWeek();
        // 使用一个不包含当前星期的列表
        List<Integer> disableDays = Arrays.asList((currentDayOfWeek % 7) + 1);
        UnavailabelDate holidayDate = new UnavailabelDate();
        holidayDate.setFrom("invalid-date");
        holidayDate.setTo("invalid-date");
        Map<String, UnavailabelDate> unavailabelDateMap = new HashMap<>();
        unavailabelDateMap.put(String.valueOf(disableDays.get(0)), holidayDate);
        // act
        boolean result = UnavailableReminderInfoUtils.hitWeekAndHolidayUnavailable(disableDays, unavailabelDateMap);
        // assert
        assertFalse(result);
    }
}
