package com.sankuai.dzshoppingguide.product.detail.application.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyPreFilter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.googlecode.aviator.runtime.type.AviatorString;
import com.sankuai.athena.digital.arch.metadata.cache.DAClassInfoCache;
import com.sankuai.athena.digital.arch.metadata.type.bo.ext.pojo.clazz.DAClassInfoBO;
//import com.sankuai.dz.api.module.arrange.framework.component.storage.ComponentDefinitionStorage;
import com.sankuai.dz.product.detail.page.low.code.engine.factory.AviatorRuleEngine;
import com.sankuai.dz.product.detail.page.low.code.entity.component.metadata.ComponentDefinition;
import com.sankuai.dz.product.detail.page.low.code.entity.spi.bo.ComponentBO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeItemDTO;
import com.sankuai.general.product.query.center.client.dto.StandardAttributeValueDTO;
import org.junit.Before;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class RuleEngineBaseTest {

    public static String toJSONString(Object groovyResult) {
        PropertyPreFilter propertyPreFilter = (serializer, source, name) -> {
            if ("componentKey".equals(name)) {
                return false;
            }
            return true;
        };

        return JSON.toJSONString(groovyResult, propertyPreFilter, SerializerFeature.DisableCircularReferenceDetect);
    }

    @Before
    public void setUp() {
//        List<Class<? extends ComponentBO>> classes = Lists.newArrayList(
//                DescriptionComponentBO.class,
//                FacilityItemComponentBO.class,
//                ServiceProjectItemComponentBO.class,
//                TitleComponentBO.class,
//                TitleWithPrefixDotComponentBO.class
//        );
//        registerComponentsByReflection(classes);
    }

    public AviatorRuleEngine getAviatorRuleEngine() {
        AviatorEvaluatorInstance aviatorInstance = getAviatorEvaluatorInstance();
        aviatorInstance.addFunction(new GetProductAttrJSONValueFunction());
        aviatorInstance.addFunction(new GetProductAttributeFunction());
        return AviatorRuleEngine.getInstance();
    }

    private AviatorEvaluatorInstance getAviatorEvaluatorInstance() {
        try {
            // 假设你已经有了AviatorRuleEngine的实例
            AviatorRuleEngine instance = AviatorRuleEngine.getInstance();
            Class<?> aviatorRuleEngineClass = instance.getClass();
            Field aviatorInstanceField = aviatorRuleEngineClass.getDeclaredField("AVIATOR_INSTANCE");
            // 设置字段可访问
            aviatorInstanceField.setAccessible(true);
            // 获取字段值（静态字段，所以传null即可，不需要实例）
            AviatorEvaluatorInstance aviatorInstance = (AviatorEvaluatorInstance) aviatorInstanceField.get(null);
            return aviatorInstance;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 自定义函数：从属性列表中获取JSON值
     */
    public static class GetProductAttrJSONValueFunction extends AbstractFunction {
        @Override
        public String getName() {
            return "getProductAttrJSONValue";
        }

        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            Object javaObject = FunctionUtils.getJavaObject(arg1, env);
            StandardAttributeDTO productAttributeModel = (StandardAttributeDTO) javaObject;
            String stringValue = FunctionUtils.getStringValue(arg2, env);
            if (productAttributeModel != null && productAttributeModel.getAttrs() != null) {
                for (StandardAttributeItemDTO attribute : productAttributeModel.getAttrs()) {
                    if (stringValue.equals(attribute.getAttrName()) && attribute.getAttrValues() != null && !attribute.getAttrValues().isEmpty()) {
                        StandardAttributeValueDTO standardAttributeValueDTO = attribute.getAttrValues().get(0);
                        if (standardAttributeValueDTO != null && standardAttributeValueDTO.getSimpleValues() != null && !standardAttributeValueDTO.getSimpleValues().isEmpty()) {
                            List<String> strings = JSON.parseArray(standardAttributeValueDTO.getSimpleValues().get(0), String.class);
                            return AviatorRuntimeJavaType.valueOf(strings);
                        }
                    }
                }
            }

            return AviatorRuntimeJavaType.valueOf(new String[0]);
        }
    }

    /**
     * 自定义函数：从属性列表中获取属性值
     */
    public static class GetProductAttributeFunction extends AbstractFunction {
        @Override
        public String getName() {
            return "getProductAttribute";
        }

        @Override
        public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {
            Object javaObject = FunctionUtils.getJavaObject(arg1, env);
            StandardAttributeDTO productAttributeModel = (StandardAttributeDTO) javaObject;
            String stringValue = FunctionUtils.getStringValue(arg2, env);
            if (productAttributeModel != null && productAttributeModel.getAttrs() != null) {
                for (StandardAttributeItemDTO attribute : productAttributeModel.getAttrs()) {
                    if (stringValue.equals(attribute.getAttrName()) && attribute.getAttrValues() != null && !attribute.getAttrValues().isEmpty()) {
                        StandardAttributeValueDTO standardAttributeValueDTO = attribute.getAttrValues().get(0);
                        if (standardAttributeValueDTO != null && standardAttributeValueDTO.getSimpleValues() != null && !standardAttributeValueDTO.getSimpleValues().isEmpty()) {
                            return new AviatorString(standardAttributeValueDTO.getSimpleValues().get(0));
                        }
                    }
                }
            }
            return new AviatorString("");
        }
    }

    /**
     * 通过反射动态注册组件到 ComponentDefinitionStorage
     * @param componentClass 要注册的组件类
     */
//    public void registerComponentByReflection(Class<? extends ComponentBO> componentClass) {
//        try {
//            // 创建组件定义
//            ComponentDefinition componentDefinition = new ComponentDefinition(componentClass);
//
//            // 通过反射获取 COMPONENT_KEY_TO_DEFINITION_MAP 字段
//            Field componentKeyMapField = ComponentDefinitionStorage.class.getDeclaredField("COMPONENT_KEY_TO_DEFINITION_MAP");
//            componentKeyMapField.setAccessible(true);
//            Map<String, ComponentDefinition> componentKeyMap = (Map<String, ComponentDefinition>) componentKeyMapField.get(null);
//
//            // 通过反射获取 COMPONENT_CLASS_TO_DEFINITION_MAP 字段
//            Field componentClassMapField = ComponentDefinitionStorage.class.getDeclaredField("COMPONENT_CLASS_TO_DEFINITION_MAP");
//            componentClassMapField.setAccessible(true);
//            Map<Class<? extends ComponentBO>, ComponentDefinition> componentClassMap =
//                    (Map<Class<? extends ComponentBO>, ComponentDefinition>) componentClassMapField.get(null);
//
//            // 添加到两个 Map 中
//            componentKeyMap.put(componentDefinition.getComponentKey(), componentDefinition);
//            componentClassMap.put(componentDefinition.getComponentClass(), componentDefinition);
//
//            System.out.println("成功注册组件: " + componentClass.getName() +
//                    ", componentKey: " + componentDefinition.getComponentKey());
//
//        } catch (Exception e) {
//            throw new RuntimeException("通过反射注册组件失败: " + componentClass.getName(), e);
//        }
//    }

    /**
     * 通过反射直接设置 ComponentDefinitionStorage 的 COMPONENT_CLASS_TO_DEFINITION_MAP 为空 Map
     */
//    public void resetComponentClassMapByReflection() {
//        try {
//            // 使用低代码框架中的 ComponentDefinitionStorage
//            Class<?> componentDefinitionStorageClass = ComponentDefinitionStorage.class;
//
//            // 通过反射获取 COMPONENT_CLASS_TO_DEFINITION_MAP 字段
//            Field componentClassMapField = componentDefinitionStorageClass.getDeclaredField("COMPONENT_CLASS_TO_DEFINITION_MAP");
//            componentClassMapField.setAccessible(true);
//            // 设置为新的空 HashMap
//            componentClassMapField.set(null, new HashMap<Class<? extends ComponentBO>, ComponentDefinition>());
//            System.out.println("已通过反射重置 COMPONENT_CLASS_TO_DEFINITION_MAP 为空 Map");
//        } catch (Exception e) {
//            throw new RuntimeException("通过反射重置 COMPONENT_CLASS_TO_DEFINITION_MAP 失败", e);
//        }
//    }

    /**
     * 批量注册多个组件
     * @param componentClasses 要注册的组件类数组
     */
//    public final void registerComponentsByReflection(List<Class<? extends ComponentBO>> componentClasses) {
//        for (Class<? extends ComponentBO> componentClass : componentClasses) {
//
//            registerDAClassInfoCache(componentClass);
//            registerComponentByReflection(componentClass);
//        }
//    }

    private static void registerDAClassInfoCache(Class<? extends ComponentBO> componentClass) {
        try {
            DAClassInfoBO daClassInfoBO = new DAClassInfoBO(componentClass);
            DAClassInfoCache.put(daClassInfoBO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
