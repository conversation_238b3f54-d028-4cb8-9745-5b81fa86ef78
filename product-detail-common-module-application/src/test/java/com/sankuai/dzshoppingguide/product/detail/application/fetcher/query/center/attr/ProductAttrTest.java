package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.MapUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import java.util.Optional;
import org.mockito.MockedStatic;

@ExtendWith(MockitoExtension.class)
class ProductAttrTest {

    private ProductAttr productAttr;

    private Map<String, AttrDTO> skuAttr;

    /**
     * 测试当 skuAttr 为 null 时返回空列表
     */
    @Test
    public void testGetSkuAttrListWhenSkuAttrIsNull() throws Throwable {
        // arrange
        ProductAttr productAttr = new ProductAttr(null);
        // act
        List<AttrDTO> result = productAttr.getSkuAttrList();
        // assert
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "结果应为空列表");
    }

    /**
     * 测试当 skuAttr 是空 map 时返回空列表
     */
    @Test
    public void testGetSkuAttrListWhenSkuAttrIsEmptyMap() throws Throwable {
        // arrange
        Map<String, AttrDTO> emptyMap = Collections.emptyMap();
        ProductAttr productAttr = new ProductAttr(emptyMap);
        // act
        List<AttrDTO> result = productAttr.getSkuAttrList();
        // assert
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "结果应为空列表");
    }

    /**
     * 测试当 skuAttr.values() 为空时返回空列表
     */
    @Test
    public void testGetSkuAttrListWhenValuesIsEmpty() throws Throwable {
        // arrange
        Map<String, AttrDTO> mockMap = mock(Map.class);
        when(mockMap.values()).thenReturn(Collections.emptyList());
        when(MapUtils.isEmpty(mockMap)).thenReturn(false);
        ProductAttr productAttr = new ProductAttr(mockMap);
        // act
        List<AttrDTO> result = productAttr.getSkuAttrList();
        // assert
        assertNotNull(result, "结果不应为null");
        assertTrue(result.isEmpty(), "结果应为空列表");
    }

    /**
     * 测试当 skuAttr 有单个正常值时返回包含该值的列表
     */
    @Test
    public void testGetSkuAttrListWithSingleValue() throws Throwable {
        // arrange
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        Map<String, AttrDTO> mockMap = mock(Map.class);
        when(mockMap.values()).thenReturn(Collections.singletonList(mockAttrDTO));
        when(MapUtils.isEmpty(mockMap)).thenReturn(false);
        ProductAttr productAttr = new ProductAttr(mockMap);
        // act
        List<AttrDTO> result = productAttr.getSkuAttrList();
        // assert
        assertNotNull(result, "结果不应为null");
        assertEquals(1, result.size(), "结果列表大小应为1");
        assertSame(mockAttrDTO, result.get(0), "返回的DTO应与mock对象相同");
    }

    /**
     * 测试当 skuAttr 有多个值时返回包含所有值的列表
     */
    @Test
    public void testGetSkuAttrListWithMultipleValues() throws Throwable {
        // arrange
        AttrDTO mockAttrDTO1 = mock(AttrDTO.class);
        AttrDTO mockAttrDTO2 = mock(AttrDTO.class);
        List<AttrDTO> mockValues = Arrays.asList(mockAttrDTO1, mockAttrDTO2);
        Map<String, AttrDTO> mockMap = mock(Map.class);
        when(mockMap.values()).thenReturn(mockValues);
        when(MapUtils.isEmpty(mockMap)).thenReturn(false);
        ProductAttr productAttr = new ProductAttr(mockMap);
        // act
        List<AttrDTO> result = productAttr.getSkuAttrList();
        // assert
        assertNotNull(result, "结果不应为null");
        assertEquals(2, result.size(), "结果列表大小应为2");
        assertTrue(result.contains(mockAttrDTO1), "结果应包含第一个mock DTO");
        assertTrue(result.contains(mockAttrDTO2), "结果应包含第二个mock DTO");
    }

    @Test
    void testGetSkuAttrValueJson_WhenSkuAttrIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        ProductAttr productAttr = new ProductAttr(null);
        // act
        String result = productAttr.getSkuAttrValueJson("anyAttr");
        // assert
        assertNull(result);
    }

    @Test
    void testGetSkuAttrValueJson_WhenAttrNameNotExist_ShouldReturnNull() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("otherAttr", mock(AttrDTO.class));
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getSkuAttrValueJson("nonExistAttr");
        // assert
        assertNull(result);
    }

    @Test
    void testGetSkuAttrValueJson_WhenAttrValueIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getValue()).thenReturn(null);
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getSkuAttrValueJson("testAttr");
        // assert
        assertNull(result);
        verify(attrDTO, times(1)).getValue();
    }

    @Test
    void testGetSkuAttrValueJson_WhenAttrValueIsEmpty_ShouldReturnNull() throws Throwable {
        // arrange
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getValue()).thenReturn(Collections.emptyList());
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getSkuAttrValueJson("testAttr");
        // assert
        assertNull(result);
        verify(attrDTO, times(1)).getValue();
    }

    @Test
    void testGetSkuAttrValueJson_WhenAttrValueHasData_ShouldReturnJsonString() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("value1", "value2");
        AttrDTO attrDTO = mock(AttrDTO.class);
        when(attrDTO.getValue()).thenReturn(values);
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getSkuAttrValueJson("testAttr");
        // assert
        assertNotNull(result);
        assertEquals(JSON.toJSONString(values), result);
        verify(attrDTO, times(1)).getValue();
    }

    @Test
    void testGetSkuAttrValueJson_WhenJsonConversionFails_ShouldReturnNull() throws Throwable {
        // arrange
        List<String> values = Arrays.asList("value1", "value2");
        AttrDTO attrDTO = mock(AttrDTO.class);
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        // 使用 spy 来部分模拟 ProductAttr
        ProductAttr productAttr = spy(new ProductAttr(skuAttr));
        // 模拟 JSON.toJSONString 抛出异常
        doThrow(new RuntimeException("JSON conversion error")).when(productAttr).getSkuAttrValueJson("testAttr");
        // act & assert
        assertThrows(RuntimeException.class, () -> productAttr.getSkuAttrValueJson("testAttr"));
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithValidValue() throws Throwable {
        // arrange
        String testKey = "validKey";
        String expectedValue = "testValue";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList(expectedValue));
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(expectedValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithEmptyValue() throws Throwable {
        // arrange
        String testKey = "emptyKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList(""));
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithNullValue() throws Throwable {
        // arrange
        String testKey = "nullKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList(null));
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithEmptyList() throws Throwable {
        // arrange
        String testKey = "emptyListKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Collections.emptyList());
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyExistsWithNullList() throws Throwable {
        // arrange
        String testKey = "nullListKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(null);
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @Test
    public void testGetSafeStringWhenKeyDoesNotExist() throws Throwable {
        // arrange
        String testKey = "nonExistentKey";
        String defaultValue = "default";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    @Test
    public void testGetSafeStringWhenKeyIsNull() throws Throwable {
        // arrange
        String defaultValue = "default";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(null, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    @Test
    public void testGetSafeStringWhenDefaultValueIsNull() throws Throwable {
        // arrange
        String testKey = "anyKey";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, null);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetSafeStringWhenMultipleValuesFirstEmpty() throws Throwable {
        // arrange
        String testKey = "multiValueKey";
        String defaultValue = "default";
        AttrDTO mockAttrDTO = mock(AttrDTO.class);
        when(mockAttrDTO.getValue()).thenReturn(Arrays.asList("", "secondValue"));
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put(testKey, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSafeString(testKey, defaultValue);
        // assert
        assertEquals(defaultValue, result);
        verify(mockAttrDTO, times(1)).getValue();
    }

    @BeforeEach
    void setUp() {
        skuAttr = new HashMap<>();
        productAttr = new ProductAttr(skuAttr);
    }

    @Test
    void testGetSkuAttrValueWhenAttrNotExists() throws Throwable {
        // arrange
        String nonExistingAttr = "nonExistingAttr";
        // act
        List<String> result = productAttr.getSkuAttrValue(nonExistingAttr);
        // assert
        assertNull(result);
    }

    @Test
    void testGetSkuAttrValueWithSingleValue() throws Throwable {
        // arrange
        String attrName = "testAttr";
        String expectedValue = "value1";
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList(expectedValue));
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(expectedValue, result.get(0));
    }

    @Test
    void testGetSkuAttrValueWithMultipleValues() throws Throwable {
        // arrange
        String attrName = "testAttr";
        List<String> expectedValues = Arrays.asList("value1", "value2", "value3");
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(expectedValues);
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNotNull(result);
        assertEquals(expectedValues.size(), result.size());
        assertEquals(expectedValues, result);
    }

    @Test
    void testGetSkuAttrValueWithEmptyList() throws Throwable {
        // arrange
        String attrName = "testAttr";
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.emptyList());
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetSkuAttrValueWithNullValueList() throws Throwable {
        // arrange
        String attrName = "testAttr";
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(null);
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNull(result);
    }

    @Test
    void testGetSkuAttrValueWithArrayFormattedString() throws Throwable {
        // arrange
        String attrName = "testAttr";
        String arrayFormattedValue = "[\"value1\",\"value2\"]";
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList(arrayFormattedValue));
        skuAttr.put(attrName, attrDTO);
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(arrayFormattedValue, result.get(0));
    }

    @Test
    public void testGetSkuAttrValueWhenValueIsEmpty() throws Throwable {
        // arrange
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.emptyList());
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        List<String> result = productAttr.getSkuAttrValue("testAttr");
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetProductAttrWhenSkuAttrIsNull() throws Throwable {
        // arrange
        ProductAttr productAttr = new ProductAttr(null);
        String attrName = "testAttr";
        // act
        Optional<AttrDTO> result = productAttr.getProductAttr(attrName);
        // assert
        assertFalse(result.isPresent(), "当 skuAttr 为 null 时应该返回空的 Optional");
    }

    @Test
    public void testGetProductAttrWhenAttrNameNotExist() throws Throwable {
        // arrange
        Map<String, AttrDTO> mockSkuAttr = mock(Map.class);
        when(mockSkuAttr.get("nonExistAttr")).thenReturn(null);
        ProductAttr productAttr = new ProductAttr(mockSkuAttr);
        String attrName = "nonExistAttr";
        // act
        Optional<AttrDTO> result = productAttr.getProductAttr(attrName);
        // assert
        assertFalse(result.isPresent(), "当 attrName 不存在时应该返回空的 Optional");
    }

    @Test
    public void testGetProductAttrWhenAttrNameExist() throws Throwable {
        // arrange
        Map<String, AttrDTO> mockSkuAttr = mock(Map.class);
        AttrDTO expectedAttr = new AttrDTO();
        expectedAttr.setName("existAttr");
        when(mockSkuAttr.get("existAttr")).thenReturn(expectedAttr);
        ProductAttr productAttr = new ProductAttr(mockSkuAttr);
        String attrName = "existAttr";
        // act
        Optional<AttrDTO> result = productAttr.getProductAttr(attrName);
        // assert
        assertTrue(result.isPresent(), "当 attrName 存在时应该返回包含 AttrDTO 的 Optional");
        assertEquals(expectedAttr, result.get(), "返回的 AttrDTO 应该与预期的相同");
    }

    @Test
    public void testGetProductAttrWhenAttrNameIsNull() throws Throwable {
        // arrange
        Map<String, AttrDTO> mockSkuAttr = mock(Map.class);
        when(mockSkuAttr.get(null)).thenReturn(null);
        ProductAttr productAttr = new ProductAttr(mockSkuAttr);
        // act
        Optional<AttrDTO> result = productAttr.getProductAttr(null);
        // assert
        assertFalse(result.isPresent(), "当 attrName 为 null 时应该返回空的 Optional");
    }

    @Test
    public void testSafeGetSkuAttrWhenSkuAttrIsNull() {
        // arrange
        ProductAttr productAttr = new ProductAttr(null);
        String attrName = "testAttr";
        // act
        AttrDTO result = productAttr.safeGetSkuAttr(attrName);
        // assert
        assertNull(result);
    }

    @Test
    public void testSafeGetSkuAttrWhenAttrNameNotExist() {
        // arrange
        Map<String, AttrDTO> skuAttrMap = new HashMap<>();
        skuAttrMap.put("existingAttr", new AttrDTO());
        ProductAttr productAttr = new ProductAttr(skuAttrMap);
        String nonExistingAttrName = "nonExistingAttr";
        // act
        AttrDTO result = productAttr.safeGetSkuAttr(nonExistingAttrName);
        // assert
        assertNull(result);
    }

    @Test
    public void testSafeGetSkuAttrWhenAttrNameExist() {
        // arrange
        AttrDTO expectedAttr = new AttrDTO();
        expectedAttr.setName("testAttr");
        Map<String, AttrDTO> skuAttrMap = new HashMap<>();
        skuAttrMap.put("testAttr", expectedAttr);
        ProductAttr productAttr = new ProductAttr(skuAttrMap);
        String attrName = "testAttr";
        // act
        AttrDTO result = productAttr.safeGetSkuAttr(attrName);
        // assert
        assertNotNull(result);
        assertEquals(expectedAttr, result);
    }

    @Test
    public void testSafeGetSkuAttrWhenAttrNameIsNull() {
        // arrange
        Map<String, AttrDTO> skuAttrMap = new HashMap<>();
        skuAttrMap.put("testAttr", new AttrDTO());
        ProductAttr productAttr = new ProductAttr(skuAttrMap);
        // act
        AttrDTO result = productAttr.safeGetSkuAttr(null);
        // assert
        assertNull(result);
    }

    @Test
    public void testSafeGetSkuAttrWhenAttrNameIsEmpty() {
        // arrange
        Map<String, AttrDTO> skuAttrMap = new HashMap<>();
        skuAttrMap.put("testAttr", new AttrDTO());
        ProductAttr productAttr = new ProductAttr(skuAttrMap);
        // act
        AttrDTO result = productAttr.safeGetSkuAttr("");
        // assert
        assertNull(result);
    }

    @Test
    public void testGetSkuAttrValueWhenAttrNotExist() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(skuAttr);
        String attrName = "nonExistAttr";
        // act
        List<String> result = productAttr.getSkuAttrValue(attrName);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetSkuAttrValueWhenNotSingleValueArray() throws Throwable {
        // arrange
        String attrName = "normalAttr";
        List<String> expectedValues = Arrays.asList("value1", "value2");
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(expectedValues);
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put(attrName, attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(() -> LionConfigUtils.isSingleValueArrayAttr(attrName)).thenReturn(false);
            // act
            List<String> result = productAttr.getSkuAttrValue(attrName);
            // assert
            assertEquals(expectedValues, result);
            mocked.verify(() -> LionConfigUtils.isSingleValueArrayAttr(attrName));
        }
    }

    @Test
    public void testGetSkuAttrValueWhenSingleValueArrayValid() throws Throwable {
        // arrange
        String attrName = "arrayAttr";
        String jsonArray = "[\"item1\",\"item2\"]";
        List<String> expectedValues = Arrays.asList("item1", "item2");
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList(jsonArray));
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put(attrName, attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(() -> LionConfigUtils.isSingleValueArrayAttr(attrName)).thenReturn(true);
            // act
            List<String> result = productAttr.getSkuAttrValue(attrName);
            // assert
            assertEquals(expectedValues, result);
            mocked.verify(() -> LionConfigUtils.isSingleValueArrayAttr(attrName));
        }
    }

    @Test
    public void testGetSkuAttrValueWhenSingleValueArrayInvalid() throws Throwable {
        // arrange
        String attrName = "arrayAttr";
        List<String> expectedValues = Arrays.asList("not a json array");
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(expectedValues);
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put(attrName, attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        try (MockedStatic<LionConfigUtils> mocked = mockStatic(LionConfigUtils.class)) {
            mocked.when(() -> LionConfigUtils.isSingleValueArrayAttr(attrName)).thenReturn(true);
            // act
            List<String> result = productAttr.getSkuAttrValue(attrName);
            // assert
            assertEquals(expectedValues, result);
            mocked.verify(() -> LionConfigUtils.isSingleValueArrayAttr(attrName));
        }
    }
}
