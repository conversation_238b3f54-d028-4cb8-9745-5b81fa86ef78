package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.third.part;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupThirdPartyBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealThirdPartyBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.third.part.dto.SkuThirdPartyDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupThirdPartyDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Test class for ProductThirdPartInfoFetcher's fulfillRequest method
 */
@ExtendWith(MockitoExtension.class)
public class ProductThirdPartInfoFetcherTest {

    @Mock
    private QueryByDealGroupIdRequestBuilder requestBuilder;

    @InjectMocks
    private ProductThirdPartInfoFetcher fetcher;

    @Mock
    private ProductDetailPageRequest request;

    /**
     * Test normal execution of fulfillRequest method
     * Verifies that both builders are called with correct parameters
     */
    @Test
    public void testFulfillRequest_NormalCase() throws Throwable {
        // arrange
        ProductThirdPartInfoFetcher fetcher = new ProductThirdPartInfoFetcher() {

            @Override
            protected FetcherResponse<ProductThirdPartInfo> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
                // Not needed for this test
                return null;
            }
        };
        when(requestBuilder.dealGroupThirdPart(any(DealGroupThirdPartyBuilder.class))).thenReturn(requestBuilder);
        when(requestBuilder.dealThirdParty(any(DealThirdPartyBuilder.class))).thenReturn(requestBuilder);
        // act
        fetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder).dealGroupThirdPart(any(DealGroupThirdPartyBuilder.class));
        verify(requestBuilder).dealThirdParty(any(DealThirdPartyBuilder.class));
    }

    /**
     * Test fulfillRequest method with null requestBuilder parameter
     * Expects NullPointerException to be thrown
     */
    @Test
    public void testFulfillRequest_NullRequestBuilder() throws Throwable {
        // arrange
        ProductThirdPartInfoFetcher fetcher = new ProductThirdPartInfoFetcher() {

            @Override
            protected FetcherResponse<ProductThirdPartInfo> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
                // Not needed for this test
                return null;
            }
        };
        // act & assert
        assertThrows(NullPointerException.class, () -> fetcher.fulfillRequest(null));
    }

    @Test
    public void testMapResult_NullAggregateResult() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = null;
        // act
        FetcherResponse<ProductThirdPartInfo> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    @Test
    public void testMapResult_NullReturnValue() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(null);
        // act
        FetcherResponse<ProductThirdPartInfo> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    @Test
    public void testMapResult_NullDealGroupDTO() throws Throwable {
        // arrange
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(null);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductThirdPartInfo> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    @Test
    public void testMapResult_ValidDealGroupNoDeals() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupThirdPartyDTO thirdPartyInfo = new DealGroupThirdPartyDTO();
        dealGroupDTO.setThirdPartyInfo(thirdPartyInfo);
        // no deals
        dealGroupDTO.setDeals(null);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        // act
        FetcherResponse<ProductThirdPartInfo> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getProductThirdPartyInfo().isPresent());
        assertEquals(thirdPartyInfo, result.getReturnValue().getProductThirdPartyInfo().get());
        assertTrue(result.getReturnValue().getSkuThirdPartyInfoMap().isEmpty());
    }

    @Test
    public void testMapResult_ValidDealGroupWithDeals() throws Throwable {
        // arrange
        DealGroupThirdPartyDTO thirdPartyInfo = new DealGroupThirdPartyDTO();
        thirdPartyInfo.setThirdPartyId(123L);
        thirdPartyInfo.setThirdPartyProductId(456L);
        DealGroupDealDTO deal1 = new DealGroupDealDTO();
        deal1.setDealId(1001L);
        DealBasicDTO basic1 = new DealBasicDTO();
        basic1.setThirdPartyId(789L);
        deal1.setBasic(basic1);
        DealGroupDealDTO deal2 = new DealGroupDealDTO();
        deal2.setDealId(1002L);
        DealBasicDTO basic2 = new DealBasicDTO();
        basic2.setThirdPartyId(790L);
        deal2.setBasic(basic2);
        List<DealGroupDealDTO> deals = new ArrayList<>();
        deals.add(deal1);
        deals.add(deal2);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setThirdPartyInfo(thirdPartyInfo);
        dealGroupDTO.setDeals(deals);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        // act
        FetcherResponse<ProductThirdPartInfo> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getProductThirdPartyInfo().isPresent());
        assertEquals(thirdPartyInfo, result.getReturnValue().getProductThirdPartyInfo().get());
        assertEquals(2, result.getReturnValue().getSkuThirdPartyInfoMap().size());
        assertTrue(result.getReturnValue().getSkuThirdPartyInfoMap().containsKey(1001L));
        assertTrue(result.getReturnValue().getSkuThirdPartyInfoMap().containsKey(1002L));
        assertEquals(789L, result.getReturnValue().getSkuThirdPartyInfoMap().get(1001L).getThirdPartyId());
        assertEquals(790L, result.getReturnValue().getSkuThirdPartyInfoMap().get(1002L).getThirdPartyId());
    }

    @Test
    public void testMapResult_FunProductType() throws Throwable {
        // arrange
        DealGroupThirdPartyDTO thirdPartyInfo = new DealGroupThirdPartyDTO();
        thirdPartyInfo.setThirdPartyId(123L);
        DealGroupDealDTO deal = new DealGroupDealDTO();
        deal.setBizDealId(2001L);
        DealBasicDTO basic = new DealBasicDTO();
        basic.setThirdPartyId(456L);
        deal.setBasic(basic);
        List<DealGroupDealDTO> deals = new ArrayList<>();
        deals.add(deal);
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setThirdPartyInfo(thirdPartyInfo);
        dealGroupDTO.setDeals(deals);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.RESERVE);
        // act
        FetcherResponse<ProductThirdPartInfo> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getProductThirdPartyInfo().isPresent());
        assertEquals(thirdPartyInfo, result.getReturnValue().getProductThirdPartyInfo().get());
        assertEquals(1, result.getReturnValue().getSkuThirdPartyInfoMap().size());
        assertTrue(result.getReturnValue().getSkuThirdPartyInfoMap().containsKey(2001L));
        assertEquals(456L, result.getReturnValue().getSkuThirdPartyInfoMap().get(2001L).getThirdPartyId());
    }
}
