package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProductAttrGetSkuAttrFirstValueTest {

    @Mock
    private AttrDTO mockAttrDTO;

    /**
     * 测试当属性存在且值列表有多个元素时，返回第一个元素
     */
    @Test
    void testGetSkuAttrFirstValue_WhenValueHasMultipleElements_ShouldReturnFirstElement() throws Throwable {
        // arrange
        String attrName = "testAttr";
        String expectedValue = "value1";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(mockAttrDTO.getValue()).thenReturn(Arrays.asList(expectedValue, "value2", "value3"));
        attrMap.put(attrName, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSkuAttrFirstValue(attrName);
        // assert
        assertEquals(expectedValue, result);
    }

    /**
     * 测试当属性存在且值列表只有一个元素时，返回该元素
     */
    @Test
    void testGetSkuAttrFirstValue_WhenValueHasSingleElement_ShouldReturnTheElement() throws Throwable {
        // arrange
        String attrName = "testAttr";
        String expectedValue = "value1";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList(expectedValue));
        attrMap.put(attrName, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSkuAttrFirstValue(attrName);
        // assert
        assertEquals(expectedValue, result);
    }

    /**
     * 测试当属性存在但值列表为空时，返回null
     */
    @Test
    void testGetSkuAttrFirstValue_WhenValueIsEmpty_ShouldReturnNull() throws Throwable {
        // arrange
        String attrName = "testAttr";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(mockAttrDTO.getValue()).thenReturn(Collections.emptyList());
        attrMap.put(attrName, mockAttrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSkuAttrFirstValue(attrName);
        // assert
        assertNull(result);
    }

    /**
     * 测试当属性不存在时，返回null
     */
    @Test
    void testGetSkuAttrFirstValue_WhenAttrNotExist_ShouldReturnNull() throws Throwable {
        // arrange
        String attrName = "nonExistAttr";
        Map<String, AttrDTO> attrMap = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        String result = productAttr.getSkuAttrFirstValue(attrName);
        // assert
        assertNull(result);
    }

    /**
     * 测试当skuAttr为null时，返回null
     */
    @Test
    void testGetSkuAttrFirstValue_WhenSkuAttrIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        String attrName = "testAttr";
        ProductAttr productAttr = new ProductAttr(null);
        // act
        String result = productAttr.getSkuAttrFirstValue(attrName);
        // assert
        assertNull(result);
    }
}
