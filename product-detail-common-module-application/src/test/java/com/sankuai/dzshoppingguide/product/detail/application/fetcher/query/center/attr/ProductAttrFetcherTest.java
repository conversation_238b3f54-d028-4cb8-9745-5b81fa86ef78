package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.service.project.ProductServiceProject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealAttrHelper;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;

import java.util.*;

import org.junit.jupiter.api.Test;

/**
 * Test for ProductAttrFetcher.mapResult method
 */
class ProductAttrFetcherTest {

    // Previous test cases remain the same...
    /**
     * Test when DealGroupDTO has valid attrs list
     */
    @Test
    public void testMapResultValidAttrsList() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO mockDealGroup = mock(DealGroupDTO.class);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("color");
        attr1.setValue(Arrays.asList("red", "blue"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("size");
        attr2.setValue(Arrays.asList("S", "M", "L"));
        attrs.add(attr2);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(mockDealGroup);
        when(mockDealGroup.getAttrs()).thenReturn(attrs);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Map<String, AttrDTO> resultAttrs = result.getReturnValue().getSkuAttr();
        assertEquals(2, resultAttrs.size());
        assertEquals(attr1, resultAttrs.get("color"));
        assertEquals(attr2, resultAttrs.get("size"));
    }

    /**
     * Test when DealGroupDTO has duplicate attr names
     */
    @Test
    public void testMapResultDuplicateAttrNames() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO mockDealGroup = mock(DealGroupDTO.class);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("color");
        attr1.setValue(Arrays.asList("red", "blue"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        // duplicate name
        attr2.setName("color");
        attr2.setValue(Arrays.asList("green", "yellow"));
        attrs.add(attr2);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(mockDealGroup);
        when(mockDealGroup.getAttrs()).thenReturn(attrs);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Map<String, AttrDTO> resultAttrs = result.getReturnValue().getSkuAttr();
        assertEquals(1, resultAttrs.size());
        // Should keep the first one
        assertEquals(attr1, resultAttrs.get("color"));
        assertNotEquals(attr2, resultAttrs.get("color"));
    }

    /**
     * Test when input response is null
     */
    @Test
    public void testMapResultNullResponse() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(null);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }

    /**
     * Test when response has null return value
     */
    @Test
    public void testMapResultNullReturnValue() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        when(mockResponse.getReturnValue()).thenReturn(null);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }

    /**
     * Test when return value has null DealGroupDTO
     */
    @Test
    public void testMapResultNullDealGroupDTO() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(null);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }

    /**
     * Test when DealGroupDTO has null attrs list
     */
    @Test
    public void testMapResultNullAttrsList() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO mockDealGroup = mock(DealGroupDTO.class);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(mockDealGroup);
        when(mockDealGroup.getAttrs()).thenReturn(null);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }

    /**
     * Test when DealGroupDTO has empty attrs list
     */
    @Test
    public void testMapResultEmptyAttrsList() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> mockResponse = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue mockReturnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO mockDealGroup = mock(DealGroupDTO.class);
        when(mockResponse.getReturnValue()).thenReturn(mockReturnValue);
        when(mockReturnValue.getDealGroupDTO()).thenReturn(mockDealGroup);
        when(mockDealGroup.getAttrs()).thenReturn(new ArrayList<>());
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(mockResponse);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttr().isEmpty());
    }

    @Test
    public void testMapResultNullAttrs() throws Throwable {
        String json = "{\"serviceProject\":{\"marketPrice\":\"298.0\",\"mustGroups\":[{\"groups\":[{\"amount\":1,\"attrs\":[{\"attrName\":\"period\",\"attrValue\":\"白天档、黄金档、凌晨档\",\"chnName\":\"时段\",\"metaAttrId\":573,\"rawAttrValue\":\"白天档、黄金档、凌晨档\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"start_time\",\"attrValue\":\"17:00\",\"chnName\":\"开始时间\",\"metaAttrId\":571,\"rawAttrValue\":\"17:00\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"end_time\",\"attrValue\":\"30:00\",\"chnName\":\"结束时间\",\"metaAttrId\":161367,\"rawAttrValue\":\"30:00\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"unit\",\"attrValue\":\"小时\",\"chnName\":\"时长单位\",\"metaAttrId\":1471,\"rawAttrValue\":\"小时\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"skuCateId\",\"attrValue\":\"2104685\",\"chnName\":\"项目分类\",\"metaAttrId\":2730,\"rawAttrValue\":\"2104685\",\"sequence\":0,\"valueType\":402},{\"attrName\":\"sellingForm\",\"attrValue\":\"按房间售卖\",\"chnName\":\"售卖形式\",\"metaAttrId\":10574531,\"rawAttrValue\":\"按房间售卖\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"numberOfPeopleOrder\",\"attrValue\":\"111\",\"chnName\":\"人数起订\",\"metaAttrId\":10574532,\"rawAttrValue\":\"111\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"timeDuration\",\"attrValue\":\"8\",\"chnName\":\"时长\",\"metaAttrId\":1633,\"rawAttrValue\":\"8\",\"sequence\":0,\"valueType\":400},{\"attrName\":\"TimePeriodMode\",\"attrValue\":\"包段模式\",\"chnName\":\"时段模式\",\"metaAttrId\":10573038,\"rawAttrValue\":\"包段模式\",\"sequence\":0,\"valueType\":500},{\"attrName\":\"ktv_roomids\",\"attrValue\":\"10390651、10390629、10316916、10316917\",\"chnName\":\"包型\",\"metaAttrId\":10695554,\"rawAttrValue\":\"10390651、10390629、10316916、10316917\",\"sequence\":0,\"valueType\":500}],\"categoryId\":2104685,\"marketPrice\":\"298.0\",\"name\":\"全天档8小时\",\"skuId\":0,\"status\":10}]}],\"optionGroups\":[],\"salePrice\":\"80.0\",\"structType\":\"uniform-structure-table\",\"title\":\"团购详情\"}}";
        ProductServiceProject serviceProjectDTO = JSONObject.parseObject(json, ProductServiceProject.class);
        Set<String> roomIds = DealAttrHelper.getServiceProjectAttr(serviceProjectDTO, "ktv_roomids", "、");
        assertNotNull(roomIds);
    }
}
