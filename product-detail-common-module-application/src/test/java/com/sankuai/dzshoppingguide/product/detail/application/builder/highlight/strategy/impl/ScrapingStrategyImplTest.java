package com.sankuai.dzshoppingguide.product.detail.application.builder.highlight.strategy.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import com.sankuai.general.product.query.center.client.dto.serviceProject.ServiceProjectAttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ScrapingStrategyImplTest {

    private final ScrapingStrategyImpl strategy = new ScrapingStrategyImpl();

    /**
     * 测试当存在scrapingTool属性时，直接返回scrapingTool的值
     */
    @Test
    public void testGetToolValue_WhenScrapingToolExists_ReturnScrapingToolValue() throws Throwable {
        // arrange
        ServiceProjectAttrDTO toolAttr = new ServiceProjectAttrDTO();
        toolAttr.setAttrName("scrapingTool");
        toolAttr.setAttrValue("刮痧板");
        ServiceProjectAttrDTO materialAttr = new ServiceProjectAttrDTO();
        materialAttr.setAttrName("scrapingMaterial");
        materialAttr.setAttrValue("刮痧油");
        List<ServiceProjectAttrDTO> attrs = Arrays.asList(toolAttr, materialAttr);
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertEquals("刮痧板", result);
    }

    /**
     * 测试当不存在scrapingTool但存在scrapingMaterial时，返回scrapingMaterial的值
     */
    @Test
    public void testGetToolValue_WhenOnlyScrapingMaterialExists_ReturnScrapingMaterialValue() throws Throwable {
        // arrange
        ServiceProjectAttrDTO materialAttr = new ServiceProjectAttrDTO();
        materialAttr.setAttrName("scrapingMaterial");
        materialAttr.setAttrValue("刮痧油");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(materialAttr);
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertEquals("刮痧油", result);
    }

    /**
     * 测试当两个属性都不存在时，返回null
     */
    @Test
    public void testGetToolValue_WhenNoAttributesExist_ReturnNull() throws Throwable {
        // arrange
        ServiceProjectAttrDTO otherAttr = new ServiceProjectAttrDTO();
        otherAttr.setAttrName("otherAttr");
        otherAttr.setAttrValue("其他值");
        List<ServiceProjectAttrDTO> attrs = Collections.singletonList(otherAttr);
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertNull(result);
    }

    /**
     * 测试当输入参数为null时，抛出NullPointerException
     */
    @Test
    public void testGetToolValue_WhenInputIsNull_ReturnNull() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> strategy.getToolValue(attrs));
    }

    /**
     * 测试当输入列表为空时，返回null
     */
    @Test
    public void testGetToolValue_WhenInputIsEmpty_ReturnNull() throws Throwable {
        // arrange
        List<ServiceProjectAttrDTO> attrs = Collections.emptyList();
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertNull(result);
    }

    /**
     * 测试当属性值为空字符串时的情况
     */
    @Test
    public void testGetToolValue_WhenAttributeValueIsEmpty_ReturnMaterialValue() throws Throwable {
        // arrange
        ServiceProjectAttrDTO toolAttr = new ServiceProjectAttrDTO();
        toolAttr.setAttrName("scrapingTool");
        toolAttr.setAttrValue("");
        ServiceProjectAttrDTO materialAttr = new ServiceProjectAttrDTO();
        materialAttr.setAttrName("scrapingMaterial");
        materialAttr.setAttrValue("刮痧油");
        List<ServiceProjectAttrDTO> attrs = Arrays.asList(toolAttr, materialAttr);
        // act
        String result = strategy.getToolValue(attrs);
        // assert
        assertEquals("刮痧油", result);
    }
}
