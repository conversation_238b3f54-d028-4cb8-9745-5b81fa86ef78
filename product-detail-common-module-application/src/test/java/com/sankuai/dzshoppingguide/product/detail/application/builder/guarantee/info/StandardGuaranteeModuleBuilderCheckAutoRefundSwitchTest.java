package com.sankuai.dzshoppingguide.product.detail.application.builder.guarantee.info;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo.ProductDetailGuaranteeVO;
import com.sankuai.general.product.query.center.client.dto.rule.DealGroupRuleDTO;
import com.sankuai.general.product.query.center.client.dto.rule.refund.DealGroupRefundRuleDTO;
import java.util.Objects;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for StandardGuaranteeModuleBuilder.checkAutoRefundSwitch method
 */
@ExtendWith(MockitoExtension.class)
public class StandardGuaranteeModuleBuilderCheckAutoRefundSwitchTest {

    @InjectMocks
    private StandardGuaranteeModuleBuilder builder = new StandardGuaranteeModuleBuilder() {

        @Override
        public ProductDetailGuaranteeVO doBuild() {
            return null;
        }
    };

    @Mock
    private ProductBaseInfo productBaseInfo;

    @Mock
    private DealGroupRuleDTO rule;

    @Mock
    private DealGroupRefundRuleDTO refundRule;

    /**
     * Test when productBaseInfo is null
     */
    @Test
    public void testCheckAutoRefundSwitch_WhenProductBaseInfoIsNull() {
        // arrange
        productBaseInfo = null;
        // act
        boolean result = builder.checkAutoRefundSwitch(productBaseInfo);
        // assert
        assertFalse(result);
    }

    /**
     * Test when rule is null
     */
    @Test
    public void testCheckAutoRefundSwitch_WhenRuleIsNull() {
        // arrange
        when(productBaseInfo.getRule()).thenReturn(null);
        // act
        boolean result = builder.checkAutoRefundSwitch(productBaseInfo);
        // assert
        assertFalse(result);
    }

    /**
     * Test when refundRule is null
     */
    @Test
    public void testCheckAutoRefundSwitch_WhenRefundRuleIsNull() {
        // arrange
        when(productBaseInfo.getRule()).thenReturn(rule);
        when(rule.getRefundRule()).thenReturn(null);
        // act
        boolean result = builder.checkAutoRefundSwitch(productBaseInfo);
        // assert
        assertFalse(result);
    }

    /**
     * Test when supportRefundType = 0 (不支持退款)
     */
    @Test
    public void testCheckAutoRefundSwitch_WhenSupportRefundTypeIs0() {
        // arrange
        when(productBaseInfo.getRule()).thenReturn(rule);
        when(rule.getRefundRule()).thenReturn(refundRule);
        when(refundRule.getSupportRefundType()).thenReturn(0);
        // act
        boolean result = builder.checkAutoRefundSwitch(productBaseInfo);
        // assert
        assertFalse(result);
    }

    /**
     * Test when supportRefundType = 1 (支持随时退)
     */
    @Test
    public void testCheckAutoRefundSwitch_WhenSupportRefundTypeIs1() {
        // arrange
        when(productBaseInfo.getRule()).thenReturn(rule);
        when(rule.getRefundRule()).thenReturn(refundRule);
        when(refundRule.getSupportRefundType()).thenReturn(1);
        // act
        boolean result = builder.checkAutoRefundSwitch(productBaseInfo);
        // assert
        assertTrue(result);
    }

    /**
     * Test when supportRefundType = 2 (支持7天退换)
     */
    @Test
    public void testCheckAutoRefundSwitch_WhenSupportRefundTypeIs2() {
        // arrange
        when(productBaseInfo.getRule()).thenReturn(rule);
        when(rule.getRefundRule()).thenReturn(refundRule);
        when(refundRule.getSupportRefundType()).thenReturn(2);
        // act
        boolean result = builder.checkAutoRefundSwitch(productBaseInfo);
        // assert
        assertTrue(result);
    }

    protected boolean checkAutoRefundSwitch(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getRule()) || Objects.isNull(productBaseInfo.getRule().getRefundRule())) {
            return false;
        }
        // 支持退款类型，0-不支持退款 1-支持随时退 2-支持7天退换
        return productBaseInfo.getRule().getRefundRule().getSupportRefundType() > 0;
    }
}
