package com.sankuai.dzshoppingguide.product.detail.domain.query.center;

import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.dz.product.detail.gateway.spi.enums.PlatformEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/17 17:22
 */
@Component
public class QueryCenterAclService {

    @MdpThriftClient(
            timeout = 1000, testTimeout = 5000,
            remoteAppKey = "com.sankuai.productuser.query.center",
            async = true)
    public DealGroupQueryService dealGroupQueryService;

    public static QueryByDealGroupIdRequestBuilder getBaseRequestBuilder(final ProductDetailPageRequest request) {
        IdTypeEnum idTypeEnum = getIdTypeEnum(request);
        if (request.getProductTypeEnum() == ProductTypeEnum.DEAL) {
            return QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(request.getProductId()), idTypeEnum);
        } else if (request.getProductTypeEnum() == ProductTypeEnum.RESERVE) {
            return QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(request.getProductId()), idTypeEnum);
        } else {
            throw new IllegalArgumentException("暂不支持该商品类型:" + request.getProductTypeEnum().name());
        }
    }

    private static IdTypeEnum getIdTypeEnum(ProductDetailPageRequest detailPageRequest) {
        if (detailPageRequest.getProductType() == ProductTypeEnum.DEAL.getCode()) {
            return detailPageRequest.getPlatformEnum() == PlatformEnum.MT ? IdTypeEnum.MT : IdTypeEnum.DP;
        }
        return IdTypeEnum.BIZ_PRODUCT;
    }

    public CompletableFuture<QueryDealGroupListResponse> query(final QueryByDealGroupIdRequest queryCenterRequest) throws TException {
        dealGroupQueryService.queryByDealGroupIds(queryCenterRequest);
        return ThriftAsyncUtils.getThriftFuture();
    }

}
