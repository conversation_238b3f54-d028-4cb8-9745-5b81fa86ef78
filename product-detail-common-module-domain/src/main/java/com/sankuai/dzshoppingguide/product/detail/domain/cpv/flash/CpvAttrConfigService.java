package com.sankuai.dzshoppingguide.product.detail.domain.cpv.flash;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
import com.sankuai.dzshoppingguide.product.detail.spi.inventory.vo.InventoryInfoDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
public class CpvAttrConfigService implements InitializingBean {
    private final String PRODUCT_EXTRA_ATTR_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.product.extra.attr.config";

    private Map<String, Set<InventoryInfoDTO>> attrConfigMap = new ConcurrentHashMap<>();

    private void parse(String json) {
        Map<String, Set<InventoryInfoDTO>> map = JSONObject.parseObject(json, new TypeReference<Map<String, Set<InventoryInfoDTO>>>() {
        });
        if (map == null) {
            throw new ProductDetailFatalError("FATAL ERROR!!!查询中心attr配置为空!!!");
        }
        this.attrConfigMap = map;
    }

    /**
     * 获取商品补充属性配置key
     * @param secondProductCategory 二级类目ID
     * @return 补充属性key集合
     */
    public Set<String> getProductExtraAttrs(int secondProductCategory) {
        return attrConfigMap.getOrDefault(String.valueOf(secondProductCategory), Collections.emptySet())
                .stream()
                .filter(Objects::nonNull)
                .map(InventoryInfoDTO::getKey)
                .collect(Collectors.toSet());
    }

    /**
     * 获取商品补充属性配置
     * @param secondProductCategory 二级类目ID
     * @return 补充属性配置集合
     */
    public Set<InventoryInfoDTO> getProductExtraAttrDTOs(int secondProductCategory) {
        return attrConfigMap.getOrDefault(String.valueOf(secondProductCategory), Collections.emptySet());
    }

    public Map<String, Integer> getCategoryPriority(int secondProductCategory) {
        Set<InventoryInfoDTO> productExtraAttrDTOs = getProductExtraAttrDTOs(secondProductCategory);
        if (CollectionUtils.isEmpty(productExtraAttrDTOs)) {
            return Maps.newHashMap();
        }
        return productExtraAttrDTOs.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(InventoryInfoDTO::getName, InventoryInfoDTO::getPriority, (v1, v2) -> v1));
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        parse(Lion.getString(Environment.getAppName(), PRODUCT_EXTRA_ATTR_CONFIG));
        Lion.addConfigListener(PRODUCT_EXTRA_ATTR_CONFIG, configEvent -> parse(configEvent.getValue()));
    }
}
