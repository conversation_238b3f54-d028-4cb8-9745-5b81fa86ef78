package com.sankuai.dzshoppingguide.product.detail.spi.common;

import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/2/20 15:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CommonDataVO extends AbstractModuleVO {

    /**
     * 商品类型，团购、预订、预付等
     */
    private int productType;
    /**
     * 点评商品id
     */
    private long dpProductId;
    /**
     * 美团商品id
     */
    private long mtProductId;
    /**
     * 商品id
     */
    private long productId;
    /**
     * 一级分类
     */
    private int productFirstCategoryId;
    /**
     * 商品二级分类
     */
    private int productSecondCategoryId;
    /**
     * 商品三级分类
     */
    private int productThirdCategoryId;
    /**
     * 点评商户id
     */
    private long dpShopId;
    /**
     * 美团商户id
     */
    private long mtShopId;
    /**
     * 商户id
     */
    private long shopId;
    /**
     * 商户二级类目
     */
    private int shopCategoryId;

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.COMMON_DATA;
    }
}
