# 商品详情页通用模块查询流程分析

## 功能概述
`ProductDetailPageCommonModuleSpiImpl#query` 是商品详情页通用模块查询的核心入口方法，负责根据请求的模块键（ModuleKeys）动态构建并返回对应的商品详情页模块数据。该方法基于模块编排框架，通过 Fetcher-Builder 架构模式实现高性能、高扩展性的模块化服务。

## 入口方法
`com.sankuai.dzshoppingguide.product.detail.application.spi.ProductDetailPageCommonModuleSpiImpl#query`

## 方法调用树
```
ProductDetailPageCommonModuleSpiImpl#query
├─ request.checkParam() - 参数校验
├─ ModuleArrangeFrameworkRunner.run(moduleArrangeRequest) - 模块编排框架执行
│  ├─ 依赖解析阶段
│  │  ├─ 解析Builder对Fetcher的依赖关系（dependentFetchers）
│  │  ├─ 解析Fetcher间的依赖关系（previousLayerDependencies）
│  │  └─ 构建DAG（有向无环图）确定执行顺序
│  ├─ Fetcher层执行阶段
│  │  ├─ CommonModuleStarter.doFetch() - 启动Fetcher（isStartFetcher=true）
│  │  ├─ QueryCenterAggregateFetcher.initFuture() - 查询中心聚合Fetcher
│  │  │  └─ queryCenterAclService.query(queryCenterRequest) - 调用查询中心服务
│  │  ├─ ProductBaseInfoFetcher.doFetch() - 商品基础信息获取
│  │  ├─ ProductCategoryFetcher.doFetch() - 商品类目信息获取
│  │  ├─ ShopInfoFetcher.doFetch() - 门店信息获取
│  │  │  ├─ sinaiDpPoiService.findShopsByShopIds() - 点评门店信息查询
│  │  │  └─ sinaiMtPoiService.findShopsByShopIds() - 美团门店信息查询
│  │  └─ [其他业务Fetcher并行执行]
│  └─ Builder层执行阶段
│     ├─ CommonDataBuilder.doBuild() - 通用数据模块构建
│     ├─ TitleModuleBuilder.doBuild() - 标题模块构建
│     ├─ TagsModuleBuilder.doBuild() - 标签模块构建
│     └─ [其他模块Builder并行执行]
├─ result.getProductDetailPageResponse() - 获取最终响应
└─ 异常处理与日志记录
```

## 详细业务流程

### 1. 请求预处理阶段
- **参数校验**：调用 `request.checkParam()` 验证请求参数的合法性
- **模块键校验**：检查 `request.getModuleKeys()` 是否为空，为空则抛出异常
- **默认模块添加**：自动添加 `ModuleKeyConstants.COMMON_DATA` 通用数据模块到请求中
- **请求封装**：将原始请求和模块键集合封装为 `ModuleArrangeRequest`

### 2. 模块编排框架执行阶段

#### 2.1 依赖解析子阶段
- **Builder依赖解析**：扫描所有 `@Builder` 注解，解析每个Builder在 `dependentFetchers` 中声明的Fetcher依赖
- **Fetcher依赖解析**：扫描所有 `@Fetcher` 注解，解析每个Fetcher在 `previousLayerDependencies` 中声明的前置依赖
- **DAG构建**：基于依赖关系构建有向无环图，确定组件执行顺序，避免循环依赖

#### 2.2 Fetcher层数据获取子阶段
按照依赖关系顺序执行，同层级Fetcher并行执行：

**启动层**：
- `CommonModuleStarter`：作为整个流程的起点，返回空的启动标识

**聚合查询层**：
- `QueryCenterAggregateFetcher`：构建查询中心请求，调用 `queryCenterAclService.query()` 获取商品核心数据

**基础数据层**：
- `ProductBaseInfoFetcher`：从聚合结果中提取商品基础信息（标题、价格、图片等）
- `ProductCategoryFetcher`：从请求自定义参数中获取商品类目信息
- `ShopIdMapperFetcher`：获取门店ID映射关系

**扩展数据层**：
- `ShopInfoFetcher`：并行调用点评和美团门店服务，获取门店详细信息
- `SkuAttrFetcher`：获取SKU属性信息
- `ProductAttrFetcher`：获取商品属性信息
- `RankTagFetcher`：获取排行标签数据
- `ReviewTagFetcher`：获取评价标签数据

#### 2.3 Builder层模块构建子阶段
等待依赖的Fetcher完成后，并行执行模块构建：

**通用数据模块**：
- `CommonDataBuilder.doBuild()`：
  - 获取 `ProductBaseInfo`、`ProductCategory`、`ShopInfo` 依赖结果
  - 构建 `CommonDataVO` 领域模型，包含商品ID、类目ID、门店ID等打点统计数据

**标题模块**：
- `TitleModuleBuilder.doBuild()`：
  - 获取商品基础信息，提取商品标题
  - 处理特殊业务场景（如商场内餐厅代金券标题处理）
  - 构建 `TitleModuleVO` 领域模型

**标签模块**：
- `TagsModuleBuilder.doBuild()`：
  - 获取排行标签和评价标签数据
  - 构建排行标签VO和评价标签VO
  - 聚合为 `TagsModuleVO` 领域模型

**工厂模式模块**：
- `BaseBuilderFactory.selectVariableBuilder()`：根据业务规则选择具体的 `BaseVariableBuilder` 实现
- 执行选中的 `BaseVariableBuilder.doBuild()` 方法构建模块

### 3. 结果聚合与响应阶段
- **模块结果收集**：框架自动收集所有Builder的构建结果
- **响应对象构建**：将模块结果聚合为 `ProductDetailPageResponse`
- **日志记录**：在特定环境下记录请求日志用于调试

### 4. 异常处理阶段
- **业务异常捕获**：捕获执行过程中的所有异常
- **失败响应构建**：调用 `ProductDetailPageResponse.fail()` 构建失败响应
- **错误日志记录**：记录异常信息和请求参数

## 关键业务规则

### 模块编排规则
- **模块按需加载**：只构建请求中指定的模块，提高性能
- **默认模块保证**：通用数据模块始终被包含，确保基础打点数据完整
- **依赖自动管理**：框架根据注解自动管理组件间依赖关系

### 工厂选择规则
- **类目驱动选择**：`BaseBuilderFactory` 根据商品类目选择对应的 `BaseVariableBuilder`
- **业务场景适配**：支持不同业务场景下的个性化模块实现
- **兜底策略保证**：提供默认Builder确保模块始终有实现

### 异步执行规则
- **Fetcher异步执行**：所有Fetcher返回 `CompletableFuture` 实现异步并行
- **Builder等待机制**：Builder等待其依赖的所有Fetcher完成后才执行
- **超时控制**：支持Fetcher级别的超时配置

## 数据流转

### 输入数据
- **ProductDetailPageRequest**：包含商品ID、商品类型、客户端类型、模块键列表等
- **ModuleKeys**：指定需要构建的模块列表

### 核心数据处理
- **查询中心数据**：通过 `QueryCenterAggregateFetcher` 获取商品核心数据
- **门店数据聚合**：并行获取点评和美团门店信息并聚合
- **业务数据转换**：将原始数据转换为业务领域模型

### 输出数据
- **ProductDetailPageResponse**：包含所有请求模块的领域模型数据
- **核心领域模型**：
  - `CommonDataVO`：通用打点数据
  - `TitleModuleVO`：标题模块数据
  - `TagsModuleVO`：标签模块数据
  - 其他业务模块VO

## 扩展点/分支逻辑

### 模块扩展点
- **新增模块**：通过添加新的Builder实现扩展新模块
- **模块个性化**：通过工厂模式实现不同类目下的个性化逻辑

### 业务分支逻辑
- **商品类型分支**：根据商品类型（团购/预订）执行不同逻辑
- **客户端类型分支**：根据客户端类型（点评/美团）获取对应数据
- **特殊场景处理**：如商场内餐厅代金券的标题特殊处理

## 外部依赖

### 核心服务依赖
- **查询中心服务**：`queryCenterAclService` - 获取商品核心数据
- **点评门店服务**：`sinaiDpPoiService` - 获取点评门店信息  
- **美团门店服务**：`sinaiMtPoiService` - 获取美团门店信息

### 框架依赖
- **模块编排框架**：`ModuleArrangeFrameworkRunner` - 提供Fetcher-Builder执行引擎
- **配置服务**：`Lion` - 提供环境配置和开关控制

## 注意事项

### 性能考虑
- 框架支持Fetcher和Builder的并行执行，最大化性能
- 合理设置Fetcher超时时间，避免长时间等待
- 只请求必要的模块，避免不必要的数据获取和计算

### 异常处理
- 单个模块异常不影响其他模块的正常执行
- 提供完善的异常日志记录，便于问题定位
- 关键依赖异常会导致整个请求失败

### 扩展性设计
- 通过注解驱动的方式，新增模块无需修改框架代码
- 工厂模式支持业务个性化扩展
- 依赖注入机制支持灵活的组件替换和测试
