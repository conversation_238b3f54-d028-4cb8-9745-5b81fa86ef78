## 目标
根据代码入口深入分析完整业务流程，生成详细的业务流程文档，便于团队理解和维护代码。

## 关键规则
- **必须生成分析文档保存到项目的docs目录下**
- **必须使用sequential-thinking辅助分析**
- **必须深入方法内部逻辑，因此你可能需要检索代码**
- **建议使用sequential-thinking辅助检索代码**
### 1. 聚焦业务核心逻辑
- 忽略日志打印、参数基础校验等次要逻辑
- 忽略异常处理中的技术细节，只关注业务异常处理逻辑
- 忽略与业务无关的工具方法调用（如字符串处理、集合操作等）
- 聚焦业务状态转换、流程分支、核心计算等关键逻辑
### 2. 深入方法调用链
- 追踪每个关键方法的内部实现，不仅停留在方法调用层面
- 对调用链上的每个重要方法都分析其内部业务逻辑
- 对于外部依赖的服务（如RPC调用），说明其功能和业务意义
- 深入分析每个关键业务分支的条件和处理逻辑
### 3. 结合已有文档
- 优先使用已有文档中的描述，避免重复分析
- 如果已有文档对某个方法有详细描述，直接引用该内容
- "站在巨人的肩膀上"，基于已有文档进行补充和完善
- 对已有文档与代码实现不一致的地方进行标注
### 4. 文档输出规范
- 分析结果保存到 `/docs` 目录下，使用 Markdown 格式
- 文档命名格式：`业务名称-流程分析.md`（如：`订单创建-流程分析.md`）
- 文档需包含方法调用树，清晰展示调用层级关系
- 使用分步业务流程描述完整处理过程
### 5. 框架框架运行机制
框架运行机制见同目录下：框架运行机制.md

## 文档结构模板
```markdown
# 业务名称流程分析
## 功能概述
[简要描述该业务功能的目的和作用]
## 入口方法
`com.example.Class.method`
## 方法调用树
入口方法
├─ 一级调用方法1
│  ├─ 二级调用方法1.1
│  ├─ 二级调用方法1.2
├─ 一级调用方法2
   ├─ 二级调用方法2.1
   └─ 二级调用方法2.2
      └─ 三级调用方法
## 详细业务流程
1. [步骤1：业务逻辑描述]
2. [步骤2：业务逻辑描述]
   - [子步骤2.1：详细逻辑]
   - [子步骤2.2：详细逻辑]
3. [步骤3：业务逻辑描述]
## 关键业务规则
- [规则1：描述业务规则及其条件]
- [规则2：描述业务规则及其条件]
## 数据流转
- 输入：[描述方法输入及其业务含义]
- 处理：[描述关键数据处理和转换]
- 输出：[描述方法输出及其业务含义]
## 扩展点/分支逻辑
- [分支1：触发条件及处理逻辑]
- [分支2：触发条件及处理逻辑]
## 外部依赖
- 标注对外部系统的依赖
## 注意事项
- [列出实现中需要特别注意的点]
```
## 系统交互图
- 如果业务流程包含多个系统模块，请使用PlantUML画出时序图
## 代码分析技巧
### 步骤1：明确业务入口
- 确定代码分析的起点（通常是Controller、Facade或Service层的公开方法）
- 了解该方法的调用场景和业务背景
### 步骤2：构建方法调用树
- 从入口方法开始，追踪所有重要的方法调用
- 使用缩进表示调用层级，清晰展示调用关系
- 忽略非核心方法调用（如日志、参数校验等）
### 步骤3：分析业务流程
- 按照代码执行顺序分析业务处理步骤
- 重点关注业务状态转换和分支逻辑
- 提取关键业务规则和数据处理逻辑
### 步骤4：整理业务规则
- 总结条件判断中隐含的业务规则
- 分析不同场景下的处理差异
- 提炼业务逻辑的核心决策点
### 步骤5：描述核心领域模型流转
- 分析核心领域模型的来源、处理和去向
- 说明数据模型转换和业务含义
- 关注核心业务实体的状态变化

## 梳理要求
1. **完整性**：覆盖所有核心业务逻辑和分支，尤其要梳理完整所有核心领域模型
2. **层次性**：清晰展示处理流程的层次结构
3. **业务性**：以业务视角描述，而非技术实现细节
4. **精确性**：准确反映代码的实际处理逻辑
5. **可理解性**：业务人员也能理解的表述方式
6. **实用性**：帮助读者快速理解业务流程和规则 