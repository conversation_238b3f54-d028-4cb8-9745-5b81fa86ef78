# 模块编排框架运行机制

## 框架概述
模块编排框架是一个基于Fetcher-Builder架构模式的模块化构建系统，通过并行数据获取和模块构建，为商品详情页提供高性能、高扩展性的模块化服务。

## 核心架构

### Fetcher-Builder架构
```
请求 → 框架运行器 → Fetcher层(数据获取) → Builder层(模块构建) → 响应
```

### 执行流程
1. **依赖解析**：
   - 解析Builder对Fetcher的依赖关系（`dependentFetchers`）
   - 解析Fetcher间的依赖关系（`previousLayerDependencies`）
   - 构建DAG（有向无环图）确定执行顺序
2. **Fetcher层执行**：
   - 从启动Fetcher开始，按依赖关系顺序执行
   - 同一层级的Fetcher并行执行
   - 每个Fetcher完成后通知依赖它的组件
3. **Builder层执行**：
   - 等待其声明的所有依赖Fetcher完成
   - 无依赖冲突的Builder并行执行
   - 通过`getDependencyResult()`获取Fetcher结果
4. **结果聚合**：将所有模块构建结果聚合成最终响应

## Fetcher运行机制

### Fetcher类型
- **启动Fetcher**：标记为 `isStartFetcher = true`，作为整个流程的起点
- **普通Fetcher**：负责获取特定业务数据，可以依赖其他Fetcher

### 注解配置
```java
@Fetcher(
    isStartFetcher = true,                    // 是否为启动Fetcher
    previousLayerDependencies = {Fetcher1.class, Fetcher2.class}, // 前置依赖
    timeout = 3000                           // 超时时间(毫秒)
)
```

### 异步执行机制
- 所有Fetcher返回 `CompletableFuture<T>` 实现异步执行
- 支持链式调用和异常处理
- 自动处理超时和重试机制 异步执行机制

## Builder运行机制

### Builder类型说明
- **BaseBuilder**：通用模块构建器，当模块比较通用时使用，直接实现业务逻辑
- **BaseBuilderFactory**：工厂模块构建器，当模块在不同类目下存在个性化时使用，通过`selectVariableBuilder`选择具体的`BaseVariableBuilder`实现类
- **BaseVariableBuilder**：可变模块构建器，作为具体的业务实现类，由`BaseBuilderFactory`选择调用

### 实际运行机制
- 框架只识别`BaseBuilder`和`BaseBuilderFactory`两种类型
- **BaseBuilder**：直接执行 `doBuild()` 方法的业务逻辑
- **BaseBuilderFactory**：先执行`selectVariableBuilder`方法选择具体的`BaseVariableBuilder`实现类，再执行选中实现类的`doBuild()`方法

### 分类方式
Builder 按照注解中 `@Builder` 的 `moduleKey` 进行分类
- `BaseBuilder` 每个都有独立的 `moduleKey`
- `BaseBuilderFactory` 和 `BaseVariableBuilder` 中 `moduleKey` 相同的，属于同一组

### 注解配置
```java
@Builder(
    moduleKey = ModuleKeyConstants.REMINDER_INFO,        // 模块标识键
    builderType = BuilderTypeEnum.BUILDER_FACTORY,      // 构建器类型
    startFetcher = CommonModuleStarter.class,           // 启动Fetcher
    dependentFetchers = {Fetcher1.class, Fetcher2.class} // 依赖的Fetcher列表
)
```

### BuilderType枚举
- **ABSTRACT_BUILDER**：抽象构建器，对应BaseBuilder
- **BUILDER_FACTORY**：工厂构建器，对应BaseBuilderFactory
- **VARIABLE_BUILDER**：可变构建器，对应BaseVariableBuilder

### 工厂模式详解

#### 工厂选择机制
每个 `BaseBuilderFactory` 通过 `selectVariableBuilder()` 方法根据业务规则选择对应的 `BaseVariableBuilder` 实现类：

```java
// 工厂类示例
@Builder(
    builderType = BuilderTypeEnum.BUILDER_FACTORY,
    moduleKey = ModuleKeyConstants.REMINDER_INFO,
    startFetcher = CommonModuleStarter.class
)
public class ReminderInfoAbstractFactory extends BaseBuilderFactory<ProductDetailReminderVO> {

    @Override
    protected Class<? extends AbstractReminderInfoBuilder> selectVariableBuilder() {
        // 根据商品类型选择
        if (Objects.equals(request.getProductTypeEnum(), ProductTypeEnum.RESERVE)) {
            return ReserveReminderInfoBuilder.class;
        }

        // 根据商品类目选择
        if (identifyMassageDeal()) {
            return selectMassageReminderBuilder(metaVersionEnum);
        }

        if (identifyBathDeal()) {
            return selectBathReminderBuilder(metaVersionEnum);
        }

        return DefaultReminderInfoBuilder.class;
    }
}

// 对应的实现类
@Builder(
    builderType = BuilderTypeEnum.VARIABLE_BUILDER,
    moduleKey = ModuleKeyConstants.REMINDER_INFO, // 与工厂类相同
    startFetcher = CommonModuleStarter.class
)
public class ReserveReminderInfoBuilder extends AbstractReminderInfoBuilder {
    @Override
    public ProductDetailReminderVO doBuild() {
        // 构建预订商品的须知条领域模型
        // ProductDetailReminderVO 是须知条的核心领域模型
        ProductDetailReminderVO reminderVO = new ProductDetailReminderVO();

        // 组装预订相关的须知内容
        List<GuaranteeInstructionsContentVO> contents = buildReserveContents();
        reminderVO.setContents(contents);
        reminderVO.setTitle("预订须知");

        return reminderVO; // 返回完整的须知条领域模型
    }
}
```

## 依赖管理

### 依赖关系定义
- **Fetcher间依赖**：通过 `previousLayerDependencies` 定义前置Fetcher依赖
- **Builder对Fetcher的依赖**：通过 `dependentFetchers` 定义Builder依赖的Fetcher列表
- **启动依赖**：通过 `startFetcher` 定义启动Fetcher

### 依赖方向说明
```
Fetcher层：Fetcher A → Fetcher B → Fetcher C (通过previousLayerDependencies定义)
Builder层：Builder → Fetcher A, Fetcher B, Fetcher C (通过dependentFetchers定义)
```

Builder在注解中明确声明它依赖哪些Fetcher：
```java
@Builder(
    moduleKey = ModuleKeyConstants.TITLE,
    dependentFetchers = {
        ProductBaseInfoFetcher.class,    // 依赖商品基础信息
        ProductCategoryFetcher.class,    // 依赖商品类目信息
        SkuAttrFetcher.class,           // 依赖SKU属性信息
        ProductAttrFetcher.class        // 依赖商品属性信息
    }
)
```

### 依赖解析
框架通过依赖关系构建DAG，确保：
- Fetcher按照 `previousLayerDependencies` 定义的顺序执行
- Builder等待其 `dependentFetchers` 中声明的所有Fetcher完成后才执行
- 无依赖关系的组件并行执行
- 避免循环依赖导致的死锁

### 数据传递
- Builder通过 `getDependencyResult(FetcherClass.class)` 获取其依赖的Fetcher结果
- 框架确保Builder只能获取其在 `dependentFetchers` 中声明的Fetcher结果
- 框架自动处理数据传递和类型转换
- 支持空值处理和异常容错

### 依赖关系示例
以标题模块Builder为例：

```java
@Builder(
    moduleKey = ModuleKeyConstants.TITLE,
    startFetcher = CommonModuleStarter.class,
    dependentFetchers = {
        ProductBaseInfoFetcher.class,    // 声明依赖商品基础信息
        ProductCategoryFetcher.class,    // 声明依赖商品类目信息
        SkuAttrFetcher.class,           // 声明依赖SKU属性信息
        ProductAttrFetcher.class        // 声明依赖商品属性信息
    }
)
public class TitleModuleBuilder extends BaseBuilder<TitleModuleVO> {

    @Override
    public TitleModuleVO doBuild() {
        // 获取依赖的Fetcher结果
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductCategory category = getDependencyResult(ProductCategoryFetcher.class);
        SkuAttr skuAttr = getDependencyResult(SkuAttrFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);

        // 基于获取的数据构建标题模块的领域模型
        // TitleModuleVO 是核心的领域模型，包含前端展示所需的所有标题信息
        TitleModuleVO titleModule = new TitleModuleVO();
        titleModule.setTitle(baseInfo.getTitle());
        titleModule.setPrice(baseInfo.getPrice());
        titleModule.setOriginalPrice(baseInfo.getOriginalPrice());
        titleModule.setCategoryName(category.getCategoryName());
        // ... 其他业务字段的组装

        return titleModule; // 返回完整的领域模型
    }
}
```

执行顺序：
1. **CommonModuleStarter** 启动（isStartFetcher=true）
2. **ProductBaseInfoFetcher** 等依赖的Fetcher按依赖关系执行
3. **TitleModuleBuilder** 等待所有依赖的Fetcher完成后执行


## 核心领域模型

### 领域模型定义
**重要说明**：Builder的 `doBuild()` 方法返回的对象是核心的领域模型，是业务开发中需要重点关注的部分。

- **领域模型定义**：返回对象代表了特定业务模块的完整数据结构
- **业务语义**：每个返回对象都有明确的业务含义和使用场景
- **前端展示**：这些对象直接用于前端页面的模块展示
- **业务扩展**：新增业务功能时，主要通过扩展这些领域模型实现

> 示例：领域模型及 ModuleKey 对应关系

| 模块名称 | ModuleKey | 返回领域模型 | 功能描述 |
|---------|-----------|-------------|----------|
| 通用数据模块 | `module_common_data` | `CommonDataVO` | 用于打点统计的通用数据 |
| 标题模块 | `module_detail_title` | `TitleModuleVO` | 商品标题和基础信息展示 |
| 标签模块 | `module_detail_deal_tags` | `TagsModuleVO` | 排行标签、评价标签等 |
| 适用门店模块 | `module_detail_deal_available_shop` | `AvailableShopVO` | 商品适用门店信息 |


### 领域模型设计原则

#### 1. 业务导向
- **业务完整性**: 每个VO包含该业务模块的完整信息
- **业务语义**: 字段命名和结构设计体现业务含义
- **业务扩展**: 支持业务功能的灵活扩展

#### 2. 前端友好
- **直接可用**: 前端可以直接使用，无需额外转换
- **结构清晰**: 嵌套结构合理，便于前端解析
- **性能考虑**: 避免冗余数据，控制数据量大小

#### 3. 版本兼容
- **向后兼容**: 新增字段不影响老版本客户端
- **渐进升级**: 支持新老版本并存
- **优雅降级**: 异常情况下提供默认值

## 最佳实践

### Builder开发建议
1. **专注业务逻辑**: Builder应专注于业务数据的组装和处理
2. **合理使用依赖**: 只声明真正需要的Fetcher依赖
3. **异常处理**: 妥善处理依赖数据为空的情况
4. **性能考虑**: 避免在Builder中进行耗时操作

### 领域模型设计建议
1. **字段完整性**: 确保包含前端展示所需的所有字段
2. **数据类型**: 使用合适的数据类型，避免类型转换
3. **默认值**: 为可选字段提供合理的默认值
4. **文档注释**: 为字段添加清晰的业务含义注释

### 工厂选择逻辑建议
1. **规则清晰**: 选择逻辑应该清晰明确，易于理解
2. **优先级明确**: 多个条件匹配时，优先级应该明确
3. **兜底策略**: 提供默认的兜底Builder
4. **可测试性**: 选择逻辑应该易于单元测试