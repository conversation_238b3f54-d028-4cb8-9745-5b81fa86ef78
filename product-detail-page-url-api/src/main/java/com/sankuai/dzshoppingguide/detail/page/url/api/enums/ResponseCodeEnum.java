package com.sankuai.dzshoppingguide.detail.page.url.api.enums;

import lombok.Getter;

/**
 * @Author: guang<PERSON>ji<PERSON>
 * @Date: 2025/1/23 11:28
 */
@Getter
public enum ResponseCodeEnum {

    SUCCESS(200, "成功"),
    FAILURE(500, "失败");

    private final int code;

    private final String desc;

    ResponseCodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ResponseCodeEnum fromCode(int code) {
        for (ResponseCodeEnum value : ResponseCodeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (ResponseCodeEnum value : ResponseCodeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

}