package com.sankuai.dzshoppingguide.detail.page.url.api.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.enums.ModuleResponseCodeEnum;
import com.sankuai.dzshoppingguide.detail.page.url.api.enums.ResponseCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 14:44
 */
@TypeDoc(description = "跳链查询结果体")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProductDetailPageUrlQueryResponse implements Serializable {

    @FieldDoc(description = "状态码", type = ModuleResponseCodeEnum.class,
            requiredness = Requiredness.REQUIRED)
    private int code;

    @FieldDoc(description = "失败原因", rule = "code != ResponseCodeEnum.SUCCESS.getCode()才有效",
            requiredness = Requiredness.OPTIONAL)
    private String msg;

    @FieldDoc(description = "商品详情页跳链", rule = "code != ResponseCodeEnum.SUCCESS.getCode()才有效",
            requiredness = Requiredness.OPTIONAL)
    private String url;

    @FieldDoc(description = "被过滤的业务参数", rule = "跳链服务会过滤非法业务参数，对应request中的customParams",
            requiredness = Requiredness.OPTIONAL)
    private Map<String, String> filteredCustomParams;

    public boolean isSuccess() {
        return code == ResponseCodeEnum.SUCCESS.getCode();
    }

    public static ProductDetailPageUrlQueryResponse succeed(String url) {
        return ProductDetailPageUrlQueryResponse.builder()
                .code(ResponseCodeEnum.SUCCESS.getCode())
                .url(url)
                .build();
    }

    public static ProductDetailPageUrlQueryResponse succeed(String url, Map<String, String> filteredCustomParams) {
        return ProductDetailPageUrlQueryResponse.builder()
                .code(ResponseCodeEnum.SUCCESS.getCode())
                .url(url)
                .filteredCustomParams(filteredCustomParams)
                .build();
    }

    public static ProductDetailPageUrlQueryResponse fail(String errorMsg) {
        return ProductDetailPageUrlQueryResponse.builder()
                .code(ResponseCodeEnum.FAILURE.getCode())
                .msg(errorMsg)
                .build();
    }

}
