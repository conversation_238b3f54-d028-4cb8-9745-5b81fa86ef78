<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzshoppingguide.detail</groupId>
        <artifactId>product-detail-common-module</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>product-detail-page-url-sdk</artifactId>
    <version>${product-detail-page-url.version}</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.dzshoppingguide.detail</groupId>
            <artifactId>product-detail-page-url-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dz</groupId>
            <artifactId>product-detail-gateway-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.cat</groupId>
            <artifactId>cat-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
        </dependency>
    </dependencies>

</project>