package com.sankuai.dzshoppingguide.detail.page.url.sdk.starter;

import com.sankuai.dzshoppingguide.detail.page.url.sdk.adaptor.ProductDetailPageDefaultUrlAdaptor;
import com.sankuai.dzshoppingguide.detail.page.url.sdk.adaptor.ProductDetailPageUrlQueryAdaptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: guangyujie
 * @Date: 2025/5/28 16:15
 */
@Configuration
public class ProductDetailPageUrlBeanConfiguration {

    @Bean
    public ProductDetailPageDefaultUrlAdaptor productDetailPageDefaultUrlAdaptor() {
        return new ProductDetailPageDefaultUrlAdaptor();
    }

    @Bean
    public ProductDetailPageUrlQueryAdaptor productDetailPageUrlQueryAdaptor() {
        return new ProductDetailPageUrlQueryAdaptor();
    }

}
