package com.sankuai.dzshoppingguide.detail.page.url.sdk.adaptor;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.pigeon.remoting.ServiceFactory;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.invoker.config.InvokerConfig;
import com.sankuai.dzshoppingguide.detail.page.url.api.request.ProductDetailPageUrlQueryRequest;
import com.sankuai.dzshoppingguide.detail.page.url.api.response.ProductDetailPageUrlQueryResponse;
import com.sankuai.dzshoppingguide.detail.page.url.api.service.ProductDetailPageUrlQueryService;
import com.sankuai.dzshoppingguide.detail.page.url.sdk.exception.ProductDetailPageUrlException;
import com.sankuai.dzshoppingguide.detail.page.url.sdk.exception.ProductDetailPageUrlFatalException;
import com.sankuai.dzshoppingguide.detail.page.url.sdk.utils.PigeonCallbackUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/19 14:52
 */
@Slf4j
public class ProductDetailPageUrlQueryAdaptor implements InitializingBean {

    private ProductDetailPageUrlQueryService service;

    @Resource
    private ProductDetailPageDefaultUrlAdaptor defaultUrlAdaptor;

    /**
     * 异步返回商品详情页跳链，如果调用失败会走兜底，会告警打点，麻烦关注问题
     */
    public CompletableFuture<String> getUrl(final ProductDetailPageUrlQueryRequest request) {
        request.checkParams();
        final long startTime = System.currentTimeMillis();
        try {
            CompletableFuture<String> future = PigeonCallbackUtils.setPigeonCallback(
                    ProductDetailPageUrlQueryResponse.class
            ).thenApply(response -> {
                if (response == null || !response.isSuccess() || StringUtils.isBlank(response.getUrl())) {
                    //发生业务异常
                    ProductDetailPageUrlException exception = new ProductDetailPageUrlException("查询到综商品详情页失败,发生业务异常");
                    failureDot(exception, startTime);
                    log.error("查询到综商品详情页失败,发生业务异常,request:{}", JSON.toJSONString(request), exception);
                    return defaultUrlAdaptor.getDefaultUrl(request);
                } else {
                    successDot(startTime);
                    return response.getUrl();
                }
            }).exceptionally(throwable -> {
                //发生RPC调用异常，可能是超时
                failureDot(throwable, startTime);
                log.error("查询到综商品详情页失败，发生RPC调用异常，request:{}", JSON.toJSONString(request), new ProductDetailPageUrlException(throwable));
                return defaultUrlAdaptor.getDefaultUrl(request);
            });
            service.query(request);
            return future;
        } catch (Throwable throwable) {
            failureDot(throwable, startTime);
            log.error("查询到综商品详情页失败,request:{}", JSON.toJSONString(request), new ProductDetailPageUrlException(throwable));
            return CompletableFuture.completedFuture(defaultUrlAdaptor.getDefaultUrl(request));
        }
    }

    private void successDot(long startTime) {
        Transaction transaction = Cat.newTransactionWithDuration("ProductDetailPageUrl", "adaptor", System.currentTimeMillis() - startTime);
        transaction.setStatus(Transaction.SUCCESS);
        transaction.complete();
    }

    private void failureDot(Throwable throwable, long startTime) {
        Transaction transaction = Cat.newTransactionWithDuration("ProductDetailPageUrl", "adaptor", System.currentTimeMillis() - startTime);
        transaction.setStatus(throwable);
        transaction.complete();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        InvokerConfig<ProductDetailPageUrlQueryService> invokerConfig = new InvokerConfig<>(
                "com.sankuai.dzshoppingguide.detail.page.url.api.service.ProductDetailPageUrlQueryService",
                ProductDetailPageUrlQueryService.class
        );
        invokerConfig.setTimeout(1000);
        invokerConfig.setCallType(CallMethod.CALLBACK.getName());
        ProductDetailPageUrlQueryService service = ServiceFactory.getService(invokerConfig);
        if (service == null) {
            throw new ProductDetailPageUrlFatalException("获取商品详情页跳链服务失败");
        }
        this.service = service;
    }
}
